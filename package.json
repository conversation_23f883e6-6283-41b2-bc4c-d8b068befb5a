{"name": "xczx_mgr", "private": true, "secret": "0123456789asdfgh", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview"}, "dependencies": {"@coze/api": "^1.2.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@kangc/v-md-editor": "^2.3.18", "@types/highlight.js": "^10.1.0", "@types/node": "^22.14.1", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.18", "axios": "^1.8.4", "clipboard": "^2.0.11", "echarts": "^5.6.0", "element-plus": "^2.9.8", "flv.js": "^1.6.2", "highlight.js": "^11.11.1", "js-cookie": "^3.0.5", "mavon-editor": "^3.0.2", "node-sass": "^9.0.0", "path": "^0.12.7", "path-to-regexp": "^8.2.0", "pinia": "^2.3.1", "pinia-plugin-persist": "^1.0.0", "qs": "^6.14.0", "sass-loader": "^16.0.5", "terser": "^5.39.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue3-encryption-plugin": "^1.0.8", "vue3-puzzle-vcode": "^1.1.7", "vuedraggable": "^4.1.0", "vuex": "^4.1.0"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/prismjs": "^1.26.5", "@types/webpack-env": "^1.18.8", "@vitejs/plugin-vue": "^5.2.3", "js-cookies": "^1.0.4", "lib-flexible": "^0.3.2", "sass": "^1.87.0", "typescript": "^5.8.3", "vite": "6.3.2", "vite-plugin-style-import": "^2.0.0", "vue-tsc": "^2.2.8"}}