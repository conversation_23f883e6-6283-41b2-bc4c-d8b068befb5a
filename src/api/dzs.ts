import axios from "@/utils/axios"
import oaxios from "axios"
import qs from "qs"
export async function getVideoList(params: any) {
    let res: any = axios.post("mgr_dzs/getDzsList", params)
    return res
  }
  
  // 插入视频列表
  export async function insertVideoList(params: any) {
  let res: any = axios.post("mgr_dzs/insertDzsList", params)
  return res
  }
  
  // 获取基础课程
  export async function getCourseList() {
    let res: any = axios.post("mgr_course/infolist")
    return res
  }

  export async function uploadDzs(params: any) {
    let res: any = axios.post("/file/upload_to_drive",params)
    return res
  }

  export async function getContentFromCoze(params: any) {
    let res: any = axios.post("mgr_dzs/getContentFromCoze",params)
    return res
  }

  export async function checkContentFromCoze(params: any) {
    let res: any = axios.post("mgr_dzs/checkContentFromCoze",params)
    return res
  }

  export async function UpdateDzs(params: any) {
    let res: any = axios.post("/mgr_dzs/UpdateDzs",params)
    return res
  }

  export async function CourseDzsInfo(params: any) {
    return await axios.post('/mgr_dzs/course_dzs_info', params)
  }

  export async function CourseDzsInfoByChapter(params: any) {
    return await axios.post('/mgr_dzs/course_dzs_info_chapter', params)
  }

  export async function UpdateDzsContent(params: any) {
    let res: any = axios.post("/mgr_dzs/UpdateDzsContent",params)
    return res
  }

  export async function UpdateDzsMenu(params: any) {
    let res: any = axios.post("/mgr_dzs/UpdateDzsMenu",params)
    return res
  }  
  
  export async function UpdateDzsZsd(params: any) {
    let res: any = axios.post("/mgr_dzs/UpdateDzsZsd",params)
    return res
  }
    
  export async function GetJcList(params: any) {
    let res: any = axios.post("/mgr_dzs/getJcList",params)
    return res
  }

  export async function EditJc(params: any) {
    let res: any = axios.post("/mgr_dzs/editJc",params)
    return res
  }

  export async function addJc(params: any) {
    let res: any = axios.post("/mgr_dzs/addJc",params)
    return res
  }

  export async function getJcDatas(params: any) {
    let res: any = axios.post("/mgr_dzs/getJcDatas",params)
    return res
  }

 // 考试大纲
  export async function getSelectCourseJcList() {
    let res: any = axios.post("mgr_dzs/getSelectCourseJcList")
    return res
  }
  export async function getCourseKsdgList(params: any) {
    let res: any = axios.post("mgr_dzs/getCourseKsdgList", params)
    return res
  }
  export async function setCourseKsdgList(params: any) {
  let res: any = axios.post("mgr_dzs/setCourseKsdgList", params)
  return res
  }
  export async function getContentKsdgFromCoze(params: any) {
    let res: any = axios.post("mgr_dzs/getContentKsdgFromCoze",params)
    return res
  }
  export async function checkContentKsdgFromCoze(params: any) {
    let res: any = axios.post("mgr_dzs/checkContentKsdgFromCoze",params)
    return res
  }
  export async function updateCourseKsdg(params: any) {
    let res: any = axios.post("mgr_dzs/updateCourseKsdg",params)
    return res
  }

export async function getCourseKnowledgeNodePage(data: object) {
  return await axios.post('mgr_dzs/getCourseKnowledgeNodePage?' + qs.stringify(data))
}
