import axios from "@/utils/axios"
import qs from "qs"
export async function userLogin(username: string, password: string) {
  const parm = {
    app_name: 'kj_zj_mgr',
    username: username,
    password: password
  }
  //debugger
  console.log(parm)
  const res: any = await axios.post('/mgr_auth/login', parm)
  // var res = Axios.post('https://apps.swufe-online.com/qsapi/api/Auth/QsLoginCheck', parm)
  return res
}
export async function getInfo() {
  const res = await axios.post('/mgr_auth/getUserInfo')
  return res
}
// 获取菜单
export function getAuthMenu() {
  var res: any = axios.post("/mgr_auth/getMenuInfo")
  return res
}

export function getSite() {
  var res: any = axios.post("user/GetSite")
  return res
}

export function loginAuto(data:string) {
  var res: any = axios.post("mgr_auth/get_loginAuto",{"utoken":data})
  return res
}

export function UpdateMenuLog(data:any) {
  var res: any = axios.post("mgr_auth/UpdateMenuLog?"+qs.stringify(data))
  return res
}

export function getIndexData() {
  var res: any = axios.post("mgr_auth/getIndexData")
  return res
}