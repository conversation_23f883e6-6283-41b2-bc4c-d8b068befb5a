import axios from "@/utils/axios"
import qs from "qs"

//获取用户列表  
export async function GetQuestionBankPage(data: object) {
  return await axios.post('mgr_question/getQuestionBankPage?' + qs.stringify(data))
}
// 用户类型
export async function GetQuestionsJobPage(data: object) {
  return await axios.post('mgr_question/getQuestionsJobPage?' + qs.stringify(data))
}
export function GetBaseKcData(data: any) {
  var res: any = axios.post("mgr_question/getBaseKcData?" + qs.stringify(data))
  return res
}
export function GetJobVerData(data: any) {
  var res: any = axios.post("mgr_question/GetJobVerData?" + qs.stringify(data))
  return res
}
export function GetJobIdQuestions(data: any) {
  var res: any = axios.post("mgr_question/getJobIdQuestions",data)
  return res
}
export function GetJobSource(data: any) {
  var res: any = axios.post("mgr_question/getJobSource?" + qs.stringify(data))
  return res
}
export async function SaveJobbankSourceData(data: object) {
  return await axios.post('mgr_question/SaveJobbankSourceData',data)
}
export function getTempJobbank(data: any) {
  var res: any = axios.post("mgr_question/getTempJobbank")
  return res
}
export function SaveTempJobbank(data: any) {
  var res: any = axios.post("mgr_question/SaveTempJobbank",data)
  return res
}
export function SaveJobBankIdQuestions(data: any) {
  var res: any = axios.post("mgr_question/saveJobBankIdQuestions",data)
  return res
}
export function getTempJobbankCount(data: any) {
  var res: any = axios.post("mgr_question/getTempJobbankCount",data)
  return res
}
export function getJobQuestionsPreview(data: any) {
  var res: any = axios.post("mgr_question/getJobQuestionsPreview",data)
  return res
}