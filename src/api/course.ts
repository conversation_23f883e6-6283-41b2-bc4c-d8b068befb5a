import axios from "@/utils/axios"
import { h } from "vue"

export async function CourseInfo() {
    let res: any = axios.post("mgr_course/info")
    return res
}

export async function UpdCourseInfo(id: number, is_online: number, is_pub: number,video_ids: number[]) {
    let res: any = axios.post("mgr_course/info_upd", {id: id, is_online: is_online, is_pub: is_pub,video_ids: video_ids})
    return res
}

export async function AddCourseInfo(course_base_id: number) {
    let res: any = axios.post("mgr_course/info_add", {course_base_id: course_base_id})
    return res
}

export async function DelCourseInfo(id: number) {
    let res: any = axios.post("mgr_course/info_del", {id: id})
    return res
}
export async function getQuestion(guid: string) {
    let res: any = axios.post("examplan/getQuestion?guid=" + guid)
    return res
}
export async function importJobbank(data: any,d:any) {
  let res: any = axios.post("examplan/importJobbank",data,{headers: {'Content-Type': 'multipart/form-data'}})
  return res
}
