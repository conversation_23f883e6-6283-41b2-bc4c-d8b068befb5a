import axios from "@/utils/axios"
import {h} from "vue"
import qs from "qs"

export async function CourseProgressStat() {
    let res: any = axios.post("mgr_course/progress_stat")
    return res
}

export async function CourseInfo() {
    let res: any = axios.post("mgr_course/info")
    return res
}
export async function CourseInfoZk() {
    let res: any = axios.post("mgr_course/info_zk")
    return res
}

export async function UpdCourseInfo(id: number, is_online: number, is_pub: number, video_ids: number[]) {
    let res: any = axios.post("mgr_course/info_upd", {
        id: id,
        is_online: is_online,
        is_pub: is_pub,
        video_ids: video_ids
    })
    return res
}

export async function AddCourseInfo(course_base_id: number) {
    let res: any = axios.post("mgr_course/info_add", {course_base_id: course_base_id})
    return res
}

export async function DelCourseInfo(id: number) {
    let res: any = axios.post("mgr_course/info_del", {id: id})
    return res
}

export async function getQuestion(guid: string) {
    let res: any = axios.post("examplan/getQuestion?guid=" + guid)
    return res
}

export async function importJobbank(data: any, d: any) {
    let res: any = axios.post("examplan/importJobbank", data, {headers: {'Content-Type': 'multipart/form-data'}})
    return res
}

export function getOlsCourseChapterData(data: any) {
    var res: any = axios.post("mgr_course/getOlsCourseChapterData", data)
    return res
}

export async function SaveOlsCourseChapterData(data: any) {
    const res = await axios.post('mgr_course/SaveOlsCourseChapterData', data)
    return res
}

export async function DelOlsCourseChapterData(data: object) {
    return await axios.post('/mgr_course/DelOlsCourseChapterData?' + qs.stringify(data))

}

export async function getOlsCourseChapterJob(data: object) {
    return await axios.post('/mgr_course/getOlsCourseChapterJob?' + qs.stringify(data))
}

export async function getOlsCourseChapter(data: object) {
    return await axios.post('/mgr_course/getOlsCourseChapter?' + qs.stringify(data))
}

export async function getOlsChapterQuestion(data: object) {
    return await axios.post('/mgr_course/getOlsChapterQuestion?' + qs.stringify(data))
}

export function getSelectOlsCourseList() {
    let res: any = axios.post("mgr_course/getSelectOlsCourseList")
    return res
}

export function saveOlsChapterQuestion(data: any) {
    let res: any = axios.post("mgr_course/saveOlsChapterQuestion", data)
    return res
}

export function getOlsChapterJob(data: any) {
    let res: any = axios.post("mgr_course/getOlsChapterJob?" + qs.stringify(data))
    return res
}


export function getPlateDataForOlsCourseById(data: any) {
    let res: any = axios.post("mgr_course/getPlateDataForOlsCourseById?" + qs.stringify(data))
    return res
}

export function getPlateDataForOlsCourseById_op(data: any) {
    let res: any = axios.post("mgr_course/getPlateDataForOlsCourseById_op?" + qs.stringify(data))
    return res
}


export function saveOlsCoursePlateByPlanid(data: any) {
    let res: any = axios.post("mgr_course/saveOlsCoursePlateByPlanid",data)
    return res
}

export function saveOlsCourse(data: any) {
    let res: any = axios.post("mgr_course/saveOlsCourse",data)
    return res
}
