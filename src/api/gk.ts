import axios from "@/utils/axios"
import qs from "qs"

export function getGkCourse(data: any) {
  var res: any = axios.post("mgr_gk/getGkCourse?" + qs.stringify(data))
  return res
}

export function getCourseBase(data: any) {
  var res: any = axios.post("mgr_gk/getCourseBase")
  return res
}

export function gkSaveCourse(data: any) {
  var res: any = axios.post("mgr_gk/gkSaveCourse",data)
  return res
}

export function deleteGkCourse(data: any) {
  var res: any = axios.post("mgr_gk/deleteGkCourse?"+qs.stringify(data))
  return res
}