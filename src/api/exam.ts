import axios from "@/utils/axios"
import qs from "qs"

export function getKcData(data: any) {
  var res: any = axios.post("examplan/getKcData?" + qs.stringify(data))
  return res
}
export function kcSaveData(data: any) {
  var res: any = axios.post("examplan/kcSaveData" ,data)
  return res
}
export function deleteKcData(data: any) {
  var res: any = axios.post("examplan/deleteKcData?id="+data)
  return res
}
export function getTextBookData(data: any) {
  var res: any = axios.post("examplan/getTextBookData?" + qs.stringify(data))
  return res
}
export function textBookSaveData(data: any) {
  var res: any = axios.post("examplan/textBookSaveData" ,data)
  return res
}
export function deletetextBookData(data: any) {
  var res: any = axios.post("examplan/deletetextBookData?id="+data)
  return res
}

export function getZyData(data: any) {
  var res: any = axios.post("examplan/getZyData?" + qs.stringify(data))
  return res
}
export function zySaveData(data: any) {
  var res: any = axios.post("examplan/zySaveData" ,data)
  return res
}
export function deleteZyData(data: any) {
  var res: any = axios.post("examplan/deleteZyData?id="+data)
  return res
}

export function getPlanData(data: any) {
  var res: any = axios.post("examplan/getPlanData?" + qs.stringify(data))
  return res
}
export function planSaveData(data: any) {
  var res: any = axios.post("examplan/planSaveData" ,data)
  return res
}
export function deletePlanData(data: any) {
  var res: any = axios.post("examplan/deletePlanData?id="+data)
  return res
}

export function getCjPlanData(data: any) {
  var res: any = axios.post("examplan/getCjPlanData?" + qs.stringify(data))
  return res
}
export function cjplanSaveData(data: any) {
  var res: any = axios.post("examplan/cjplanSaveData" ,data)
  return res
}
export function deleteCjPlanData(data: any) {
  var res: any = axios.post("examplan/deleteCjPlanData?id="+data)
  return res
}

export function getPlateData(data: any) {
  var res: any = axios.post("examplan/getPlateData?" + qs.stringify(data))
  return res
}

export function plateSaveData(data: any) {
  var res: any = axios.post("examplan/plateSaveData" ,data)
  return res
}
export function deletePlateData(data: any) {
  var res: any = axios.post("examplan/deletePlateData?id="+data)
  return res
}
export function getPlateDataByPlanid(data: any) {
  var res: any = axios.post("examplan/getPlateDataByPlanid?id="+data)
  return res
}
export function savePlateByPlanid(data: any) {
  var res: any = axios.post("examplan/savePlateByPlanid",data)
  return res
}

export function getPlateDataByCjPlanid(data: any) {
  var res: any = axios.post("examplan/getPlateDataByCjPlanid?id="+data)
  return res
}
export function savePlateByCjPlanid(data: any) {
  var res: any = axios.post("examplan/savePlateByCjPlanid",data)
  return res
}
export function getOnlineCourse(data: any) {
  var res: any = axios.post("examplan/getOnlineCourse?"+qs.stringify(data))
  return res
}

export function getPublicPlanData(data: any) {
  var res: any = axios.post("examplan/getPublicPlanData?" + qs.stringify(data))
  return res
}
export function publicPlanSaveData(data: any) {
  var res: any = axios.post("examplan/publicPlanSaveData" ,data)
  return res
}
export function deletePublicPlanData(data: any) {
  var res: any = axios.post("examplan/deletePublicPlanData?id="+data)
  return res
}