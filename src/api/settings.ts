import axios from "@/utils/axios"
import qs from "qs"


// 获取功能模块数据

export function GetKjZjModulesData(data: any) {
  var res: any = axios.post("mgr_setting/getModuleData?" + qs.stringify(data))
  return res
}
export async function SaveKjZjModuleData(data: any) {
  const res =await axios.post('mgr_setting/updateModuleData', data)
  return res
}
export function DisablePerentNode(data: any) {
  var res: any = axios.post("mgr_setting/DisablePerentNode?" + qs.stringify(data))
  return res
}
export async function DelModuleData(data: object) {
  return await axios.post('/mgr_setting/delModuleData?'+qs.stringify(data))
}

// 角色管理

export async function GetRoleslistData(data: object) {
  return await axios.post('mgr_setting/getRoleData?' + qs.stringify(data))
}
export async function SaveSrmRoleFromData(data: object) {
  return await axios.post('/mgr_setting/updateRoleData',data) 
}
export async function DelSrmRoleData(data: object) {
  return await axios.post('/mgr_setting/delRoleData?'+qs.stringify(data))
}
export async function GetUserBetweenRole(data: object) {
  return await axios.post('/mgr_setting/getUserBetweenRole?'+qs.stringify(data))
}

export async function SetSrmUserOfRole(data: object) {
  return await axios.post('/mgr_setting/setUserOfRole?'+qs.stringify(data))
}

//用户授权
export async function GetRoleUserAuthInfo(data: object) {
  return await axios.post('mgr_setting/getRoleAuthInfo?' + qs.stringify(data))
}
export async function SetRoleUserAuth(data: object) {
  return await axios.post('mgr_setting/setRoleAuth', data)
}
//获取用户列表  
export async function GetUserInfoList(data: object) {
  return await axios.post('mgr_setting/getUserInfoList?' + qs.stringify(data))
}
export async function SaveRoleOfUserData(data: object) {
  return await axios.post('/mgr_setting/setRoleOfUserData?'+qs.stringify(data))
}
export async function SaveUserInfoFromData(data: object) {
  return await axios.post('/mgr_setting/saveUserInfoFromData',data) 
}
export async function DelUserInfoData(data: object) {
  return await axios.post('/mgr_setting/delUserInfoData?'+qs.stringify(data))
}
// 用户类型
export async function GetUserTypeData(data: object) {
  return await axios.post('mgr_setting/getUserTypeData?' + qs.stringify(data))
}
export async function SaveUserTypeData(data: object) {
  return await axios.post('mgr_setting/updateUserTypeData',data)
  // return await axios.post('/mgr_setting/updateRoleData?' + qs.stringify(data))
}