<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    fullscreen
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    :show-close="false"
    destroy-on-close
    class="preview-dialog"
    @close="handleClose"
  >
    <div class="dialog-close" @click="handleClose">
      <el-icon><Close /></el-icon>
    </div>
    
    <div class="preview-container">
      <div class="preview-panel left-panel">
        <div class="panel-header">原始内容</div>
        <div class="panel-content">
          <el-input
            v-model="rawContent"
            type="textarea"
            :rows="10"
            placeholder="请输入知识点内容，多个知识点用逗号分隔"
            @input="handleInput"
            @scroll="handleLeftScroll"
          />
        </div>
      </div>
      
      <div class="preview-panel right-panel">
        <div class="panel-header">预览效果</div>
        <div class="panel-content">
          <div v-if="knowledgePoints.length === 0" class="empty-hint">
            <el-empty description="暂无内容" />
          </div>
          <div v-else class="knowledge-points">
            <div v-for="(point, index) in knowledgePoints" :key="index" class="knowledge-point">
              {{ point }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Close } from '@element-plus/icons-vue';
import { UpdateDzsZsd } from '@/api/dzs';

interface Props {
  modelValue: boolean;
  id: number | string;
  title?: string;
  content: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '知识点预览',
  content: ''
});

const emit = defineEmits<{
  (e: 'update:content', value: string): void;
  (e: 'update:modelValue', value: boolean): void;
  (e: 'close'): void;
  (e: 'save', value: string): void;
}>();

const dialogVisible = ref(props.modelValue);
const rawContent = ref('');
const originalContent = ref('');

// 同步滚动
const handleLeftScroll = () => {
  let leftScroll = document.querySelector('.el-textarea__inner')
  let rightScroll = document.querySelector('.markdown-preview')
  if (rightScroll && leftScroll) {
    rightScroll.scrollTop = leftScroll.scrollTop;
  }
};

// 计算属性：将逗号分隔的字符串转换为知识点数组
const knowledgePoints = computed(() => {
  if (!rawContent.value.trim()) return [];
  return rawContent.value
    .split(',')
    .map(point => point.trim())
    .filter(point => point);
});

// 检查内容是否有更改
const hasChanges = () => {
  return rawContent.value !== originalContent.value;
};

// 处理输入，添加防抖
let debounceTimer: ReturnType<typeof setTimeout> | null = null;
const handleInput = () => {
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }
  debounceTimer = setTimeout(() => {
    // 可以在这里添加自动保存逻辑
  }, 500);
};

// 处理关闭
const handleClose = async () => {
  // 检查是否有未保存的更改
  if (hasChanges()) {
    try {
      await ElMessageBox.confirm('内容已修改，是否保存更改？', '提示', {
        confirmButtonText: '保存',
        cancelButtonText: '不保存',
        type: 'warning',
        distinguishCancelAndClose: true
      });
      
      // 用户点击了保存
      try {
        const response = await UpdateDzsZsd({
          id: props.id,
          zsd: knowledgePoints.value
        });
        
        if (response.code === 200) {
          // 更新本地原始内容
          originalContent.value = rawContent.value;
          // 通知父组件更新
          emit('update:content', rawContent.value);
          emit('save', rawContent.value);
          ElMessage.success('保存成功');
        } else {
          ElMessage.error(response.msg || '保存失败');
          return; // 保存失败时不关闭对话框
        }
      } catch (e) {
        console.error('保存知识点失败:', e);
        ElMessage.error('保存失败，请检查数据格式');
        return; // 保存失败时不关闭对话框
      }
    } catch (error) {
      // 用户点击了取消或关闭
      if (error === 'cancel') {
        // 不保存更改，恢复原始内容
        rawContent.value = originalContent.value;
      } else if (error === 'close') {
        // 用户点击了关闭按钮，取消关闭操作
        return;
      }
    }
  }
  
  // 关闭对话框
  emit('update:modelValue', false);
  emit('close');
  
  // 清除防抖定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer);
    debounceTimer = null;
  }
};

// 初始化内容
const initContent = () => {
  rawContent.value = props.content || '';
  originalContent.value = props.content || '';
};

// 监听props变化
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val;
  if (val) {
    initContent();
  }
}, { immediate: true });

watch(dialogVisible, (val) => {
  if (val) {
    initContent();
  }
  emit('update:modelValue', val);
});

// 监听content变化
watch(() => props.content, (val) => {
  if (val !== rawContent.value) {
    initContent();
  }
}, { immediate: true });

// 组件挂载时初始化内容
onMounted(() => {
  initContent();
});
</script>

<style lang="scss" scoped>
.dialog-close {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2001;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 20px;
  transition: all 0.3s;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
    transform: scale(1.1);
  }
  
  .el-icon {
    font-size: 20px;
  }
}

.preview-dialog {
  :deep(.el-dialog) {
    margin: 0 !important;
    max-height: 100%;
    max-width: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__body) {
    padding: 0 !important;
    margin: 0;
    height: calc(100vh - 55px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .preview-container {
    display: flex;
    height: calc(100vh - 100px);
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
  }

  .left-panel {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #dcdfe6;
  }

  .right-panel {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }

  .panel-header {
    padding: 10px 15px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #dcdfe6;
    font-weight: bold;
    color: #606266;
  }

  .panel-content {
    flex: 1;
    overflow: auto;
    padding: 15px;
    display: flex;
    flex-direction: column;

    .el-textarea {
      flex: 1;
      min-height: 0;
      
      :deep(.el-textarea__inner) {
        height: 100% !important;
        min-height: 100% !important;
        resize: none;
        font-family: 'Courier New', monospace;
        line-height: 1.5;
        border: none;
        box-shadow: none;
      }
    }
  }

  .empty-hint {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #909399;
  }

  .knowledge-points {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .knowledge-point {
      padding: 8px 12px;
      background-color: #f5f7fa;
      border-radius: 4px;
      border-left: 3px solid #409eff;
      
      &:hover {
        background-color: #ecf5ff;
      }
    }
  }
}
</style>
