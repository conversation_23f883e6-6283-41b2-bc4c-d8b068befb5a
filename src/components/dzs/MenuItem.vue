<template>
  <div>
    <div :class="['menu-title', 'level-' + level, item.type]">
      <v-md-preview :text="item.title" class="md-preview" />
    </div>
    <div v-if="item.children && item.children.length > 0" class="menu-children">
      <menu-item 
        v-for="(child, index) in item.children" 
        :key="index"
        :item="child" 
        :level="level + 1"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'MenuItem',
  props: {
    item: {
      type: Object,
      required: true
    },
    level: {
      type: Number,
      default: 0
    }
  },
  components: {
    MenuItem: () => import('./MenuItem.vue')
  }
});
</script>

<style scoped>
.menu-title {
  padding: 6px 12px;
  margin: 2px 0;
  border-radius: 4px;
  transition: background-color 0.3s;
  cursor: default;

  &:hover {
    background-color: #f5f7fa;
  }

  &.h1 {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
    margin-left: 0;
  }

  &.h2 {
    font-size: 16px;
    color: #606266;
    margin-left: 20px;
  }

  &.h3 {
    font-size: 14px;
    color: #909399;
    margin-left: 40px;
  }
}

.menu-children {
  margin-left: 20px;
}
:deep(.github-markdown-body){
  padding: 0;
}
</style>
