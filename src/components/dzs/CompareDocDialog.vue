<template>
  <el-dialog
    v-model="dialogVisible"
    fullscreen
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    :show-close="false"
    class="compare-dialog"
    @close="handleClose"
  >
    <div class="dialog-close" @click="handleClose">
      <el-icon><Close /></el-icon>
    </div>
    
    <div class="compare-container">
      <!-- 左侧：Markdown 内容 -->
      <div class="panel left-panel">
        <div class="panel-header">Markdown 内容</div>
        <div class="panel-content">
          <v-md-preview :text="content" class="md-preview" />
        </div>
      </div>
      
      <!-- 右侧：Office 文档预览 -->
      <div class="panel right-panel">
        <div class="panel-header">原文档</div>
        <div class="panel-content">
          <iframe
            v-if="docUrl"
            :src="getOfficeViewerUrl(docUrl)"
            frameborder="0"
            class="office-preview"
            allowfullscreen
          ></iframe>
          <div v-else class="empty-hint">
            <el-empty description="无文档可预览" />
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { Close } from '@element-plus/icons-vue';

interface Props {
  modelValue: boolean;
  content: string;
  docUrl: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  content: '',
  docUrl: ''
});

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'close'): void;
}>();

const dialogVisible = ref(props.modelValue);

// 获取 Office 在线预览 URL
const getOfficeViewerUrl = (url: string) => {
  return `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(url)}&wdOrigin=BROWSELINK`;
};

const handleClose = () => {
  dialogVisible.value = false;
  emit('update:modelValue', false);
  emit('close');
};

// 监听 props 变化
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val;
});

watch(dialogVisible, (val) => {
  emit('update:modelValue', val);
  if (!val) {
    emit('close');
  }
});
</script>

<style lang="scss" scoped>
.dialog-close {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2001;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 20px;
  transition: all 0.3s;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
    transform: scale(1.1);
  }
  
  .el-icon {
    font-size: 20px;
  }
}

.compare-dialog {
  :deep(.el-dialog) {
    margin: 0 !important;
    max-height: 100%;
    max-width: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__body) {
    padding: 0 !important;
    margin: 0;
    height: calc(100vh - 55px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
}

.compare-container {
  display: flex;
  height: 100%;
  overflow: hidden;
}

.panel {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #dcdfe6;
  
  &:last-child {
    border-right: none;
  }
  
  .panel-header {
    padding: 10px 15px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #dcdfe6;
    font-weight: bold;
    color: #606266;
  }
  
  .panel-content {
    flex: 1;
    overflow: auto;
    padding: 0;
    position: relative;
    
    .md-preview {
      padding: 20px;
      height: 100%;
      box-sizing: border-box;
      overflow-y: auto;
    }
    
    .office-preview {
      width: 100%;
      height: 100%;
      border: none;
    }
    
    .empty-hint {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #909399;
    }
  }
}
:deep(.katex-html) {
  display: none;
}
</style>
