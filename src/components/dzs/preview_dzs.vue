<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    fullscreen
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    :show-close="false"
    destroy-on-close
    class="preview-dialog"
    @close="handleClose"
  >
    <div class="dialog-close" @click="handleClose">
      <el-icon><Close /></el-icon>
    </div>
    <div class="editor-container">
      <v-md-editor
        v-model="rawContent"
        height="calc(100vh - 120px)"
        @change="handleContentChange"
      />
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, onBeforeUnmount, computed, nextTick } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Close } from '@element-plus/icons-vue';
import { UpdateDzsContent } from '@/api/dzs';

interface Props {
  modelValue: boolean;
  id: number | string;
  title?: string;
  content: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '内容预览',
  content: ''
});

const emit = defineEmits<{
  (e: 'update:content', value: string): void;
  (e: 'update:modelValue', value: boolean): void;
  (e: 'close'): void;
  (e: 'save', value: string): void;
}>();

const dialogVisible = ref(props.modelValue);
const rawContent = ref('');
const originalContent = ref('');
let debounceTimer: ReturnType<typeof setTimeout> | null = null;

// 初始化内容
onMounted(() => {
  rawContent.value = props.content;
  originalContent.value = props.content;
});

// 处理内容变化 - 使用防抖
const handleContentChange = (value: string) => {
  // 立即更新本地值以获得流畅的输入体验
  rawContent.value = value;
  
  // 防抖处理，减少更新频率
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }
  
  debounceTimer = setTimeout(() => {
    emit('update:content', value);
  }, 300); // 300ms 防抖延迟
};

// 监听props变化 - 仅在外部内容变化时更新
watch(() => props.content, (newVal) => {
  // 只有当内容确实变化时才更新，避免循环更新
  if (newVal !== rawContent.value) {
    rawContent.value = newVal;
    // 当内容从外部更新时，同时更新原始内容
    if (dialogVisible.value) {
      originalContent.value = newVal;
    }
  }
}, { immediate: true });

// 监听对话框显示/隐藏状态
watch(() => props.modelValue, (newVal) => {
  dialogVisible.value = newVal;
  // 当对话框打开时，初始化原始内容
  if (newVal) {
    originalContent.value = props.content;
  }
});

const hasChanges = () => {
  return rawContent.value !== originalContent.value;
};

const handleClose = async () => {
  // 检查是否有未保存的更改
  if (hasChanges()) {
    try {
      await ElMessageBox.confirm('内容已修改，是否保存更改？', '提示', {
        confirmButtonText: '保存',
        cancelButtonText: '不保存',
        type: 'warning',
        distinguishCancelAndClose: true
      });
      
      // 用户点击了保存
      try {
        const response = await UpdateDzsContent({
          id: props.id,
          content: rawContent.value
        });
        
        if (response.code === 200) {
          ElMessage.success('保存成功');
          originalContent.value = rawContent.value; // 更新原始内容
          emit('update:content', rawContent.value);
          emit('save', rawContent.value);
        } else {
          ElMessage.error(response.msg || '保存失败');
          return; // 保存失败时不关闭对话框
        }
      } catch (error) {
        console.error('保存失败:', error);
        ElMessage.error('保存失败，请重试');
        return; // 保存失败时不关闭对话框
      }
    } catch (error) {
      // 用户点击了取消或关闭
      if (error === 'cancel') {
        // 不保存更改
        originalContent.value = rawContent.value; // 重置原始内容
      } else if (error === 'close') {
        // 用户点击了关闭按钮，取消关闭操作
        return;
      }
    }
  }
  
  // 重置原始内容
  originalContent.value = rawContent.value;
  
  // 关闭对话框
  emit('update:modelValue', false);
  emit('close');
  // 清除防抖定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer);
    debounceTimer = null;
  }
  
  // 确保内容同步
  emit('update:content', rawContent.value);
};

// 组件卸载时清理
onBeforeUnmount(() => {
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }
});
</script>

<style lang="scss" scoped>
.dialog-close {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2001;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 20px;
  transition: all 0.3s;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
    transform: scale(1.1);
  }
  
  .el-icon {
    font-size: 20px;
  }
}

.preview-dialog {
  :deep(.el-dialog) {
    margin: 0 !important;
    max-height: 100%;
    max-width: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__body) {
    padding: 20px !important;
    margin: 0;
    height: calc(100vh - 110px);
    display: flex;
    flex-direction: column;
  }

  .editor-container {
    flex: 1;
    height: 100%;

    :deep(.v-md-editor) {
      border: 1px solid #dcdfe6;
      border-radius: 4px;
    }
  }
}

:deep(.katex-html) {
  display: none;
}
</style>
