<template>
  <el-dialog
    v-model="dialogVisible"
    fullscreen
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    :show-close="false"
    destroy-on-close
    class="preview-dialog"
    @close="handleClose"
  >
    <div class="dialog-close" @click="handleClose">
      <el-icon><Close /></el-icon>
    </div>
    <div class="preview-container">
      <div class="preview-panel left-panel">
        <div class="panel-header">原始内容</div>
        <div class="panel-content">
          <el-input
            v-model="rawContent"
            type="textarea"
            resize="none"
            class="content-textarea"
            @input="handleContentChange"
            @scroll="handleLeftScroll"
          />
        </div>
      </div>
      <div class="preview-panel">
        <div class="panel-header">菜单结构</div>
        <div class="panel-content markdown-preview">
          <div v-if="menuData.length === 0" class="empty-hint">
            输入有效的JSON格式菜单数据
          </div>
          <div v-else class="menu-tree">
            <div v-for="(item, index) in menuData" :key="index" class="menu-item">
              <menu-item :item="item" :level="0" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, computed, defineComponent, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Close } from '@element-plus/icons-vue';
import { UpdateDzsMenu } from '@/api/dzs';
import MenuItem from './MenuItem.vue';

interface MenuItem {
  type: string;
  title: string;
  children?: MenuItem[];
}

interface Props {
  modelValue: boolean;
  id: number | string;
  title?: string;
  content: string;
}

// 同步滚动
const handleLeftScroll = () => {
  let leftScroll = document.querySelector('.el-textarea__inner')
  let rightScroll = document.querySelector('.markdown-preview')
  if (rightScroll && leftScroll) {
    rightScroll.scrollTop = leftScroll.scrollTop;
  }
};

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '菜单预览',
  content: '[]'
});

const emit = defineEmits<{
  (e: 'update:content', value: string): void;
  (e: 'update:modelValue', value: boolean): void;
  (e: 'close'): void;
  (e: 'save', value: string): void;
}>();

const dialogVisible = ref(props.modelValue);
const rawContent = ref('');
const originalContent = ref('');

// 解析JSON内容为菜单数据
const menuData = computed<MenuItem[]>(() => {
  try {
    let content = rawContent.value;
    
    // 如果内容是字符串，尝试解析为JSON
    if (typeof content === 'string') {
      content = JSON.parse(content);
    }
    
    // 确保返回一个数组
    if (Array.isArray(content)) {
      return content.filter(item => item && typeof item === 'object');
    } else if (content && typeof content === 'object') {
      return [content];
    }
    
    return [];
  } catch (e) {
    console.error('解析菜单数据失败:', e);
    return [];
  }
});

// 处理内容变化 - 使用防抖
let debounceTimer: ReturnType<typeof setTimeout> | null = null;
const handleContentChange = (value: string) => {
  // 更新原始字符串值
  rawContent.value = value;
  
  // 防抖处理，减少更新频率
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }
  
  debounceTimer = setTimeout(() => {
    emit('update:content', value);
  }, 300);
};

// 监听props变化 - 仅在外部内容变化时更新
watch(() => props.content, (newVal) => {
  if (newVal !== rawContent.value) {
    // 如果新值是对象，转换为JSON字符串
    if (typeof newVal === 'object' && newVal !== null) {
      rawContent.value = JSON.stringify(newVal, null, 2);
    } else {
      rawContent.value = newVal || '';
    }
    
    // 当内容从外部更新时，同时更新原始内容
    if (dialogVisible.value) {
      originalContent.value = rawContent.value;
    }
  }
}, { immediate: true });

// 初始化时处理内容
onMounted(() => {
  if (props.content) {
    if (typeof props.content === 'object') {
      rawContent.value = JSON.stringify(props.content, null, 2);
    } else {
      rawContent.value = props.content;
    }
    originalContent.value = rawContent.value;
  }
});

// 监听对话框显示/隐藏状态
watch(() => props.modelValue, (newVal) => {
  dialogVisible.value = newVal;
  // 当对话框打开时，初始化原始内容
  if (newVal) {
    originalContent.value = rawContent.value;
  }
});

const hasChanges = () => {
  return rawContent.value !== originalContent.value;
};

const handleClose = async () => {
  // 检查是否有未保存的更改
  if (hasChanges()) {
    try {
      await ElMessageBox.confirm('内容已修改，是否保存更改？', '提示', {
        confirmButtonText: '保存',
        cancelButtonText: '不保存',
        type: 'warning',
        distinguishCancelAndClose: true
      });
      
      // 用户点击了保存
      try {
        let menuData;
        // 确保内容是有效的JSON对象
        if (typeof rawContent.value === 'string') {
          menuData = JSON.parse(rawContent.value);
        } else {
          menuData = rawContent.value;
        }
        
        // 调用更新菜单的API
        const response = await UpdateDzsMenu({
          id: props.id,
          menu: menuData
        });
        
        if (response.code === 200) {
          // 更新本地原始内容
          originalContent.value = rawContent.value;
          // 通知父组件更新
          emit('update:content', JSON.stringify(menuData, null, 2));
          emit('save', JSON.stringify(menuData, null, 2));
          ElMessage.success('保存成功');
        } else {
          ElMessage.error(response.msg || '保存失败');
          return; // 保存失败时不关闭对话框
        }
      } catch (e) {
        console.error('保存菜单失败:', e);
        ElMessage.error('保存失败，请检查数据格式');
        return; // 保存失败时不关闭对话框
      }
    } catch (error) {
      // 用户点击了取消或关闭
      if (error === 'cancel') {
        // 不保存更改，恢复原始内容
        rawContent.value = originalContent.value;
      } else if (error === 'close') {
        // 用户点击了关闭按钮，取消关闭操作
        return;
      }
    }
  }
  
  // 关闭对话框
  emit('update:modelValue', false);
  emit('close');
  
  // 清除防抖定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer);
    debounceTimer = null;
  }
};
</script>

<script lang="ts">
// 递归组件需要单独写在一个script标签中
// 使用 defineComponent 定义组件
export default defineComponent({
  name: 'PreviewDzsMenu',
  components: {
    MenuItem: () => import('./MenuItem.vue')
  }
});
</script>

<style lang="scss" scoped>
.dialog-close {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2001;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 20px;
  transition: all 0.3s;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
    transform: scale(1.1);
  }
  
  .el-icon {
    font-size: 20px;
  }
}

.preview-dialog {
  :deep(.el-dialog) {
    margin: 0 !important;
    max-height: 100%;
    max-width: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__body) {
    padding: 0 !important;
    margin: 0;
    height: calc(100vh - 55px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .preview-container {
    display: flex;
    height: calc(100vh - 100px);
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
  }

  .left-panel {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }

  .preview-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #dcdfe6;
    overflow: hidden;
    min-width: 0;

    &:last-child {
      border-right: none;
    }
  }

  .panel-header {
    padding: 10px 15px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #dcdfe6;
    font-weight: bold;
    color: #606266;
  }

  .panel-content {
    flex: 1;
    overflow: auto;
    padding: 15px;
    display: flex;
    flex-direction: column;

    .el-textarea {
      flex: 1;
      min-height: 0;
      
      :deep(.el-textarea__inner) {
        height: 100% !important;
        min-height: 100% !important;
        resize: none;
        font-family: 'Courier New', monospace;
        line-height: 1.5;
        border: none;
        box-shadow: none;
      }
    }
  }

  .empty-hint {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
    font-size: 14px;
  }

  .menu-tree {
    padding: 10px 0;
  }

  .menu-item {
    margin-bottom: 8px;
  }
}
</style>
