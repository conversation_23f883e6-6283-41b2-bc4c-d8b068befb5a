<template>
    <div ref="containerRef" @mouseup="handleMouseUp">
      <slot>
      </slot>
      <!-- 朗读按钮 -->
      <el-button
        v-if="showReadButton"
        :style="{
          position: 'absolute',
          top: `${buttonPosition.top}px`,
          left: `${buttonPosition.left}px`,
          zIndex: 1000
        }"
        class="px-3 py-1 bg-blue-600 text-white rounded shadow"
        round
        @click="showVoicePanel"
      >
        朗读文字
      </el-button>
      <!-- 语音控制面板 -->
      <el-dialog
        v-model="showVoiceControlPanel"
        :style="{
          position: 'absolute',
          top: `${buttonPosition.top + 40}px`,
          left: `${buttonPosition.left}px`,
          margin: 0,
          padding: '10px'
        }"
        width="300px"
        :show-close="false"
        :modal="false"
        class="voice-control-panel"
      >
        <div class="voice-control-content">
          <div class="voice-select">
            <span>选择语音：</span>
            <el-select v-model="selectedVoiceIndex" @change="handleVoiceChange">
              <el-option
                v-for="(voice, index) in chineseVoices"
                :key="index"
                :label="voice.name"
                :value="index"
              />
            </el-select>
          </div>
          <div class="voice-controls">
            <el-button @click="pauseReading" :disabled="!isReading">
              {{ isPaused ? '继续' : '暂停' }}
            </el-button>
            <el-button @click="stopReading" :disabled="!isReading">停止</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted, onUnmounted, nextTick } from 'vue';
  
  const props = defineProps<{ content?: string }>();
  
  const containerRef = ref<HTMLElement | null>(null);
  const showReadButton = ref(false);
  const buttonPosition = ref({ top: 0, left: 0 });
  const selectedVoice = ref<SpeechSynthesisVoice | null>(null);
  const utterance = ref<SpeechSynthesisUtterance | null>(null);
  const showVoiceControlPanel = ref(false);
  const selectedVoiceIndex = ref(0);
  const chineseVoices = ref<SpeechSynthesisVoice[]>([]);
  const isReading = ref(false);
  const isPaused = ref(false);
  const selectedText = ref('');
  const currentReadingIndex = ref(-1);
  const readingTexts = ref<string[]>([]);
  const selectedElement = ref<HTMLElement | null>(null);
  const originalHTML = ref('');
  const sentenceRanges = ref<{ start: number; end: number; text: string }[]>([]);
  
  const getChineseVoice = () => {
    const voices = window.speechSynthesis.getVoices();
    chineseVoices.value = voices.filter((voice) => voice.lang === 'zh-CN');
    return chineseVoices.value[0];
  };
  
  onMounted(() => {
    if (speechSynthesis.onvoiceschanged !== undefined) {
      speechSynthesis.onvoiceschanged = () => {
        const voices = getChineseVoice();
        if (voices) {
          selectedVoice.value = voices;
        }
      };
    }
    const voices = getChineseVoice();
    if (voices) {
      selectedVoice.value = voices;
    }
  });
  
  onUnmounted(() => {
    stopReading();
  });
  
  const splitTextIntoParagraphs = (text: string): string[] => {
    const segments = text.split(/([。！？.!?])/).filter((p) => p.trim().length > 0);
    const result: string[] = [];
    for (let i = 0; i < segments.length; i += 2) {
      if (i + 1 < segments.length) {
        result.push(segments[i] + segments[i + 1]);
      } else {
        result.push(segments[i]);
      }
    }
    return result;
  };
  
  const handleMouseUp = (event: MouseEvent) => {
    const selection:any = window.getSelection();
    const text = selection?.toString().trim() || '';
  
    if (text.length > 0) {
      selectedText.value = text;
      buttonPosition.value = {
        top: event.clientY + window.scrollY + 10,
        left: event.clientX + window.scrollX + 10,
      };
      showReadButton.value = true;
  
      // 记录原文DOM和HTML
      const range = selection.getRangeAt(0);
      selectedElement.value = range.commonAncestorContainer.parentElement as HTMLElement;
      if (!selectedElement.value) return;
      originalHTML.value = selectedElement.value.innerHTML;
  
      // 计算每个句子在原文中的起止位置
      const fullText = selectedElement.value.innerText;
      const selectedStart = fullText.indexOf(text);
      if (selectedStart === -1) return;
      const sentences = splitTextIntoParagraphs(text);
      sentenceRanges.value = [];
      let offset = selectedStart;
      for (const s of sentences) {
        const idx = fullText.indexOf(s, offset);
        if (idx !== -1) {
          sentenceRanges.value.push({ start: idx, end: idx + s.length, text: s });
          offset = idx + s.length;
        }
      }
  
      readingTexts.value = sentences;
      showVoiceControlPanel.value = true;
      if (chineseVoices.value.length > 0) {
        startReadingFromIndex(0);
      } else {
        speechSynthesis.onvoiceschanged = () => {
          chineseVoices.value = window.speechSynthesis.getVoices().filter((voice) => voice.lang === 'zh-CN');
          if (chineseVoices.value.length > 0) {
            startReadingFromIndex(0);
          }
        };
      }
    } else {
      showReadButton.value = false;
    }
  };
  
  const highlightCurrentSentence = (index: number) => {
    if (!selectedElement.value || !originalHTML.value) return;
    restoreOriginalHTML();
    const range = sentenceRanges.value[index];
    if (!range) return;
    const safeText = range.text.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\$&');
    const re = new RegExp(safeText);
    const html = selectedElement.value.innerHTML;
    const newHtml = html.replace(re, `<span class="reading-highlight">${range.text}</span>`);
    selectedElement.value.innerHTML = newHtml;
  };
  
  const restoreOriginalHTML = () => {
    if (selectedElement.value && originalHTML.value) {
      selectedElement.value.innerHTML = originalHTML.value;
    }
  };
  
  const showVoicePanel = () => {
    showVoiceControlPanel.value = true;
    if (selectedText.value && readingTexts.value.length > 0) {
      if (chineseVoices.value.length > 0) {
        startReadingFromIndex(0);
      } else {
        speechSynthesis.onvoiceschanged = () => {
          chineseVoices.value = window.speechSynthesis.getVoices().filter((voice) => voice.lang === 'zh-CN');
          if (chineseVoices.value.length > 0) {
            startReadingFromIndex(0);
          }
        };
      }
    }
  };
  
  const handleVoiceChange = () => {
    stopReading();
    const idx = currentReadingIndex.value >= 0 ? currentReadingIndex.value : 0;
    if (readingTexts.value.length > 0) {
      startReadingFromIndex(idx);
    }
  };
  
  const pauseReading = () => {
    if (isPaused.value) {
      window.speechSynthesis.resume();
      isPaused.value = false;
    } else {
      window.speechSynthesis.pause();
      isPaused.value = true;
    }
  };
  
  const stopReading = () => {
    window.speechSynthesis.cancel();
    isReading.value = false;
    isPaused.value = false;
    currentReadingIndex.value = -1;
    restoreOriginalHTML();
  };
  
  const startReadingFromIndex = (index: number) => {
    if (index < 0 || index >= readingTexts.value.length) return;
    currentReadingIndex.value = index;
    const text = readingTexts.value[index];
    const voice = chineseVoices.value[selectedVoiceIndex.value];
    if (!voice) {
      alert('未找到可用中文语音');
      return;
    }
    highlightCurrentSentence(index);
    window.speechSynthesis.cancel();
    const u = new SpeechSynthesisUtterance(text);
    u.voice = voice;
    u.lang = voice.lang;
    u.onstart = () => console.log('朗读开始');
    u.onend = () => {
      if (currentReadingIndex.value < readingTexts.value.length - 1) {
        startReadingFromIndex(currentReadingIndex.value + 1);
      } else {
        isReading.value = false;
        isPaused.value = false;
        showVoiceControlPanel.value = false;
        currentReadingIndex.value = -1;
        restoreOriginalHTML();
      }
    };
    window.speechSynthesis.speak(u);
    isReading.value = true;
    isPaused.value = false;
  };
  </script>
  
  <style scoped lang="scss">
  :deep(.reading-highlight) {
    background: #FFEB3B !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
  }
  .voice-control-panel {
    :deep(.el-dialog__header) {
      display: none;
    }
    :deep(.el-dialog__body) {
      padding: 10px;
    }
  }
  .voice-control-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
    .voice-select {
      display: flex;
      align-items: center;
      gap: 10px;
      span {
        white-space: nowrap;
      }
    }
    .voice-controls {
      display: flex;
      gap: 10px;
      justify-content: center;
    }
  }
  </style>