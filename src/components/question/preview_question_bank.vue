<template>
  <div class="preview_bank_global" :v-loading="loading" 
          v-loading="loading_preview_lobal"
          :element-loading-text="loadingText_preview_lobal"
          :element-loading-spinner="init_dialog_adup.loadingSvg"
          element-loading-svg-view-box="-10, -10, 50, 50"
          element-loading-background="rgba(122, 122, 122, 0.8)">
    <div class="preview-question-bank" >
      <div class="main-content" v-if="datajob.length>0" >
        <div v-for="(groupjob, groupIdx) in datajob" :key="groupjob.id" class="question-group" >
          <div class="question-item" :data-question-id="groupjob.id">
            <div class="question-header">
              <!-- <span class="question-jobid"> {{getQuestionDisplayNumber(groupjob)}}、</span> -->
              <span class="question-jobid"> {{ groupIdx+1 }}、</span>
              <span class="question-type">【{{ groupjob.questiontype }}】 </span>
              <span class="question-jobid">题编号： ( {{groupjob.id}} ) </span>
              <span class="question-jobid">题序号：  {{groupjob.sn}} </span>
              <el-button type="success" icon="Edit" size="small" circle @click="handleJobEdit(groupjob)"/>
            </div>
            <div class="question-header">
              <div class="el_tag_5">
                <el-tag class="el_tag_5">题干</el-tag>
              </div>
              <div class="jobbank-title" >
                <v-md-preview :text="groupjob.title.title"></v-md-preview>
                 <!-- <mavon-editor :toolbarsFlag="false" :subfield="false" defaultOpen="preview" :editable="false" :boxShadow="false" :html="true" style="margin-top: 10px;" v-model="groupjob.title.title" /> -->
              </div>
            </div>
            <div class="question-header" v-if="groupjob.score_explain!='' && groupjob.score_explain===null && groupjob.score_explain===undefined ">
              <div class="el_tag_5">
                <el-tag class="el_tag_5">得分说明</el-tag>
              </div>
              <div class="jobbank-title" >
                <v-md-preview :text="groupjob.score_explain"></v-md-preview>

              </div>
            </div>
            <!-- 单一题型 -->
            <div v-if="'options' in groupjob.title" >
              <div class="question-options">
                  <div v-if="groupjob.title.questionType.includes('单')">
                    <el-radio-group v-model="groupjob.title.answer" disabled  >
                      <el-radio :value="opt.listNo" size="large" v-for="(opt, optIdx) in groupjob.title.options" :key="optIdx">
                        <div class="opt_options">
                          <span class="option-label">{{ opt.listNo }}</span>
                          <v-md-preview :text="opt.option"></v-md-preview>
                        </div>
                      </el-radio> 
                    </el-radio-group>
                  </div>
                  <div v-else-if="groupjob.title.questionType.includes('多')">
                    <el-checkbox-group :model-value="groupjob.title.answer ? groupjob.title.answer.split(',') : []" disabled>
                      <el-checkbox :value="opt.listNo" size="large" v-for="(opt, optIdx) in groupjob.title.options" :key="optIdx">
                        <div class="opt_options">
                          <span class="option-label">{{ opt.listNo }}</span>
                          <v-md-preview :text="opt.option"></v-md-preview>
                        </div>
                      </el-checkbox> 
                    </el-checkbox-group>
                  </div>
                  <div v-else>
                     <el-checkbox-group :model-value="groupjob.title.answer ? groupjob.title.answer.split(',') : []" disabled>
                      <el-checkbox :value="opt.listNo" size="large" v-for="(opt, optIdx) in groupjob.title.options" :key="optIdx">
                        <div class="opt_options">
                          <span class="option-label">{{ opt.listNo }}</span>
                          <v-md-preview :text="opt.option"></v-md-preview>
                        </div>
                      </el-checkbox> 
                    </el-checkbox-group>
                  </div>
              </div>
              <div class="question-header">
                <div class="el_tag_5">
                  <el-tag class="el_tag_5">选项答案</el-tag>
                </div>
                <div class="question-title">
                  <v-md-preview :text="groupjob.title.answer"></v-md-preview>
                </div>
              </div>
              <div class="question-header">
                <div class="el_tag_5">
                  <el-tag class="el_tag_5" >答案解析</el-tag>
                </div>
                <div class="question-title">
                  <v-md-preview :text="groupjob.title.answer_parsing"></v-md-preview>
                </div>
              </div> 
            </div>
            <!-- 单一判断题型 -->
            <div v-else-if="groupjob.title.questionType.includes('判') && (groupjob.title.answer.includes('0') || groupjob.title.answer.includes('1'))">
                  <el-radio-group v-model="groupjob.title.answer" disabled  >
                    <el-radio value="1">A、正确</el-radio>
                    <el-radio value="0">B、错误</el-radio>
                  </el-radio-group>
                    <div class="question-header">
                      <div class="el_tag_5">
                        <el-tag class="el_tag_5">选项答案</el-tag>
                      </div>
                      <div class="question-title">
                          <span v-if="groupjob.answer.includes('0')">错误</span>
                          <span v-if="groupjob.answer.includes('1')">正确</span>
                          <span v-else style="color: red;"> {{ groupjob.answer }} </span>
                      </div>
                    </div>
                    <div class="question-header">
                      <div class="el_tag_5">
                        <el-tag class="el_tag_5" >答案解析</el-tag>
                      </div>
                      <div class="question-title">
                        <v-md-preview :text="groupjob.answer_parsing"></v-md-preview>
                      </div>
                    </div> 
              </div>
            <!-- 组合题型 -->
            <div v-else-if="'subQuestions' in groupjob.title">
              <div v-for="(subQu, subQuIdx) in groupjob.title.subQuestions" :key="subQuIdx"> 
                  <!-- 小题题干 -->
                  <div class="question-header">
                    <div class="el_tag_5">
                      <el-tag class="el_tag_5">小题干</el-tag>
                    </div>
                    <div class="jobbank-title" >
                      <v-md-preview :text="subQu.title"></v-md-preview>
                    </div>
                  </div>
                  <div v-if="'options' in subQu" >
                      <div class="question-options">
                          <div v-if="subQu.questionType.includes('单')">
                            <el-radio-group v-model="subQu.answer" disabled  >
                              <el-radio :value="subopt.listNo" size="large" v-for="(subopt, suboptIdx) in subQu.options" :key="suboptIdx">
                                <div class="opt_options">
                                  <span class="option-label">{{ subopt.listNo }}</span>
                                  <v-md-preview :text="subopt.option"></v-md-preview>
                                </div>
                              </el-radio> 
                            </el-radio-group>
                          </div>
                          <div v-else-if="subQu.questionType.includes('多')">
                            <el-checkbox-group :model-value="subQu.answer ? subQu.answer.split(',') : []" disabled>
                              <el-checkbox :value="subopt.listNo" size="large" v-for="(subopt, suboptIdx) in subQu.options" :key="suboptIdx">
                                <div class="opt_options">
                                  <span class="option-label">{{ subopt.listNo }}</span>
                                  <v-md-preview :text="subopt.option"></v-md-preview>
                                </div>
                              </el-checkbox> 
                            </el-checkbox-group>
                          </div>
                          <div v-else>
                            <el-checkbox-group :model-value="subQu.answer ? subQu.answer.split(',') : []" disabled>
                              <el-checkbox :value="subopt.listNo" size="large" v-for="(subopt, suboptIdx) in subQu.options" :key="suboptIdx">
                                <div class="opt_options">
                                  <span class="option-label">{{ subopt.listNo }}</span>
                                  <v-md-preview :text="subopt.option"></v-md-preview>
                                </div>
                              </el-checkbox> 
                            </el-checkbox-group>
                          </div>
                      </div>
                      <div class="question-header">
                        <div class="el_tag_5">
                          <el-tag class="el_tag_5">选项答案</el-tag>
                        </div>
                        <div class="question-title">
                          <v-md-preview :text="subQu.answer"></v-md-preview>
                        </div>
                      </div>
                      <div class="question-header">
                        <div class="el_tag_5">
                          <el-tag class="el_tag_5" >答案解析</el-tag>
                        </div>
                        <div class="question-title">
                          <v-md-preview :text="subQu.answer_parsing"></v-md-preview>
                        </div>
                      </div> 
                  </div>
                  <div v-else-if="subQu.questionType.includes('判') && (subQu.answer.includes('0') || subQu.answer.includes('1'))">
                    <el-radio-group v-model="subQu.answer" disabled  >
                      <el-radio value="1">A、正确</el-radio>
                      <el-radio value="0">B、错误</el-radio>
                    </el-radio-group>
                    <div class="question-header">
                      <div class="el_tag_5">
                        <el-tag class="el_tag_5">选项答案</el-tag>
                      </div>
                      <div class="question-title">
                          <span v-if="subQu.answer.includes('0')">错误</span>
                          <span v-if="subQu.answer.includes('1')">正确</span>
                          <span v-else style="color: red;"> {{ subQu.answer }} </span>
                      </div>
                    </div>
                    <div class="question-header">
                      <div class="el_tag_5">
                        <el-tag class="el_tag_5" >答案解析</el-tag>
                      </div>
                      <div class="question-title">
                        <v-md-preview :text="subQu.answer_parsing"></v-md-preview>
                      </div>
                    </div> 
                  </div>
                  <div v-else>
                    <div class="question-header"> 
                      <div class="el_tag_5">
                        <el-tag class="el_tag_5" >答案</el-tag>
                      </div>
                      <div class="question-title">
                        <v-md-preview :text="subQu.answer"></v-md-preview>
                      </div>
                    </div>
                    <div class="question-header">
                      <div class="el_tag_5">
                        <el-tag class="el_tag_5" >答案解析</el-tag>
                      </div>
                      <div class="question-title">
                        <v-md-preview :text="subQu.answer_parsing"></v-md-preview>
                      </div>
                    </div> 
                  </div> 
              </div>
            </div>
            <!-- 其他题型（主观题） -->
            <div v-else>
              <div class="question-header"> 
                <div class="el_tag_5">
                  <el-tag class="el_tag_5" >答案</el-tag>
                </div>
                <div class="question-title">
                  <v-md-preview :text="groupjob.answer"></v-md-preview>
                  
                </div>
              </div>
              <div class="question-header">
                <div class="el_tag_5">
                  <el-tag class="el_tag_5" >答案解析</el-tag>
                </div>
                <div class="question-title">
                  <v-md-preview :text="groupjob.answer_parsing"></v-md-preview>
                </div>
              </div> 
            </div> 
            <!-- 试题各类信息 -->
            <div class="question-header">
                <el-tag class="el_tag_5" >知识点</el-tag>
                <span>{{ groupjob.knowledge_point }}</span>
            </div>
            <div class="question-header">
                <el-tag class="el_tag_5" >试题来源</el-tag>
                <span>{{ groupjob.jobbank_source_title }}</span>
            </div>
            <div class="question-header">
                <el-tag class="el_tag_5" >试题能力层次</el-tag>
                <span>{{ groupjob.competence_level }}</span>
            </div>
            <div class="question-header">
                <el-tag class="el_tag_5" >难度能力层次</el-tag>
                <span>{{ groupjob.difficulty_level }}</span>
            </div>
            <div class="question-header">
                <el-tag class="el_tag_5" >版本(套题)</el-tag>
                <span>{{ groupjob.ver }}</span>
            </div> 
          </div>
        </div>
        
      </div>
      <div class="main-content" v-else>
        <span v-if="quesAlldata.code==200">所选套题，无题库信息！！！请确认</span>
        <span v-else>{{quesAlldata.msg}}</span>
      </div>
      <div class="side-info">
        <div class="info-item">
          <el-button type="success" plain size="default" icon="refresh" round @click="getJobDataVer">刷新</el-button> 
        </div>
        <div v-if="datajob.length>0" class="info-item">
          <span>（ {{ datajob[0].kc_bm }} ） {{ datajob[0].kc_mc }} (共{{datajob.length}}题)</span>
        </div>
        <div v-if="datajob.length>0" >
            <div class="question-types-card">
              <div class="card-title">试题选择卡</div>
              <div class="type-list">
                <div v-for="(questions, type) in groupedQuestions" 
                     :key="type" 
                     class="type-item">
                  <div class="type-header">
                    <div class="type-info">
                      <span class="type-name">{{type}}</span>
                      <span class="type-count">(共{{questions.length}}题)</span>
                    </div>
                  </div>
                  <div class="sub-questions">
                    <div class="question-numbers-div">
                      <div v-for="(question, index) in questions" 
                            :key="question.id"
                            class="question-number-indx"
                            @click="scrollToQuestion(question.id)">
                        {{ getQuestionNumberByType(type, index) }}
                        <!-- {{ index + 1 }}  -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
        </div>
      </div>
    </div>

     <!-- 题库编辑 -->
    <el-dialog v-model="init_dialog_adup.dialogShow"
              align-center draggable
              v-if="init_dialog_adup.dialogShow"
              :show-close="true"
              :fullscreen="true"
              :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="dialog_content" v-loading="init_dialog_adup.loading"
          :element-loading-text="init_dialog_adup.loadingText"
          :element-loading-spinner="init_dialog_adup.loadingSvg"
          element-loading-svg-view-box="-10, -10, 50, 50"
          element-loading-background="rgba(122, 122, 122, 0.8)">
        <UpQuestionJob :jobbankid="init_dialog_adup.jobbankid"></UpQuestionJob>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, watch, defineProps,onUnmounted } from 'vue';
import { ElMessage,ElMessageBox } from 'element-plus'
import { getJobQuestionsPreview } from '@/api/question'
import {toolsUtils} from "@/utils/tools";
import UpQuestionJob from "@/components/question/up_question_job.vue";
import { ms } from 'element-plus/es/locale';

// 接收的参数
const props = defineProps<{ 
  coursebaseid: string | number
  ,jobbanksourceid: string | number
  , jobbankver: string | number 
  , jobbankid: string | number 
  
}>(); 
const course_base_id = ref(props.coursebaseid);
const jobbank_source_id = ref(props.jobbanksourceid);
const jobbank_ver = ref(props.jobbankver);
const jobbank_id = ref(props.jobbankid);

const datajob = ref([]) as any; 
const quesAlldata = ref([]) as any; 
const answerArray = ref([]) as any;  
const loading = ref(false); 
const loading_preview_lobal = ref(false); 
const loadingText_preview_lobal = ref("题库渲染中，请稍后..."); 

const init_dialog_adup = reactive({
  dialogShow: false,
  title: '',
  loading: false,
  loadingText: "获取数据中，请稍后...",
  loadingSvg: `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `,
  jobbankid: 0
})

const groupedQuestions = ref([]) as any; 

const getGroupedByQuestions = () => {
  const groups: { [key: string]: any[] } = {};
  datajob.value.forEach((job: any) => {
    const type = job.questiontype;
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push( { 'id':job.id });
  });
  groupedQuestions.value=groups
};


const scrollToQuestion = (id: number) => {
  const element = document.querySelector(`[data-question-id="${id}"]`);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

const handleJobEdit = (jobrow:any) => {
    init_dialog_adup.dialogShow = true
    init_dialog_adup.jobbankid=jobrow.id
}



const getJobDataVer = async () => {
  // 根据答案自动勾选选项
  loading.value = true;
  loading_preview_lobal.value = true;
  try {
    const pars = {
      course_base_id: course_base_id.value
      ,jobbank_source_id: jobbank_source_id.value
      ,jobbank_ver: jobbank_ver.value
      ,jobbank_id: jobbank_id.value
    }
    getJobQuestionsPreview(pars).then((msg: any) => {
      datajob.value = msg.data || []
      quesAlldata.value = msg 
      if (datajob.value.length>0) { 
        getGroupedByQuestions()
        setTimeout(() => {
          loading_preview_lobal.value = false
          loading.value = false
        }, 2000)
      }else{
        loading_preview_lobal.value = false
        loading.value = false
      }
      
    }).catch((err: any) => {
      console.log(err)
      loading.value  = false
    })
  } catch (e) {
    ElMessage.error('题库获取失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => { 
  getJobDataVer();//试题详细
});

watch(() => props.coursebaseid, (newVal) => {
  course_base_id.value = newVal;
  getJobDataVer();
});

// 根据题型和索引获取题号
const getQuestionNumberByType = (type: any, index: number) => {
  return typeStartNumbers.value[type] + index;
};

// 获取每个题型的起始序号
const typeStartNumbers = computed(() => {
  const numbers: { [key: string]: number } = {};
  let currentNumber = 1;
  
  for (const type in groupedQuestions.value) {
    numbers[type] = currentNumber;
    currentNumber += groupedQuestions.value[type].length;
  }
  return numbers;
});


 
</script>

<style lang="scss">
.preview_bank_global{
  .opt_options {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    padding: 4px 0;
  }
  .option-label {
    min-width: 22px;
    text-align: center;
    margin-right: 5px;
    font-weight: bold;
    display: inline-block;
  } 
 
  .question-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: auto;
    margin-left:100px;
    align-items: flex-start;
    margin-top: 10px;
  }

  .opt_options .v-md-preview {
    flex: 1;
    margin: 0;
    word-break: break-all;
  }
  .el_tag_5{
    width: 100px;
  }
  .jobbank-title{
    width: auto; 
    margin-top: 10px;
  }
  .question-title{
    width: auto;
  }



  .v-md-editor-preview{
    .github-markdown-body{
      padding:0px !important
      
    }
    .github-markdown-body blockquote, 
    .github-markdown-body details, 
    .github-markdown-body dl, 
    .github-markdown-body ol, 
    .github-markdown-body p, 
    .github-markdown-body pre, 
    .github-markdown-body table, 
    .github-markdown-body ul{
        margin: 0 !important;
    }
    .katex-html{
      display: none !important;
    }
  }

  .preview-question-bank {
    display: flex;
    gap: 24px;
    height: calc(100vh - 80px); 
  }
  .main-content {
    flex: 1;
    overflow-y: auto;
  }
  .question-group {
    background: #fff;
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px #f0f1f2;
    padding: 20px;
    min-width:800px ;
  }
  
  .question-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 10px;
  }
  .question-type {
    color: #409eff;
    font-weight: bold;
    width: auto;
  }
  .question-jobid {
    color: #409eff;
    font-weight: bold;
  }
  
  
  .side-info {
    width: 300px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px #f0f1f2;
    padding: 18px 20px;
    display: flex;
    flex-direction: column;
    gap: 18px;
    height: max-content;
  }
  .info-item {
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    color: #333;
  }
  .print-btn {
    margin-top: 20px;
    width: 100%;
    background: #409eff;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 8px 0;
    font-size: 16px;
    cursor: pointer;
  }
  .print-btn:hover {
    background: #66b1ff;
  }
  .question-types-card {
    margin-top: 20px;
    border-top: 1px solid #eee;
    padding-top: 15px;
    flex: 1;

    .card-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 12px;
      color: #333;
    }

    .type-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
      flex: 1;
      overflow-y: auto;
      height: calc(100vh - 250px); 
    }

    .type-item {
      margin-bottom: 8px;
    }

    .type-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      padding: 8px 12px;
      background: #f5f7fa;
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        background: #ecf5ff;
      }
    }

    .type-info {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .type-name {
      font-size: 14px;
      font-weight: 500;
    }

    .type-count {
      font-size: 12px;
      color: #909399;
    }

    .sub-questions {
      margin-top: 8px;
    }

    
    .question-numbers-div {
      padding: 0 12px;
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 8px;
      background: #fff;
      padding: 8px;
      border-radius: 4px;
    }

    .question-number-indx {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f5f7fa;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.3s;
      border: 1px solid #e4e7ed;

      &:hover {
        background: #ecf5ff;
        border-color: #409eff;
        color: #409eff;
      }

      &.active {
        background: #409eff;
        color: white;
        border-color: #409eff;
      }
    }
  }
}
</style>
