<template>
  <div class="preview_bank_global" :v-loading="loading" 
          v-loading="loading_preview_lobal"
          :element-loading-text="loadingText_preview_lobal"
          element-loading-spinner="el-icon-loading"
          element-loading-svg-view-box="-10, -10, 50, 50"
          element-loading-background="rgba(122, 122, 122, 0.8)">
    <div class="preview-question-bank" >
      <div class="main-content" v-if="datajob.length>0" >
        <div v-for="(groupjob, groupIdx) in datajob" :key="groupIdx" class="question-group" >
          <div class="question-item" :data-question-id="groupjob.id">
            <div class="question-header">
              <!-- <span class="question-jobid"> {{getQuestionDisplayNumber(groupjob)}}、</span> -->
              <span class="question-jobid"> {{ groupIdx+1 }}、</span>
              <span class="question-type">【{{ groupjob.questiontype }}】 </span>
              <span class="question-jobid">题编号： ( {{groupjob.id}} ) </span>
              <span class="question-jobid">题序号：  {{groupjob.sn}} </span>
              <el-button type="primary" size="small" @click="runAITest(groupjob)" :loading="groupjob.aiLoading" :disabled="!!groupjob.aiResult" class="ai-test-btn">
                {{ 'AI测试' }}
              </el-button>
            </div>
            <div class="question-header">
              <div class="el_tag_5">
                <el-tag class="el_tag_5">题干</el-tag>
              </div>
              <div class="jobbank-title" >
                <v-md-preview :text="groupjob.title.title"></v-md-preview>
                 <!-- <mavon-editor :toolbarsFlag="false" :subfield="false" defaultOpen="preview" :editable="false" :boxShadow="false" :html="true" style="margin-top: 10px;" v-model="groupjob.title.title" /> -->
              </div>
            </div>
            <div class="question-header" v-if="groupjob.score_explain!='' && groupjob.score_explain===null && groupjob.score_explain===undefined ">
              <div class="el_tag_5">
                <el-tag class="el_tag_5">得分说明</el-tag>
              </div>
              <div class="jobbank-title" >
                <v-md-preview :text="groupjob.score_explain"></v-md-preview>

              </div>
            </div>
            <!-- 单一题型 -->
            <div v-if="'options' in groupjob.title" >
              <div class="question-options">
                  <div v-if="groupjob.title.questionType.includes('单')">
                    <el-radio-group v-model="groupjob.title.answer" disabled  >
                      <el-radio :value="opt.listNo" size="large" v-for="(opt, optIdx) in groupjob.title.options" :key="optIdx">
                        <div class="opt_options">
                          <span class="option-label">{{ opt.listNo }}</span>
                          <v-md-preview :text="opt.option"></v-md-preview>
                        </div>
                      </el-radio> 
                    </el-radio-group>
                  </div>
                  <div v-else-if="groupjob.title.questionType.includes('多')">
                    <div style="display: none;">{{ answerArray = groupjob.title.answer ? groupjob.title.answer.split(',') : [] }}</div>
                    <el-checkbox-group v-model="answerArray" disabled>
                      <el-checkbox :value="opt.listNo" size="large" v-for="(opt, optIdx) in groupjob.title.options" :key="optIdx">
                        <div class="opt_options">
                          <span class="option-label">{{ opt.listNo }}</span>
                          <v-md-preview :text="opt.option"></v-md-preview>
                        </div>
                      </el-checkbox> 
                    </el-checkbox-group>
                  </div>
                  <div v-else>
                    <div style="display: none;">{{ answerArray = groupjob.title.answer ? groupjob.title.answer.split(',') : [] }}</div>
                    <el-checkbox-group v-model="answerArray" disabled>
                      <el-checkbox :value="opt.listNo" size="large" v-for="(opt, optIdx) in groupjob.title.options" :key="optIdx">
                        <div class="opt_options">
                          <span class="option-label">{{ opt.listNo }}</span>
                          <v-md-preview :text="opt.option"></v-md-preview>
                        </div>
                      </el-checkbox> 
                    </el-checkbox-group>
                  </div>
              </div>
              <div class="question-header">
                <div class="el_tag_5">
                  <el-tag class="el_tag_5">选项答案</el-tag>
                </div>
                <div class="question-title">
                  <v-md-preview :text="groupjob.title.answer"></v-md-preview>
                </div>
              </div>
              <div class="question-header">
                <div class="el_tag_5">
                  <el-tag class="el_tag_5" >答案解析</el-tag>
                </div>
                <div class="question-title">
                  <v-md-preview :text="groupjob.title.answer_parsing"></v-md-preview>
                </div>
              </div> 
            </div>
            <!-- 单一判断题型 -->
            <div v-else-if="groupjob.title.questionType.includes('判') && (groupjob.title.answer.includes('0') || groupjob.title.answer.includes('1'))">
                  <el-radio-group v-model="groupjob.title.answer" disabled  >
                    <el-radio value="1">A、正确</el-radio>
                    <el-radio value="0">B、错误</el-radio>
                  </el-radio-group>
                    <div class="question-header">
                      <div class="el_tag_5">
                        <el-tag class="el_tag_5">选项答案</el-tag>
                      </div>
                      <div class="question-title">
                          <span v-if="groupjob.answer.includes('0')">错误</span>
                          <span v-if="groupjob.answer.includes('1')">正确</span>
                          <span v-else style="color: red;"> {{ groupjob.answer }} </span>
                      </div>
                    </div>
                    <div class="question-header">
                      <div class="el_tag_5">
                        <el-tag class="el_tag_5" >答案解析</el-tag>
                      </div>
                      <div class="question-title">
                        <v-md-preview :text="groupjob.answer_parsing"></v-md-preview>
                      </div>
                    </div> 
              </div>
            <!-- 组合题型 -->
            <div v-else-if="'subQuestions' in groupjob.title">
              <div v-for="(subQu, subQuIdx) in groupjob.title.subQuestions" :key="subQuIdx"> 
                  <!-- 小题题干 -->
                  <div class="question-header">
                    <div class="el_tag_5">
                      <el-tag class="el_tag_5">小题干</el-tag>
                    </div>
                    <div class="jobbank-title" >
                      <v-md-preview :text="subQu.title"></v-md-preview>
                    </div>
                  </div>
                  <div v-if="'options' in subQu" >
                      <div class="question-options">
                          <div v-if="subQu.questionType.includes('单')">
                            <el-radio-group v-model="subQu.answer" disabled  >
                              <el-radio :value="subopt.listNo" size="large" v-for="(subopt, suboptIdx) in subQu.options" :key="suboptIdx">
                                <div class="opt_options">
                                  <span class="option-label">{{ subopt.listNo }}</span>
                                  <v-md-preview :text="subopt.option"></v-md-preview>
                                </div>
                              </el-radio> 
                            </el-radio-group>
                          </div>
                          <div v-else-if="subQu.questionType.includes('多')">
                            <div style="display: none;">{{ answerArray = subQu.answer ? subQu.answer.split(',') : [] }}</div>
                            <el-checkbox-group v-model="answerArray" disabled>
                              <el-checkbox :value="subopt.listNo" size="large" v-for="(subopt, suboptIdx) in subQu.options" :key="suboptIdx">
                                <div class="opt_options">
                                  <span class="option-label">{{ subopt.listNo }}</span>
                                  <v-md-preview :text="subopt.option"></v-md-preview>
                                </div>
                              </el-checkbox> 
                            </el-checkbox-group>
                          </div>
                          <div v-else>
                            <div style="display: none;">{{ answerArray = groupjob.title.answer ? groupjob.title.answer.split(',') : [] }}</div>
                            <el-checkbox-group v-model="answerArray" disabled>
                              <el-checkbox :value="opt.listNo" size="large" v-for="(opt, optIdx) in groupjob.title.options" :key="optIdx">
                                <div class="opt_options">
                                  <span class="option-label">{{ opt.listNo }}</span>
                                  <v-md-preview :text="opt.option"></v-md-preview>
                                </div>
                              </el-checkbox> 
                            </el-checkbox-group>
                          </div>
                      </div>
                      <div class="question-header">
                        <div class="el_tag_5">
                          <el-tag class="el_tag_5">选项答案</el-tag>
                        </div>
                        <div class="question-title">
                          <v-md-preview :text="subQu.answer"></v-md-preview>
                        </div>
                      </div>
                      <div class="question-header">
                        <div class="el_tag_5">
                          <el-tag class="el_tag_5" >答案解析</el-tag>
                        </div>
                        <div class="question-title">
                          <v-md-preview :text="subQu.answer_parsing"></v-md-preview>
                        </div>
                      </div> 
                  </div>
                  <div v-else-if="subQu.questionType.includes('判') && (subQu.answer.includes('0') || subQu.answer.includes('1'))">
                    <el-radio-group v-model="subQu.answer" disabled  >
                      <el-radio value="1">A、正确</el-radio>
                      <el-radio value="0">B、错误</el-radio>
                    </el-radio-group>
                    <div class="question-header">
                      <div class="el_tag_5">
                        <el-tag class="el_tag_5">选项答案</el-tag>
                      </div>
                      <div class="question-title">
                          <span v-if="subQu.answer.includes('0')">错误</span>
                          <span v-if="subQu.answer.includes('1')">正确</span>
                          <span v-else style="color: red;"> {{ subQu.answer }} </span>
                      </div>
                    </div>
                    <div class="question-header">
                      <div class="el_tag_5">
                        <el-tag class="el_tag_5" >答案解析</el-tag>
                      </div>
                      <div class="question-title">
                        <v-md-preview :text="subQu.answer_parsing"></v-md-preview>
                      </div>
                    </div> 
                  </div>
                  <div v-else>
                    <div class="question-header"> 
                      <div class="el_tag_5">
                        <el-tag class="el_tag_5" >答案</el-tag>
                      </div>
                      <div class="question-title">
                        <v-md-preview :text="subQu.answer"></v-md-preview>
                      </div>
                    </div>
                    <div class="question-header">
                      <div class="el_tag_5">
                        <el-tag class="el_tag_5" >答案解析</el-tag>
                      </div>
                      <div class="question-title">
                        <v-md-preview :text="subQu.answer_parsing"></v-md-preview>
                      </div>
                    </div> 
                  </div> 
              </div>
            </div>
            <!-- 其他题型（主观题） -->
            <div v-else>
              <div class="question-header"> 
                <div class="el_tag_5">
                  <el-tag class="el_tag_5" >答案</el-tag>
                </div>
                <div class="question-title">
                  <v-md-preview :text="groupjob.answer"></v-md-preview>
                  
                </div>
              </div>
              <div class="question-header">
                <div class="el_tag_5">
                  <el-tag class="el_tag_5" >答案解析</el-tag>
                </div>
                <div class="question-title">
                  <v-md-preview :text="groupjob.answer_parsing"></v-md-preview>
                </div>
              </div> 
            </div> 
            <!-- 试题各类信息 -->
            <div class="question-header">
                <el-tag class="el_tag_5" >知识点</el-tag>
                <span>{{ groupjob.knowledge_point }}</span>
            </div>
            <div class="question-header">
                <el-tag class="el_tag_5" >试题来源</el-tag>
                <span>{{ groupjob.jobbank_source_title }}</span>
            </div>
            <div class="question-header">
                <el-tag class="el_tag_5" >试题能力层次</el-tag>
                <span>{{ groupjob.competence_level }}</span>
            </div>
            <div class="question-header">
                <el-tag class="el_tag_5" >难度能力层次</el-tag>
                <span>{{ groupjob.difficulty_level }}</span>
            </div>
            <div class="question-header">
                <el-tag class="el_tag_5" >版本(套题)</el-tag>
                <span>{{ groupjob.ver }}</span>
            </div>
            <!-- AI 测试结果 -->
            <div v-if="hasAIResults(groupjob)" class="ai-test-result">
              <div class="ai-test-header">
                <el-tag type="success" effect="dark" size="small">AI 解析</el-tag>
              </div>
              <div class="ai-results-container">
                <!-- Doubao AI Result -->
                <div class="ai-result-column">
                  <div class="ai-bot-header">
                    <el-tag type="success" size="small" effect="dark">豆包</el-tag>
                  </div>
                  <div class="ai-reasoning">
                    <div class="ai-section">
                      <div class="ai-label">思考过程：</div>
                      <div class="ai-content">{{ groupjob.aiResult?.doubao?.reasoning || '无' }}</div>
                    </div>
                    <div class="ai-section">
                      <div class="ai-label">最终答案：</div>
                      <div class="ai-answer">{{ groupjob.aiResult?.doubao?.answer || '无' }}</div>
                    </div>
                  </div>
                </div>
                
                <!-- Deepseek AI Result -->
                <div class="ai-result-column">
                  <div class="ai-bot-header">
                    <el-tag type="primary" size="small" effect="dark">Deepseek</el-tag>
                  </div>
                  <div class="ai-reasoning">
                    <div class="ai-section">
                      <div class="ai-label">思考过程：</div>
                      <div class="ai-content">{{ groupjob.aiResult?.deepseek?.reasoning || '无' }}</div>
                    </div>
                    <div class="ai-section">
                      <div class="ai-label">最终答案：</div>
                      <div class="ai-answer">{{ groupjob.aiResult?.deepseek?.answer || '无' }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
      </div>
      <div class="main-content" v-else>
        <span v-if="quesAlldata.code==200">所选套题，无题库信息！！！请确认</span>
        <span v-else>{{quesAlldata.msg}}</span>
      </div>

    </div>


  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, watch, defineProps, onUnmounted } from 'vue';
import { ElMessage,ElMessageBox } from 'element-plus'
import { getJobQuestionsPreview } from '@/api/question'
import {toolsUtils} from "@/utils/tools";
import { ms } from 'element-plus/es/locale';

// 接收的参数
const props = defineProps<{ 
  coursebaseid: string | number
  ,jobbanksourceid: string | number
  , jobbankver: string | number 
  , jobbankid: string | number 
  
}>(); 
const course_base_id = ref(props.coursebaseid);
const jobbank_source_id = ref(props.jobbanksourceid);
const jobbank_ver = ref(props.jobbankver);
const jobbank_id = ref(props.jobbankid);

const datajob = ref([]) as any; 
const quesAlldata = ref([]) as any; 
const answerArray = ref([]) as any;  
const loading = ref(false); 
const loading_preview_lobal = ref(false); 
const loadingText_preview_lobal = ref("题库渲染中，请稍后...");

// AI 测试相关状态
const aiTesting = ref(false); 

// Dialog related code has been removed

const groupedQuestions = ref([]) as any; 

const getGroupedByQuestions = () => {
  const groups: { [key: string]: any[] } = {};
  datajob.value.forEach((job: any) => {
    const type = job.questiontype;
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push( { 'id':job.id });
  });
  groupedQuestions.value=groups
};


const scrollToQuestion = (id: number) => {
  const element = document.querySelector(`[data-question-id="${id}"]`);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

// Edit functionality has been removed



const getJobDataVer = async () => {
  // 根据答案自动勾选选项
  loading.value = true;
  loading_preview_lobal.value = true;
  try {
    const pars = {
      course_base_id: course_base_id.value
      ,jobbank_source_id: jobbank_source_id.value
      ,jobbank_ver: jobbank_ver.value
      ,jobbank_id: jobbank_id.value
    }
    getJobQuestionsPreview(pars).then((msg: any) => {
      datajob.value = msg.data || []
      quesAlldata.value = msg
      // 渲染前处理公式斜杠
      // if (datajob.value?.title?.title) {
      //   datajob.value.title.title = toolsUtils.format2Md(datajob.value.title.title);
      // }
      
      if (datajob.value.length>0) { 
        getGroupedByQuestions()
        setTimeout(() => {
          loading_preview_lobal.value = false
          loading.value = false
        }, 2000)
      }else{
        loading_preview_lobal.value = false
        loading.value = false
      }
      
    }).catch((err: any) => {
      console.log(err)
      loading.value  = false
    })
  } catch (e) {
    ElMessage.error('题库获取失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => { 
  getJobDataVer();//试题详细
});

watch(() => props.coursebaseid, (newVal) => {
  course_base_id.value = newVal;
  getJobDataVer();
});

// 获取每个题型的起始序号
const typeStartNumbers = computed(() => {
  const numbers: { [key: string]: number } = {};
  let currentNumber = 1;
  
  for (const type in groupedQuestions.value) {
    numbers[type] = currentNumber;
    currentNumber += groupedQuestions.value[type].length;
  }
  return numbers;
});

// 根据题型和索引获取题号
const getQuestionNumberByType = (type: any, index: number) => {
  return typeStartNumbers.value[type] + index;
};

// 修改题目显示的序号
// const getQuestionDisplayNumber = (groupjob: any) => {
//   const type = groupjob.questionType;
//   const questions = groupedQuestions.value[type];
//   const index = questions.findIndex((q:any) => q.id === groupjob.id);
//   return getQuestionNumberByType(type, index);
// };
const createGlobalRenderer = () => {
  const queues: Record<string, { type: 'reasoning' | 'answer'; char: string; question: any }[]> = {};
  const results: Record<string, { reasoning: string; answer: string }> = {};
  
  // 我们不再需要每个机器人一个 running 标志，只需要一个全局标志
  let isRunning = false;

  const push = (botName: string, type: 'reasoning' | 'answer', content: string, question: any) => {
    if (!queues[botName]) queues[botName] = [];
    if (!results[botName]) results[botName] = { reasoning: '', answer: '' };

    for (const char of content) {
      queues[botName].push({ type, char, question });
    }

    // 不再调用 process(botName)，而是启动全局的渲染循环
    startGlobalProcessor();
  };

  const startGlobalProcessor = async () => {
    // 如果全局循环已经在运行，则直接返回。新加入的字符会被正在运行的循环处理。
    if (isRunning) return;
    isRunning = true;

    // 只要任何一个队列里还有字符，就继续循环
    while (Object.values(queues).some(q => q.length > 0)) {
      
      // 遍历所有的机器人队列
      for (const botName in queues) {
        if (queues[botName].length > 0) {
          // 从队列中取出一个字符进行处理
          const { type, char, question } = queues[botName].shift()!;
          results[botName][type] += char;

          // 更新 question 对象
          // 注意: 频繁更新整个对象可能导致性能问题，但这忠于你的原版逻辑
          question.aiResult = {
            ...question.aiResult,
            [botName.toLowerCase()]: { ...results[botName] }
          };
        }
      }

      // 在处理完一轮所有机器人的单个字符后，等待一小段时间
      // 这会创建出所有机器人同时打字的效果
      await new Promise(r => setTimeout(r, 20));
    }
    
    // 所有队列都处理完毕，重置标志
    isRunning = false;
  };

  return { push };
};

// 你的其他代码部分保持不变
const GlobalRenderer = createGlobalRenderer();

// 发送单个AI请求
const sendAIRequest = async (question: any, botIds: string[]) => {
  const start = Date.now();
  console.log(`[Multi-Bot] 请求开始，请求的 Bot ID: ${botIds.join(', ')}`);

  try {
    // 请求体现在使用 bot_ids 字段，值为一个数组
    const requestData = {
      title: question.title.title,
      options: question.title.options || [],
      optionNum: question.title.options?.length || 0,
      questionType: question.questiontype,
      bot_ids: botIds // <--- 关键改动：字段名和值类型都变了
    };

    const response = await fetch('https://xczx3.swufe.edu.cn/cz_api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    console.log(`[Multi-Bot] fetch 返回，用时 ${Date.now() - start}ms`);

    if (!response.body) throw new Error('No response body');

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    await (async function readStream() {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const text = decoder.decode(value, { stream: true });
        const lines = text.split('\n').filter(line => line.startsWith('data: '));

        for (const line of lines) {
          try {
            const jsonStr = line.replace('data: ', '').trim();
            if (jsonStr === '[DONE]') continue;

            const data = JSON.parse(jsonStr);
            
            // <--- 关键改动：从数据中动态获取 botName
            const botName = data.botName; 
            if (!botName) {
              console.warn("收到的数据块缺少 'botName' 字段:", data);
              continue; // 如果没有 botName，无法分发，跳过这个数据块
            }

            // 使用从数据流中得到的 botName 来调用 GlobalRenderer.push
            if (data.type === 'reasoning' && data.content) {
              GlobalRenderer.push(botName, 'reasoning', data.content, question);
            } else if (data.type === 'content' && data.content) {
              GlobalRenderer.push(botName, 'answer', data.content, question);
            } else if (data.type === 'error') {
              // 也可以处理后端传来的特定bot的错误
              console.error(`[${botName}] 后端流式错误:`, data.content);
              ElMessage.error(`${botName} AI测试出错: ${data.content}`);
            }

          } catch (e) {
            // 解析错误日志现在是全局的
            console.error(`[Multi-Bot] JSON 解析错误:`, e, "原始行:", line);
          }
        }
      }
    })();

    console.log(`[Multi-Bot] 流处理完成，用时 ${Date.now() - start}ms`);
    // 返回整个 aiResult 对象，因为可能多个bot都更新了
    return question.aiResult;
  } catch (error: unknown) {
    console.error(`统一AI请求出错:`, error);
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    ElMessage.error(`AI测试失败: ${errorMessage}`);
    return null;
  }
};

// 检查是否有AI结果
const hasAIResults = (question: any) => {
  return question.aiResult && (question.aiResult.doubao || question.aiResult.deepseek);
};
 
// 运行AI测试
const runAITest = async (question: any) => {
  try {
    question.aiLoading = true;
    // 保留已有的结果，只重置正在请求的AI
    question.aiResult = question.aiResult || {};
    
    await sendAIRequest(question, ['7516459709579739147', '7516740396136022056']);
    
  } catch (error: unknown) {
    console.error('AI测试出错:', error);
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    ElMessage.error('AI测试过程中出错: ' + errorMessage);
  } finally {
    question.aiLoading = false;
  }
};
</script>

<style lang="scss">
.preview_bank_global{
  /* AI 测试按钮 */
  .ai-test-btn {
    margin-left: 10px;
  }
  
  /* AI 测试相关样式 */
  .ai-results-container {
    display: flex;
    gap: 20px;
    margin-top: 10px;
    
    .ai-result-column {
      flex: 1;
      min-width: 0; /* 防止flex item溢出 */
    }
    
    .ai-bot-header {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  
  .ai-test-result {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px dashed #e4e7ed;
    margin-top: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 15px;
    background-color: #f8f9fa;
    
    .ai-test-header {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .ai-section {
      margin-bottom: 10px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .ai-label {
        font-weight: bold;
        margin-bottom: 5px;
        color: #409eff;
      }
      
      .ai-content {
        background: #fff;
        padding: 10px;
        border-radius: 4px;
        border-left: 3px solid #67c23a;
        white-space: pre-wrap;
      }
      
      .ai-answer {
        font-size: 16px;
        font-weight: bold;
        color: #f56c6c;
      }
    }
  }
  
  .opt_options {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    padding: 4px 0;
  }
  .option-label {
    min-width: 22px;
    text-align: center;
    margin-right: 5px;
    font-weight: bold;
    display: inline-block;
  } 
 
  .question-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: auto;
    margin-left:100px;
    align-items: flex-start;
    margin-top: 10px;
  }

  .opt_options .v-md-preview {
    flex: 1;
    margin: 0;
    word-break: break-all;
  }
  .el_tag_5{
    width: 100px;
  }
  .jobbank-title{
    width: auto; 
    margin-top: 10px;
  }
  .question-title{
    width: auto;
  }



  .v-md-editor-preview{
    .github-markdown-body{
      padding:0px !important
      
    }
    .github-markdown-body blockquote, 
    .github-markdown-body details, 
    .github-markdown-body dl, 
    .github-markdown-body ol, 
    .github-markdown-body p, 
    .github-markdown-body pre, 
    .github-markdown-body table, 
    .github-markdown-body ul{
        margin: 0 !important;
    }
    .katex-html{
      display: none !important;
    }
  }

  .preview-question-bank {
    height: calc(100vh - 80px); 
  }
  .main-content {
    flex: 1;
    overflow-y: auto;
  }
  .question-group {
    background: #fff;
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px #f0f1f2;
    padding: 20px;
    min-width:800px ;
  }
  
  .question-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 10px;
  }
  .question-type {
    color: #409eff;
    font-weight: bold;
    width: auto;
  }
  .question-jobid {
    color: #409eff;
    font-weight: bold;
  }
  
  
  /* Side info styles have been removed */
  .print-btn {
    margin-top: 20px;
    width: 100%;
    background: #409eff;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 8px 0;
    font-size: 16px;
    cursor: pointer;
  }
  .print-btn:hover {
    background: #66b1ff;
  }
  .question-types-card {
    margin-top: 20px;
    border-top: 1px solid #eee;
    padding-top: 15px;
    flex: 1;

    .card-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 12px;
      color: #333;
    }

    .type-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
      flex: 1;
      overflow-y: auto;
      height: calc(100vh - 250px); 
    }

    .type-item {
      margin-bottom: 8px;
    }

    .type-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      padding: 8px 12px;
      background: #f5f7fa;
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        background: #ecf5ff;
      }
    }

    .type-info {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .type-name {
      font-size: 14px;
      font-weight: 500;
    }

    .type-count {
      font-size: 12px;
      color: #909399;
    }

    .sub-questions {
      margin-top: 8px;
    }

    
    .question-numbers-div {
      padding: 0 12px;
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 8px;
      background: #fff;
      padding: 8px;
      border-radius: 4px;
    }

    .question-number-indx {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f5f7fa;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.3s;
      border: 1px solid #e4e7ed;

      &:hover {
        background: #ecf5ff;
        border-color: #409eff;
        color: #409eff;
      }

      &.active {
        background: #409eff;
        color: white;
        border-color: #409eff;
      }
    }
  }
}
</style>
