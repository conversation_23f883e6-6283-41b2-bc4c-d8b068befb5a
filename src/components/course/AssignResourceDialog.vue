<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    fullscreen
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    destroy-on-close
    class="assign-resource-dialog"
    @close="handleClose"
  >
    <!-- 顶部选择器区域 -->
    <div class="top-selectors">
      <div class="left-selector">
        <label>板块选择：</label>
        <el-select
          v-model="selectedPlateId"
          placeholder="请选择板块"
          @change="handlePlateChange"
          style="width: 200px;"
          size="small"
        >
          <el-option
            v-for="item in plateList"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          />
        </el-select>
      </div>
      <div class="right-selector">
        <label>版本选择：</label>
        <el-select
          v-model="selectedVer"
          placeholder="请选择版本"
          @change="handleVerChange"
          style="width: 120px; margin-right: 10px;"
          size="small"
        >
          <el-option
            v-for="item in verList"
            :key="item.ver"
            :label="item.ver"
            :value="item.ver"
          />
        </el-select>
        <label>课程选择：</label>
        <el-select
          v-model="selectedCourseId"
          placeholder="请选择课程"
          @change="handleCourseChange"
          style="width: 300px;"
          size="small"
          filterable
          :disabled="!selectedVer"
        >
          <el-option
            v-for="item in courseList"
            :key="item.id"
            :label="`${item.course_code} ${item.course_name}`"
            :value="item.id"
          />
        </el-select>
      </div>
    </div>

    <div class="dialog-content">
      <!-- 左侧：课程章节结构 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3>课程章节</h3>
          <span class="subtitle">拖拽右侧资源到对应章节</span>
        </div>
        <div class="panel-content">
          <div v-if="loading" style="padding: 20px; text-align: center; color: #999;">
            <el-icon class="is-loading"><Loading /></el-icon>
            正在加载章节数据...
          </div>
          <div v-else-if="!selectedPlateId" style="padding: 20px; text-align: center; color: #999;">
            请先选择板块
          </div>
          <div v-else-if="chapterTreeData.length === 0" style="padding: 20px; text-align: center; color: #999;">
            暂无章节数据
          </div>
          <div v-else style="margin-bottom: 10px; font-size: 12px; color: #666;">
            共 {{ chapterTreeData.length }} 个章节
          </div>
          <div v-if="!loading && selectedPlateId" style="margin-bottom: 10px; font-size: 12px; color: #666;">
            拖拽提示：从右侧资源拖拽到左侧章节
          </div>
          <el-tree
            v-if="!loading"
            ref="chapterTreeRef"
            :data="chapterTreeData"
            :props="chapterTreeProps"
            node-key="id"
            :expand-on-click-node="false"
            :default-expand-all="true"
            class="chapter-tree"
          >
            <template #default="{ node, data }">
              <div
                class="tree-node chapter-drop-zone"
                :class="{ 'drag-over': data.isDragOver }"
                @dragover="handleChapterDragOver($event, data)"
                @dragleave="handleChapterDragLeave($event, data)"
                @drop="handleChapterDrop($event, data)"
              >
                <div class="node-content">
                  <el-icon class="node-icon">
                    <Folder v-if="!data.isLeaf" />
                    <Document v-else />
                  </el-icon>
                  <span class="node-label">{{ node.label }}</span>
                </div>
                <div v-if="data.resources && data.resources.length > 0" class="resource-tags">
                  <el-tag
                    v-for="resource in data.resources"
                    :key="resource.id"
                    :type="getResourceTagType(resource.type)"
                    size="small"
                    closable
                    @close="removeResource(data, resource)"
                  >
                    <el-icon class="tag-icon">
                      <component :is="getResourceIcon(resource.type)" />
                    </el-icon>
                    {{ resource.title }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧：可用资源 -->
      <div class="right-panel">
        <div class="panel-header">
          <h3>可用资源</h3>
          <el-radio-group v-model="resourceFilter" size="small" @change="filterResources">
            <el-radio label="">
              <el-icon><Files /></el-icon>
              全部
            </el-radio>
            <el-radio label="video" :disabled="!availableResourceTypes.includes('video')">
              <el-icon><VideoPlay /></el-icon>
              视频
            </el-radio>
            <el-radio label="dzs" :disabled="!availableResourceTypes.includes('dzs')">
              <el-icon><Reading /></el-icon>
              电子书
            </el-radio>
            <el-radio label="ksdg" :disabled="!availableResourceTypes.includes('ksdg')">
              <el-icon><Document /></el-icon>
              考试大纲
            </el-radio>
            <el-radio label="jc" :disabled="!availableResourceTypes.includes('jc')">
              <el-icon><Reading /></el-icon>
              教材
            </el-radio>
            <el-radio label="job" :disabled="!availableResourceTypes.includes('job')">
              <el-icon><EditPen /></el-icon>
              试题
            </el-radio>
          </el-radio-group>
        </div>
        <div class="panel-content">
          <div v-if="loading" style="padding: 20px; text-align: center; color: #999;">
            <el-icon class="is-loading"><Loading /></el-icon>
            正在加载资源数据...
          </div>
          <div v-else-if="!selectedCourseId" style="padding: 20px; text-align: center; color: #999;">
            请先选择课程
          </div>
          <div v-else-if="filteredResourceTreeData.length === 0" style="padding: 20px; text-align: center; color: #999;">
            暂无资源数据
          </div>
          <div v-else style="margin-bottom: 10px; font-size: 12px; color: #666;">
            共 {{ filteredResourceTreeData.length }} 个资源章节，可拖拽资源到左侧
          </div>
          <el-tree
            v-if="!loading"
            ref="resourceTreeRef"
            :data="filteredResourceTreeData"
            :props="resourceTreeProps"
            node-key="id"
            :expand-on-click-node="false"
            :default-expand-all="true"
            class="resource-tree"
          >
            <template #default="{ node, data }">
              <div
                class="tree-node resource-node"
                :class="{ 'is-resource': data.isResource }"
                :draggable="data.isResource"
                @dragstart="handleResourceDragStart($event, data)"
                @dragend="handleResourceDragEnd($event, data)"
              >
                <el-icon class="node-icon" :class="getResourceIconClass(data.type)">
                  <component :is="getResourceIcon(data.type)" />
                </el-icon>
                <span class="node-label">{{ node.label }}</span>
                <el-tag
                  v-if="data.isResource"
                  :type="getResourceTagType(data.type)"
                  size="small"
                  class="resource-type-tag"
                >
                  {{ getResourceTypeName(data.type) }}
                </el-tag>
              </div>
            </template>
          </el-tree>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="loading || saving">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving" :disabled="loading">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Folder,
  Document,
  VideoPlay,
  Reading,
  EditPen,
  Files,
  Loading
} from '@element-plus/icons-vue';
import {
  getCourseChapterResource,
  getCourseChapterVideos,
  saveCourseResource,
  getCourseChapterDzs,
  getCourseChapterJobs,
  getCourseKsdg,
  getCourseJc,
} from '@/api/kcmgr';
import { getPlateDataForOlsCourseById_op } from '@/api/course';
import { GetJcVer, GetJcList } from '@/api/dzs';

// 定义组件名
defineOptions({
  name: 'AssignResourceDialog'
});

// Props
interface Props {
  modelValue: boolean;
  courseBaseId: number | null;
  courseInfoId: number | null;
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '指定资料'
});

// Emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'close'): void;
  (e: 'save'): void;
}>();

// 类型定义
interface ChapterNode {
  id: string;
  label: string;
  isLeaf: boolean;
  chapterId: number;
  courseInfoId: number;
  resources: ResourceItem[];
  children?: ChapterNode[];
}

interface ResourceNode {
  id: string;
  label: string;
  title: string;
  type: string;
  isResource: boolean;
  course_video_id?: number;
  course_dzs_id?: number;
  course_job_id?: number;
  dzs_chapter_id?: number; // 电子书章节ID
  children?: ResourceNode[];
}

interface ResourceItem {
  id: number;
  title: string;
  type: string;
  course_video_id?: number;
  course_dzs_id?: number;
  course_job_id?: number;
  sn?: number;
}

// Reactive data
const dialogVisible = ref(props.modelValue);
const saving = ref(false);
const loading = ref(false);
const resourceFilter = ref('');

// 左侧板块数据
const plateList = ref<any[]>([]);
const selectedPlateId = ref<number | null>(null);

// 右侧版本和课程数据
const verList = ref<any[]>([]);
const courseList = ref<any[]>([]);
const selectedVer = ref<string>('');
const selectedCourseId = ref<number | null>(null);

// 可用的资源类型（根据左侧选中的板块决定）
const availableResourceTypes = ref<string[]>([]);

// Tree data
const chapterTreeData = ref<ChapterNode[]>([]);
const resourceTreeData = ref<ResourceNode[]>([]);
const filteredResourceTreeData = ref<ResourceNode[]>([]);

// 资源信息映射 (resource_id -> resource_info)
const resourceInfoMap = ref<Map<string, any>>(new Map());

// Tree props
const chapterTreeProps = {
  children: 'children',
  label: 'label'
};

const resourceTreeProps = {
  children: 'children',
  label: 'label'
};

// Tree refs
const chapterTreeRef = ref();
const resourceTreeRef = ref();

// Resource type mappings
const resourceTypeNames: Record<string, string> = {
  video: '视频',
  dzs: '电子书',
  ksdg: '考试大纲',
  jc: '教材',
  job: '试题'
};

const resourceIcons: Record<string, any> = {
  video: VideoPlay,
  dzs: Reading,
  ksdg: Document,
  jc: Reading,
  job: EditPen,
  default: Files
};

const resourceTagTypes: Record<string, string> = {
  video: 'primary',
  dzs: 'success',
  ksdg: 'info',
  jc: 'success',
  job: 'warning'
};

// Methods
const getResourceTypeName = (type: string) => {
  return resourceTypeNames[type] || type;
};

const getResourceIcon = (type: string) => {
  return resourceIcons[type] || resourceIcons.default;
};

const getResourceTagType = (type: string) => {
  return resourceTagTypes[type] || '';
};

const getResourceIconClass = (type: string) => {
  const classMap: Record<string, string> = {
    dzs: 'dzs-icon',
    video: 'video-icon',
    job: 'job-icon'
  };
  return classMap[type] || '';
};

// 当前拖拽的资源数据
let currentDragData: any = null;

// 资源拖拽开始事件
const handleResourceDragStart = (event: DragEvent, data: any) => {
  console.log('=== 资源拖拽开始 ===');
  console.log('拖拽资源:', data);

  if (!data.isResource) {
    event.preventDefault();
    return;
  }

  currentDragData = data;

  // 设置拖拽效果
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/plain', JSON.stringify(data));
  }

  // 添加拖拽样式
  (event.target as HTMLElement)?.classList.add('dragging');
};

// 资源拖拽结束事件
const handleResourceDragEnd = (event: DragEvent, data: any) => {
  console.log('=== 资源拖拽结束 ===');
  console.log('拖拽资源结束:', data);

  currentDragData = null;

  // 移除拖拽样式
  (event.target as HTMLElement)?.classList.remove('dragging');
};

// 章节拖拽悬停事件
const handleChapterDragOver = (event: DragEvent, chapterData: any) => {
  if (!currentDragData || !currentDragData.isResource) {
    return;
  }

  event.preventDefault();
  event.dataTransfer!.dropEffect = 'move';

  // 添加悬停样式
  chapterData.isDragOver = true;
};

// 章节拖拽离开事件
const handleChapterDragLeave = (_event: DragEvent, chapterData: any) => {
  // 移除悬停样式
  chapterData.isDragOver = false;
};

// 章节拖拽放置事件
const handleChapterDrop = (event: DragEvent, chapterData: any) => {
  event.preventDefault();

  console.log('=== 章节拖拽放置 ===');
  console.log('拖拽资源:', currentDragData);
  console.log('目标章节:', chapterData);

  // 移除悬停样式
  chapterData.isDragOver = false;

  if (!currentDragData || !currentDragData.isResource) {
    console.log('拖拽条件不满足');
    return;
  }

  // 执行资源分配
  assignResourceToChapter(currentDragData, chapterData);
};

// 将资源分配到章节的核心逻辑
const assignResourceToChapter = (resource: any, targetNode: any) => {
  console.log('分配资源到节点:', { resource, targetNode });

  // 如果拖拽到章上面，允许分配到章级别
  if (!targetNode.isLeaf) {
    console.log('拖拽到章，允许分配到章级别');
  }

  // 添加资源到目标节点（章或节）
  if (!targetNode.resources) {
    targetNode.resources = [];
  }
  
  // 检查是否已存在
  const resourceId = resource.course_video_id || resource.dzs_chapter_id || resource.course_job_id || resource.id;
  const exists = targetNode.resources.some((r: any) => {
    if (resource.type === 'video' && r.type === 'video') {
      return r.course_video_id === resourceId;
    } else if (resource.type === 'dzs' && r.type === 'dzs') {
      return r.course_dzs_id === resourceId || r.id === resourceId;
    } else if (resource.type === 'job' && r.type === 'job') {
      return r.course_job_id === resourceId || r.id === resourceId;
    }
    return false;
  });

  if (exists) {
    const nodeType = targetNode.isLeaf ? '节' : '章';
    ElMessage.warning(`该资源已存在于此${nodeType}`);
    return;
  }

  // 添加资源到目标节点（章或节）
  const newResource: ResourceItem = {
    id: resourceId, // 对于电子书，这是章节ID
    title: resource.title || resource.label,
    type: resource.type,
    course_video_id: resource.type === 'video' ? resourceId : undefined,
    course_dzs_id: resource.type === 'dzs' ? resource.course_dzs_id : undefined, // 保存电子书ID
    course_job_id: resource.type === 'job' ? resourceId : undefined,
    sn: targetNode.resources.length + 1 // 设置序号
  };

  targetNode.resources.push(newResource);

  // 从右侧资源树中移除该资源
  removeResourceFromTree(resource);

  const nodeType = targetNode.isLeaf ? '节' : '章';
  console.log(`资源分配完成，${nodeType}资源:`, targetNode.resources);
  ElMessage.success(`资源"${resource.title || resource.label}"已添加到"${targetNode.label}"`);
};

// 从右侧资源树中移除资源
const removeResourceFromTree = (resource: any) => {
  console.log('从资源树中移除资源:', resource);

  // 遍历资源树数据，找到并移除该资源
  filteredResourceTreeData.value.forEach(chapter => {
    if (chapter.children) {
      const index = chapter.children.findIndex(child => {
        // 精确匹配：优先使用唯一的 id 进行匹配
        return child.id === resource.id;
      });
      if (index > -1) {
        chapter.children.splice(index, 1);
        console.log(`从章节 ${chapter.label} 中移除了${resource.type}资源`);
      }
    }
  });

  // 同时从原始资源树数据中移除
  resourceTreeData.value.forEach(chapter => {
    if (chapter.children) {
      const index = chapter.children.findIndex(child => {
        // 精确匹配：优先使用唯一的 id 进行匹配
        return child.id === resource.id;
      });
      if (index > -1) {
        chapter.children.splice(index, 1);
      }
    }
  });
};

// Remove resource from chapter
const removeResource = (chapter: any, resource: any) => {
  const index = chapter.resources.findIndex((r: any) => r.id === resource.id && r.type === resource.type);
  if (index > -1) {
    chapter.resources.splice(index, 1);
    ElMessage.success('资源移除成功');
  }
};

// Filter resources
const filterResources = () => {
  console.log('=== 开始过滤资源 ===');
  console.log('resourceTreeData.value:', resourceTreeData.value);
  console.log('resourceFilter.value:', resourceFilter.value);

  // 先获取未分配的资源数据
  const unassignedResourceData = getUnassignedResources();
  console.log('未分配的资源数据:', unassignedResourceData);

  if (!resourceFilter.value) {
    filteredResourceTreeData.value = unassignedResourceData;
  } else {
    filteredResourceTreeData.value = unassignedResourceData.map(chapter => ({
      ...chapter,
      children: chapter.children?.filter((resource: ResourceNode) => resource.type === resourceFilter.value) || []
    })).filter(chapter => chapter.children && chapter.children.length > 0);
  }

  console.log('最终过滤后的数据:', filteredResourceTreeData.value);
  console.log('=== 过滤资源完成 ===');
};

// 获取未分配的资源数据
const getUnassignedResources = () => {
  console.log('=== 获取未分配资源 ===');
  console.log('chapterTreeData.value:', chapterTreeData.value);
  console.log('resourceTreeData.value:', resourceTreeData.value);

  // 收集所有已分配的资源ID
  const assignedVideoIds = new Set<number>();
  const assignedDzsIds = new Set<number>();
  const assignedJobIds = new Set<number>();

  chapterTreeData.value.forEach(chapter => {
    // 处理章级别的资源（如果有）
    if (chapter.resources) {
      chapter.resources.forEach(resource => {
        if (resource.type === 'video' && resource.course_video_id) {
          assignedVideoIds.add(resource.course_video_id);
        } else if (resource.type === 'dzs' && resource.id) {
          // 电子书使用章节ID（resource.id）
          assignedDzsIds.add(resource.id);
        } else if (resource.type === 'job' && resource.id) {
          // 试题使用ID
          assignedJobIds.add(resource.id);
        }
      });
    }

    // 处理节级别的资源
    if (chapter.children) {
      chapter.children.forEach(section => {
        if (section.resources) {
          section.resources.forEach(resource => {
            if (resource.type === 'video' && resource.course_video_id) {
              assignedVideoIds.add(resource.course_video_id);
            } else if (resource.type === 'dzs' && resource.id) {
              // 电子书使用章节ID（resource.id）
              assignedDzsIds.add(resource.id);
            } else if (resource.type === 'job' && resource.id) {
              // 试题使用ID
              assignedJobIds.add(resource.id);
            }
          });
        }
      });
    }
  });

  console.log('已分配的资源ID:', { assignedVideoIds, assignedDzsIds, assignedJobIds });

  // 从资源树中过滤掉已分配的资源
  const result = resourceTreeData.value.map(chapter => {
    const filteredChildren = chapter.children?.filter(resource => {
      if (resource.type === 'video' && resource.course_video_id) {
        return !assignedVideoIds.has(resource.course_video_id);
      } else if (resource.type === 'dzs' && resource.dzs_chapter_id) {
        // 电子书使用章节ID进行过滤
        return !assignedDzsIds.has(resource.dzs_chapter_id);
      } else if (resource.type === 'job' && resource.course_job_id) {
        return !assignedJobIds.has(resource.course_job_id);
      }
      return true;
    }) || [];

    return {
      ...chapter,
      children: filteredChildren
    };
  }).filter(chapter => {
    // 如果章节原本就没有数据，保留章节显示
    // 如果章节原本有数据但现在被过滤空了，删除章节
    const originalChildrenCount = resourceTreeData.value.find(c => c.id === chapter.id)?.children?.length || 0;
    if (originalChildrenCount === 0) {
      return true; // 保留原本就没有数据的章节
    }
    return chapter.children && chapter.children.length > 0; // 只保留还有数据的章节
  });

  console.log('getUnassignedResources 返回结果:', result);
  return result;
};

// filteredResourceTreeData is already reactive, no need for computed

// Watch props
watch(() => props.modelValue, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    loadData();
  }
});

watch(dialogVisible, (newVal) => {
  emit('update:modelValue', newVal);
});

// 获取板块数据
const getPlateData = async () => {
  if (!props.courseInfoId) return;

  try {
    const res = await getPlateDataForOlsCourseById_op({ id: props.courseInfoId });
    if (res.code === 200) {
      plateList.value = res.data || [];
    }
  } catch (error) {
    console.error('获取板块数据失败:', error);
  }
};

// 获取版本数据
const getVerData = async () => {
  try {
    const res = await GetJcVer({});
    if (res.code === 200) {
      verList.value = res.data || [];
      // 默认选中第一个版本
      if (verList.value.length > 0) {
        selectedVer.value = verList.value[0].ver;
        await getCourseData();
      }
    }
  } catch (error) {
    console.error('获取版本数据失败:', error);
  }
};

// 获取课程数据
const getCourseData = async () => {
  if (!selectedVer.value) return;

  try {
    const res = await GetJcList({ ver: selectedVer.value });
    if (res.code === 200) {
      courseList.value = res.data?.list || [];
    }
  } catch (error) {
    console.error('获取课程数据失败:', error);
  }
};

// 创建虚拟章节
const createVirtualChapter = (plate: any) => {
  chapterTreeData.value = [
    {
      id: `virtual_chapter_${plate.id}`,
      label: `${plate.title} - 资源分配`,
      isLeaf: true,
      chapterId: plate.id,
      courseInfoId: props.courseInfoId || 0,
      resources: []
    }
  ];
};

// 板块变化处理
const handlePlateChange = (plateId: number) => {
  selectedPlateId.value = plateId;

  // 根据选中的板块信息决定如何加载章节数据
  const selectedPlate = plateList.value.find(plate => plate.id === plateId);
  if (selectedPlate) {
    // 解析resource字段，设置可用的资源类型
    if (selectedPlate.resource) {
      // resource可能是 "video,dzs" 或单个类型 "video"
      availableResourceTypes.value = selectedPlate.resource.split(',').map((type: string) => type.trim());
    } else {
      // 如果没有resource字段，默认支持所有类型
      availableResourceTypes.value = ['video', 'dzs', 'ksdg', 'jc', 'job'];
    }

    console.log('选中板块:', selectedPlate);
    console.log('可用资源类型:', availableResourceTypes.value);

    // 重置资源过滤器
    resourceFilter.value = '';

    if (selectedPlate.hava_chapter === 1) {
      // 需要分章节，加载真实章节数据
      loadChapterData();
    } else {
      // 不需要分章节，创建虚拟章节
      createVirtualChapter(selectedPlate);
    }

    // 如果右侧两个select都有值，重新加载右侧表格数据
    if (selectedVer.value && selectedCourseId.value) {
      console.log('板块变化，重新加载右侧资源数据');
      loadResourceData();
    }
  }
};

// 版本变化处理
const handleVerChange = async (ver: string) => {
  selectedVer.value = ver;
  selectedCourseId.value = null;
  await getCourseData();
  // 清空资源数据
  resourceTreeData.value = [];
  filteredResourceTreeData.value = [];
};

// 课程变化处理
const handleCourseChange = (courseId: number) => {
  selectedCourseId.value = courseId;
  // 重新加载资源数据
  loadResourceData();
};

// Load data
const loadData = async () => {
  loading.value = true;

  try {
    // 加载板块数据和版本数据
    await Promise.all([
      getPlateData(),
      getVerData()
    ]);

    if (!props.courseBaseId) {
      console.log('没有courseBaseId，使用测试数据');

    // 添加测试章节数据（章节-小节结构）
    chapterTreeData.value = [
      {
        id: 'chapter_174',
        label: '第一章 行政法学的基本范畴',
        isLeaf: false,
        chapterId: 174,
        courseInfoId: 23,
        resources: [],
        children: [
          {
            id: 'section_192',
            label: '0101 行政',
            isLeaf: true,
            chapterId: 192,
            courseInfoId: 23,
            resources: [
              {
                id: 101,
                title: '[第一章] 已分配的测试视频1',
                type: 'video',
                course_video_id: 101,
                sn: 1
              }
            ]
          }
        ]
      },
      {
        id: 'chapter_2',
        label: '第二章 测试空章节',
        isLeaf: false,
        chapterId: 2,
        courseInfoId: 23,
        resources: [],
        children: []
      }
    ];

    // 添加测试资源数据
    resourceTreeData.value = [
      {
        id: 'resource_chapter_1',
        label: '第一章 测试资源章节',
        title: '测试资源章节',
        type: 'chapter',
        isResource: false,
        children: [
          {
            id: 'video_101',
            label: '[第一章] 已分配的测试视频1',
            title: '[第一章] 已分配的测试视频1',
            type: 'video',
            isResource: true,
            course_video_id: 101
          },
          {
            id: 'video_102',
            label: '[第一章] 未分配的测试视频2',
            title: '[第一章] 未分配的测试视频2',
            type: 'video',
            isResource: true,
            course_video_id: 102
          },
          {
            id: 'dzs_201',
            label: '[第一章] 测试电子书1',
            title: '[第一章] 测试电子书1',
            type: 'dzs',
            isResource: true,
            course_dzs_id: 201
          },
          {
            id: 'dzs_202',
            label: '[第一章] 测试电子书2',
            title: '[第一章] 测试电子书2',
            type: 'dzs',
            isResource: true,
            course_dzs_id: 202
          },
          {
            id: 'job_301',
            label: '[第一章] 测试试题1',
            title: '[第一章] 测试试题1',
            type: 'job',
            isResource: true,
            course_job_id: 301
          }
        ]
      },
      {
        id: 'resource_chapter_empty',
        label: '第二章 空章节',
        title: '空章节',
        type: 'chapter',
        isResource: false,
        children: []
      }
    ];

      // 过滤已分配的资源
      filterAssignedResources();
      return;
    }

    // 不再自动加载章节数据，等待用户选择板块
    // await loadChapterData();

    // 只有在有选中课程时才加载资源数据
    if (selectedCourseId.value) {
      await loadResourceData();
      // 数据加载完成后，过滤掉已分配的资源
      filterAssignedResources();
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// Load chapter data
const loadChapterData = async () => {
  try {
    console.log('开始加载章节数据, courseBaseId:', props.courseBaseId);
    const res: any = await getCourseChapterResource({ courseInfoId: props.courseInfoId });
    console.log('章节数据API响应:', res);
    if (res.code === 200) {
      const processedData = processChapterData(res.data || []);
      console.log('处理后的章节数据:', processedData);
      chapterTreeData.value = processedData;
    }
  } catch (error) {
    console.error('加载章节数据失败:', error);
  }
};

// Load resource data
const loadResourceData = async () => {
  // 如果没有选中课程，不加载资源数据
  if (!selectedCourseId.value) {
    resourceTreeData.value = [];
    filteredResourceTreeData.value = [];
    return;
  }

  // 如果没有可用的资源类型，不加载数据
  if (availableResourceTypes.value.length === 0) {
    resourceTreeData.value = [];
    filteredResourceTreeData.value = [];
    return;
  }

  try {
    console.log('开始加载资源数据，可用类型:', availableResourceTypes.value);

    // 根据可用的资源类型动态构建API调用
    const apiCalls: Promise<any>[] = [];
    const resourceTypeMap: string[] = [];

    if (availableResourceTypes.value.includes('video')) {
      apiCalls.push(getCourseChapterVideos({ course_jc_id: selectedCourseId.value }));
      resourceTypeMap.push('video');
    }

    if (availableResourceTypes.value.includes('dzs')) {
      apiCalls.push(getCourseChapterDzs({ course_jc_id: selectedCourseId.value }));
      resourceTypeMap.push('dzs');
    }

    if (availableResourceTypes.value.includes('ksdg')) {
      apiCalls.push(getCourseKsdg({ course_jc_id: selectedCourseId.value }));
      resourceTypeMap.push('ksdg');
    }

    if (availableResourceTypes.value.includes('jc')) {
      apiCalls.push(getCourseJc({ course_jc_id: selectedCourseId.value }));
      resourceTypeMap.push('jc');
    }

    if (availableResourceTypes.value.includes('job')) {
      apiCalls.push(getCourseChapterJobs({ course_jc_id: selectedCourseId.value }));
      resourceTypeMap.push('job');
    }

    // 并行调用所有需要的API
    const results = await Promise.all(apiCalls);

    console.log('API调用结果:', results);

    // 合并所有资源数据
    const allResourceData: any[] = [];

    results.forEach((res, index) => {
      const resourceType = resourceTypeMap[index];
      console.log(`${resourceType}数据:`, res);

      if (res && res.code === 200 && res.data) {
        allResourceData.push(...res.data.map((item: any) => ({
          ...item,
          resourceType
        })));
      }
    });

    console.log('合并后的资源数据:', allResourceData);

    resourceTreeData.value = processResourceData(allResourceData);
    // 数据加载完成后，调用过滤函数更新显示数据
    filterResources();
  } catch (error) {
    console.error('加载资源数据失败:', error);
  }
};

// Process chapter data
const processChapterData = (data: any[]): ChapterNode[] => {
  console.log('处理章节数据:', data);

  if (!data || data.length === 0) {
    console.log('没有章节数据');
    return [];
  }

  // 分离章和节
  const chapters = data.filter(item => item.pid === 0); // 章
  const sections = data.filter(item => item.pid !== 0); // 节

  const chapterMap = new Map<string, ChapterNode>();

  // 先处理章
  chapters.forEach(chapter => {
    console.log('处理章:', chapter);
    const chapterId = `chapter_${chapter.id}`;

    // 格式化章节显示：chapter_code 01/02/03 -> 第一章/第二章/第三章
    const chapterCode = chapter.chapter_code || '';
    const chapterNum = chapterCode ? parseInt(chapterCode, 10) : 0;
    const chapterDisplay = chapterNum > 0 ? `第${chapterNum}章` : '';
    const chapterLabel = chapterDisplay ?
      `${chapterDisplay} ${chapter.chapter_name || ''}`.trim() :
      (chapter.chapter_name || `章节_${chapter.id}`);

    chapterMap.set(chapterId, {
      id: chapterId,
      label: chapterLabel,
      isLeaf: false, // 章不是叶子节点
      chapterId: chapter.id,
      courseInfoId: chapter.course_info_id,
      resources: [],
      children: []
    });
  });

  // 再处理节
  sections.forEach(section => {
    console.log('处理节:', section);
    const sectionId = `section_${section.id}`;
    const parentChapterId = `chapter_${section.pid}`;

    // 格式化节显示
    const sectionCode = section.chapter_code || '';
    const sectionLabel = `${sectionCode} ${section.chapter_name || ''}`.trim() || `节_${section.id}`;

    const sectionNode: ChapterNode = {
      id: sectionId,
      label: sectionLabel,
      isLeaf: true, // 节是叶子节点，可以分配资源
      chapterId: section.id,
      courseInfoId: section.course_info_id,
      resources: []
    };

    // 添加到对应的章下面
    const parentChapter = chapterMap.get(parentChapterId);
    if (parentChapter && parentChapter.children) {
      parentChapter.children.push(sectionNode);
    }
  });

  // 处理资源分配（章和节都可以分配资源）
  data.forEach(item => {
    let targetNode: ChapterNode | undefined;

    if (item.pid === 0) {
      // 处理章级别的资源
      const chapterId = `chapter_${item.id}`;
      targetNode = chapterMap.get(chapterId);
    } else {
      // 处理节级别的资源
      const sectionId = `section_${item.id}`;
      const parentChapterId = `chapter_${item.pid}`;
      const parentChapter = chapterMap.get(parentChapterId);

      if (parentChapter && parentChapter.children) {
        targetNode = parentChapter.children.find(child => child.id === sectionId);
      }
    }

    if (!targetNode) return;

    // 处理已有的资源（JSON数组格式）
    if (item.dzs_ids) {
      try {
        const dzsData = item.dzs_ids;
        if (Array.isArray(dzsData)) {
          dzsData.forEach((dzs: any) => {
            targetNode!.resources.push({
              id: dzs.id,
              title: `电子书_${dzs.id}`,
              type: 'dzs',
              sn: dzs.sn
            });
          });
        }
      } catch (e) {
        console.error('处理dzs_ids失败:', e, item.dzs_ids);
      }
    }

    if (item.video_ids) {
      try {
        const videoData = item.video_ids;
        if (Array.isArray(videoData)) {
          videoData.forEach((video: any) => {
            // 从资源信息映射中获取正确的标题
            const resourceKey = `video_${video.id}`;
            const resourceInfo = resourceInfoMap.value.get(resourceKey);
            const videoTitle = resourceInfo ? resourceInfo.title : `视频_${video.id}`;

            targetNode!.resources.push({
              id: video.id,
              title: videoTitle,
              type: 'video',
              sn: video.sn,
              course_video_id: video.id
            });
          });
        }
      } catch (e) {
        console.error('处理video_ids失败:', e, item.video_ids);
      }
    }

    if (item.job_ids) {
      try {
        const jobData = item.job_ids;
        if (Array.isArray(jobData)) {
          jobData.forEach((job: any) => {
            targetNode!.resources.push({
              id: job.id,
              title: `试题_${job.id}`,
              type: 'job',
              sn: job.sn
            });
          });
        }
      } catch (e) {
        console.error('处理job_ids失败:', e, item.job_ids);
      }
    }
  });

  const result = Array.from(chapterMap.values());
  console.log('最终章节数据:', result);
  return result;
};

// Process resource data
const processResourceData = (data: any[]): ResourceNode[] => {
  const chapterMap = new Map<string, ResourceNode>();

  // 清空并重建资源信息映射
  resourceInfoMap.value.clear();
  
  data.forEach(item => {
    let chapterKey = '';
    let chapterCode = '';
    let resourceId = '';
    let resourceTitle = '';
    let resourceType = item.resourceType;

    // 根据资源类型处理不同的数据结构
    if (resourceType === 'video') {
      chapterCode = item.chapter_code || '';
      // 格式化章节显示：01 -> 第一章, 02 -> 第二章
      const chapterNum = chapterCode ? parseInt(chapterCode, 10) : 0;
      const chapterDisplay = chapterNum > 0 ? `第${chapterNum}章` : '其他';
      chapterKey = item.chapter_name ? `${chapterDisplay} ${item.chapter_name}` : chapterDisplay;
      resourceId = item.course_video_id;
      // 视频标题包含章节信息
      resourceTitle = item.course_video_title ? `${item.course_video_title.split("_")[1]}` : `视频_${item.course_video_id}`;
    } else if (resourceType === 'dzs') {
      chapterCode = item.chap_num || '';
      const chapterNum = chapterCode ? parseInt(chapterCode, 10) : 0;
      const chapterDisplay = chapterNum > 0 ? `第${chapterNum}章` : (item.ver || '其他');
      // 如果有 chap_title，则显示章节标题
      chapterKey = item.chap_title ?
        `${chapterDisplay} ${item.chap_title}`.trim() :
        chapterDisplay;
      resourceId = item.id; // 使用章节的ID，不是course_dzs_id
      // 截取电子书内容的前50个字符作为标题
      resourceTitle = item.chap_content ? item.chap_content.substring(0, 50).replace(/[#\n\r]/g, '').trim() + '...' : `电子书_${item.id}`;
    } else if (resourceType === 'job') {
      chapterKey = item.chapter_mc || (item.ver || '其他');
      chapterCode = item.chapter_no || '';
      resourceId = item.id;
      // 从JSON格式的title中提取标题
      try {
        const titleObj = JSON.parse(item.title || '{}');
        resourceTitle = titleObj.title || `试题_${item.id}`;
      } catch (e) {
        resourceTitle = `试题_${item.id}`;
      }
    }

    if (!chapterKey) return;

    // 创建或获取章节
    if (!chapterMap.has(chapterKey)) {
      chapterMap.set(chapterKey, {
        id: `resource_chapter_${resourceType}_${chapterCode || Date.now()}`,
        label: chapterKey,
        title: chapterKey,
        type: 'chapter',
        isResource: false,
        children: []
      });
    }

    const chapter = chapterMap.get(chapterKey)!;

    if (resourceId && resourceTitle) {
      // 存储资源信息到映射中
      const resourceKey = `${resourceType}_${resourceId}`;
      resourceInfoMap.value.set(resourceKey, {
        id: resourceId,
        title: resourceTitle,
        type: resourceType,
        chapter_name: chapterKey,
        chapter_code: chapterCode
      });

      chapter.children!.push({
        id: resourceKey,
        label: resourceTitle,
        title: resourceTitle,
        type: resourceType,
        isResource: true,
        course_video_id: resourceType === 'video' ? Number(resourceId) : undefined,
        course_dzs_id: resourceType === 'dzs' ? Number(item.course_dzs_id) : undefined, // 保存电子书ID
        course_job_id: resourceType === 'job' ? Number(resourceId) : undefined,
        // 对于电子书，还需要保存章节ID
        dzs_chapter_id: resourceType === 'dzs' ? Number(resourceId) : undefined
      });
    }
  });

  return Array.from(chapterMap.values());
};

// 过滤掉已分配的资源
const filterAssignedResources = () => {
  console.log('开始过滤已分配的资源');

  // 使用统一的过滤逻辑
  filteredResourceTreeData.value = getUnassignedResources();

  console.log('过滤后的资源数据:', filteredResourceTreeData.value);
};

// Handle save
const handleSave = async () => {
  if (!props.courseInfoId) {
    ElMessage.error('缺少课程信息ID');
    return;
  }

  try {
    saving.value = true;

    // 收集所有章节和节的资源分配
    const processNode = async (node: ChapterNode) => {
      // 处理当前节点的资源（章或节都可以有资源）
      if (node.resources && node.resources.length > 0) {
        // 分别处理不同类型的资源
        const dzsResources = node.resources.filter((r: ResourceItem) => r.type === 'dzs');
        const videoResources = node.resources.filter((r: ResourceItem) => r.type === 'video');
        const jobResources = node.resources.filter((r: ResourceItem) => r.type === 'job');

        // 构建保存数据
        const saveData: any = {
          course_info_id: node.courseInfoId,
          ols_course_chapter_id: node.chapterId
        };

        // 添加电子书资源
        if (dzsResources.length > 0) {
          const dzsData = dzsResources.map((r, index) => ({
            id: r.id,
            sn: r.sn || (index + 1)
          }));
          saveData.dzs_ids = JSON.stringify(dzsData);
        }

        // 添加视频资源
        if (videoResources.length > 0) {
          const videoData = videoResources.map((r, index) => ({
            id: r.course_video_id || r.id,
            sn: r.sn || (index + 1)
          }));
          saveData.video_ids = JSON.stringify(videoData);
        }

        // 添加试题资源
        if (jobResources.length > 0) {
          const jobData = jobResources.map((r, index) => ({
            id: r.id,
            sn: r.sn || (index + 1)
          }));
          saveData.job_ids = JSON.stringify(jobData);
        }

        // 调用保存API（章和节都可以保存资源）
        if (Object.keys(saveData).length > 2) { // 除了基本的两个字段外还有其他数据
          const nodeType = node.isLeaf ? '节' : '章';
          console.log(`保存${nodeType}资源:`, node.label, saveData);
          await saveCourseResource(saveData);
        }
      }

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          await processNode(child);
        }
      }
    };

    // 处理所有章节
    for (const chapter of chapterTreeData.value) {
      await processNode(chapter);
    }

    ElMessage.success('保存成功');
    emit('save');
    handleClose();
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败');
  } finally {
    saving.value = false;
  }
};

// Handle close
const handleClose = () => {
  dialogVisible.value = false;
  emit('close');
};
</script>

<style lang="scss" scoped>
.assign-resource-dialog {
  :deep(.el-dialog) {
    margin: 0 !important;
    max-height: 100vh;
    max-width: 100vw;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__header) {
    flex-shrink: 0;
    padding: 20px 20px 0 20px;
  }

  :deep(.el-dialog__body) {
    padding: 20px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  :deep(.el-dialog__footer) {
    flex-shrink: 0;
    padding: 0 20px 20px 20px;
  }
}

.dialog-content {
  display: flex;
  flex: 1;
  min-height: 0;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.left-panel,
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.left-panel {
  border-right: 1px solid #e4e7ed;
}

.panel-header {
  padding: 16px 20px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .subtitle {
    font-size: 12px;
    color: #909399;
  }

  :deep(.el-radio-group) {
    display: flex;
    gap: 12px;

    .el-radio {
      margin-right: 0;

      .el-radio__label {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;

        .el-icon {
          font-size: 14px;
        }
      }
    }
  }
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px;
  min-height: 0;
  max-height: calc(100vh - 300px); /* 为header和footer留出空间 */
}

.chapter-tree,
.resource-tree {
  :deep(.el-tree-node__content) {
    height: auto;
    min-height: 32px;
    padding: 8px 0;
  }
}

.tree-node {
  display: flex;
  align-items: flex-start;
  margin-top:10px;
  justify-content: space-between;
  width: 100%;
  min-height: 32px;
  
  .node-content {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
  }
  
  .node-icon {
    margin-right: 8px;
    font-size: 16px;
    color: #606266;
    
    &.dzs-icon {
      color: #67c23a;
    }
    
    &.video-icon {
      color: #409eff;
    }
    
    &.job-icon {
      color: #e6a23c;
    }
  }
  
  .node-label {
    flex: 1;
    font-size: 14px;
    color: #303133;
    word-break: break-all;
  }
  
  .resource-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-left: 8px;
    max-width: 200px;
    
    .el-tag {
      display: flex;
      align-items: center;
      
      .tag-icon {
        margin-right: 4px;
        font-size: 12px;
      }
    }
  }
  
  .resource-type-tag {
    margin-left: 8px;
  }
}

.resource-node {
  &.is-resource {
    cursor: move;

    &:hover {
      background-color: #f5f7fa;
      border-radius: 4px;
    }

    &.dragging {
      opacity: 0.5;
      transform: scale(0.95);
      transition: all 0.2s ease;
    }
  }
}

.chapter-drop-zone {
  transition: all 0.2s ease;

  &.drag-over {
    background-color: #e6f7ff;
    border: 2px dashed #1890ff;
    border-radius: 4px;

    .node-label {
      color: #1890ff;
      font-weight: 600;
    }
  }
}

.dialog-footer {
  padding: 16px 20px;
  text-align: right;
  border-top: 1px solid #e4e7ed;
}

.top-selectors {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .left-selector {
    display: flex;
    align-items: center;
    gap: 8px;
    padding-left: 20px;

    label {
      font-size: 14px;
      color: #606266;
      white-space: nowrap;
      margin-right: 8px;
    }
  }

  .right-selector {
    display: flex;
    align-items: center;
    gap: 16px;
    padding-right: 20px;

    label {
      font-size: 14px;
      color: #606266;
      white-space: nowrap;
      margin-right: 8px;
    }
  }
}

.panel-filters {
  padding: 10px 20px 8px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafafa;
}
</style>

<script lang="ts">
export default {
  name: 'AssignResourceDialog'
}
</script>
