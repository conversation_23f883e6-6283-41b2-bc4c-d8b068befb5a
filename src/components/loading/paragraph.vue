<template>
  <div v-if="is_loading" class="page_loading animate-pulse">
    <div class="page_loading_right">
      <img src="@/assets/img/default/paragraph.png" alt="" v-for="i in 5">
    </div>
  </div>
</template>
<script setup lang="ts">
import {defineProps} from 'vue'
const props = defineProps<{
  is_loading: boolean
}>()
</script>
<style scoped lang="scss">
.page_loading {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  align-content: center;
  gap: 25px;
  padding: 25px;
  transition: all 0.3s ease;

  .page_loading_left {
    width: 280px;
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    align-content: flex-start;
    gap: 25px;
    overflow: hidden;
    transition: all 0.3s ease;
    img {
      width: 100%;
      height: 160px;
    }
  }

  .page_loading_right {
    flex: 1;
    height: 100%;
    overflow: hidden;
    gap: 25px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    align-content: flex-start;
    img {
      width: 100%;
      height: 160px;
    }
  }
}

</style>
