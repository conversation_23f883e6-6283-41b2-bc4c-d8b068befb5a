// cover some element-ui styles
.el-badge__content.is-fixed {
  top: 11px !important;
  right: 28px !important;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  /*  margin: 0 auto;*/
  margin-left: auto;
  margin-right: auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// 时间选择器
.el-date-editor .el-range-separator {
  width: 7%;
}

//自定义表格开始

.el-table {
  width: 100%;
  overflow: hidden;
  font-size: 12px !important;
}

.el-table th > .cell {
  text-align: center !important;
}

.el-table__header tr,
.el-table__header th {
  padding: 0 !important;
  height: 30px !important;
  margin: 0 !important;
}

.el-table__body tr,
.el-table__body td {
  padding: 0 !important;
  height: 30px !important;
  margin: 0 !important;
}

.el-table td div {
  padding: 2px !important;
  margin: 0 !important;
}

.el-table .cell {
  line-height: 1.5em !important;
  padding: 5px 12px!important;
}

.el-table th.gutter {
  display: table-cell !important;
}

.el-table td > .cell {
  padding-left: 5px !important;
  padding-right: 5px !important;
}

.el-table {
  .current-row { //全局当前行
    td {
      background-color: #bef1ec !important;
    }
  }
}

.el-table {
  .table-selected-row-bg-color {
    td {
      background-color: #bef1ec !important;
    }
  }
}

.el-table__body tr:hover > td { //全局hover行
  background: #ecf6fd !important;
}
.el-table th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
}

.el-table th>.cell {
  padding: 12px 0px !important;
}
.el-input__inner{
  font-size: 13px!important;
}
.el-dialog__body{
  padding: 15px 20px;
}
//自定义表格结束
.el-tabs__item{
  font-size: 13px!important;
}
.el-button{
  font-size: 13px!important;
}