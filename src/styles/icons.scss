@font-face {
  font-family: 'IconFont';
  src: url('../assets/font/iconfont.eot'); /* IE9*/
  src: url('../assets/font/iconfont.eot#iefix') format('embedded-opentype'), /* IE6-IE8 */ url('../assets/font/iconfont.woff') format('woff'),
    /* chrome, firefox */ url('../assets/font/iconfont.ttf') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/ url('../assets/font/iconfont.svg#iconfont') format('svg'); /* iOS 4.1- */
}

.ic {
  font-family: 'IconFont' !important;
  //font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  //-webkit-text-stroke-width: 0.2px;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: auto;
}

.ic-accessory:before {
  content: '\e6dd';
}

.ic-activity:before {
  content: '\e6de';
}

.ic-activity-fill:before {
  content: '\e6df';
}

.ic-add:before {
  content: '\e6e0';
}

.ic-addition-fill:before {
  content: '\e6e1';
}

.ic-addition:before {
  content: '\e6e2';
}

.ic-addpeople-fill:before {
  content: '\e6e3';
}

.ic-addpeople:before {
  content: '\e6e4';
}

.ic-addressbook-fill:before {
  content: '\e6e5';
}

.ic-addressbook:before {
  content: '\e6e6';
}

.ic-barrage-fill:before {
  content: '\e6e7';
}

.ic-barrage:before {
  content: '\e6e8';
}

.ic-browse-fill:before {
  content: '\e6e9';
}

.ic-browse:before {
  content: '\e6ea';
}

.ic-brush:before {
  content: '\e6eb';
}

.ic-brush-fill:before {
  content: '\e6ec';
}

.ic-businesscard-fill:before {
  content: '\e6ed';
}

.ic-businesscard:before {
  content: '\e6ee';
}

.ic-camera-fill:before {
  content: '\e6ef';
}

.ic-camera:before {
  content: '\e6f0';
}

.ic-clock-fill:before {
  content: '\e6f1';
}

.ic-clock:before {
  content: '\e6f2';
}

.ic-close:before {
  content: '\e6f3';
}

.ic-collection-fill:before {
  content: '\e6f4';
}

.ic-collection:before {
  content: '\e6f5';
}

.ic-computer-fill:before {
  content: '\e6f6';
}

.ic-computer:before {
  content: '\e6f7';
}

.ic-coordinates-fill:before {
  content: '\e6f8';
}

.ic-coordinates:before {
  content: '\e6f9';
}

.ic-coupons-fill:before {
  content: '\e6fa';
}

.ic-coupons:before {
  content: '\e6fb';
}

.ic-createtask-fill:before {
  content: '\e6fc';
}

.ic-createtask:before {
  content: '\e6fd';
}

.ic-customerservice-fill:before {
  content: '\e6fe';
}

.ic-customerservice:before {
  content: '\e6ff';
}

.ic-delete-fill:before {
  content: '\e700';
}

.ic-delete:before {
  content: '\e701';
}

.ic-document:before {
  content: '\e702';
}

.ic-document-fill:before {
  content: '\e703';
}

.ic-dynamic-fill:before {
  content: '\e704';
}

.ic-dynamic:before {
  content: '\e705';
}

.ic-editor:before {
  content: '\e706';
}

.ic-eit:before {
  content: '\e707';
}

.ic-emoji-fill:before {
  content: '\e708';
}

.ic-emoji:before {
  content: '\e709';
}

.ic-empty:before {
  content: '\e70a';
}

.ic-empty-fill:before {
  content: '\e70b';
}

.ic-enter:before {
  content: '\e70c';
}

.ic-enterinto:before {
  content: '\e70d';
}

.ic-enterinto-fill:before {
  content: '\e70e';
}

.ic-feedback-fill:before {
  content: '\e70f';
}

.ic-feedback:before {
  content: '\e710';
}

.ic-flag-fill:before {
  content: '\e711';
}

.ic-flag:before {
  content: '\e712';
}

.ic-flashlight:before {
  content: '\e713';
}

.ic-flashlight-fill:before {
  content: '\e714';
}

.ic-flip:before {
  content: '\e715';
}

.ic-flip-fill:before {
  content: '\e716';
}

.ic-fullscreen:before {
  content: '\e717';
}

.ic-group:before {
  content: '\e718';
}

.ic-group-fill:before {
  content: '\e719';
}

.ic-headlines-fill:before {
  content: '\e71a';
}

.ic-headlines:before {
  content: '\e71b';
}

.ic-homepage-fill:before {
  content: '\e71c';
}

.ic-homepage:before {
  content: '\e71d';
}

.ic-integral-fill:before {
  content: '\e71e';
}

.ic-integral:before {
  content: '\e71f';
}

.ic-interactive-fill:before {
  content: '\e720';
}

.ic-interactive:before {
  content: '\e721';
}

.ic-keyboard:before {
  content: '\e722';
}

.ic-label:before {
  content: '\e723';
}

.ic-label-fill:before {
  content: '\e724';
}

.ic-like-fill:before {
  content: '\e725';
}

.ic-like:before {
  content: '\e726';
}

.ic-live-fill:before {
  content: '\e727';
}

.ic-live:before {
  content: '\e728';
}

.ic-lock-fill:before {
  content: '\e729';
}

.ic-lock:before {
  content: '\e72a';
}

.ic-mail:before {
  content: '\e72b';
}

.ic-mail-fill:before {
  content: '\e72c';
}

.ic-manage-fill:before {
  content: '\e72d';
}

.ic-manage:before {
  content: '\e72e';
}

.ic-message:before {
  content: '\e72f';
}

.ic-message-fill:before {
  content: '\e730';
}

.ic-mine:before {
  content: '\e731';
}

.ic-mine-fill:before {
  content: '\e732';
}

.ic-mobilephone-fill:before {
  content: '\e733';
}

.ic-mobilephone:before {
  content: '\e734';
}

.ic-more:before {
  content: '\e735';
}

.ic-narrow:before {
  content: '\e736';
}

.ic-offline-fill:before {
  content: '\e737';
}

.ic-offline:before {
  content: '\e738';
}

.ic-order-fill:before {
  content: '\e739';
}

.ic-order:before {
  content: '\e73a';
}

.ic-other:before {
  content: '\e73b';
}

.ic-people-fill:before {
  content: '\e73c';
}

.ic-people:before {
  content: '\e73d';
}

.ic-picture-fill:before {
  content: '\e73e';
}

.ic-picture:before {
  content: '\e73f';
}

.ic-play:before {
  content: '\e740';
}

.ic-play-fill:before {
  content: '\e741';
}

.ic-playon-fill:before {
  content: '\e742';
}

.ic-playon:before {
  content: '\e743';
}

.ic-praise-fill:before {
  content: '\e744';
}

.ic-praise:before {
  content: '\e745';
}

.ic-prompt-fill:before {
  content: '\e746';
}

.ic-prompt:before {
  content: '\e747';
}

.ic-qrcode-fill:before {
  content: '\e748';
}

.ic-qrcode:before {
  content: '\e749';
}

.ic-redpacket-fill:before {
  content: '\e74a';
}

.ic-redpacket:before {
  content: '\e74b';
}

.ic-refresh:before {
  content: '\e74c';
}

.ic-remind-fill:before {
  content: '\e74d';
}

.ic-remind:before {
  content: '\e74e';
}

.ic-return:before {
  content: '\e74f';
}

.ic-right:before {
  content: '\e750';
}

.ic-scan:before {
  content: '\e751';
}

.ic-select-fill:before {
  content: '\e752';
}

.ic-select:before {
  content: '\e753';
}

.ic-send:before {
  content: '\e754';
}

.ic-service-fill:before {
  content: '\e755';
}

.ic-service:before {
  content: '\e756';
}

.ic-setup-fill:before {
  content: '\e757';
}

.ic-setup:before {
  content: '\e758';
}

.ic-share-fill:before {
  content: '\e759';
}

.ic-share:before {
  content: '\e75a';
}

.ic-shielding-fill:before {
  content: '\e75b';
}

.ic-shielding:before {
  content: '\e75c';
}

.ic-smallscreen-fill:before {
  content: '\e75d';
}

.ic-smallscreen:before {
  content: '\e75e';
}

.ic-stealth-fill:before {
  content: '\e75f';
}

.ic-stealth:before {
  content: '\e760';
}

.ic-success-fill:before {
  content: '\e761';
}

.ic-success:before {
  content: '\e762';
}

.ic-suspend:before {
  content: '\e763';
}

.ic-switch:before {
  content: '\e764';
}

.ic-systemprompt-fill:before {
  content: '\e765';
}

.ic-systemprompt:before {
  content: '\e766';
}

.ic-tailor:before {
  content: '\e767';
}

.ic-task:before {
  content: '\e768';
}

.ic-task-fill:before {
  content: '\e769';
}

.ic-tasklist-fill:before {
  content: '\e76a';
}

.ic-tasklist:before {
  content: '\e76b';
}

.ic-text:before {
  content: '\e76c';
}

.ic-time-fill:before {
  content: '\e76d';
}

.ic-time:before {
  content: '\e76e';
}

.ic-translation-fill:before {
  content: '\e76f';
}

.ic-translation:before {
  content: '\e770';
}

.ic-trash:before {
  content: '\e771';
}

.ic-trash-fill:before {
  content: '\e772';
}

.ic-undo:before {
  content: '\e773';
}

.ic-unlock-fill:before {
  content: '\e774';
}

.ic-unlock:before {
  content: '\e775';
}

.ic-video:before {
  content: '\e776';
}

.ic-video-fill:before {
  content: '\e777';
}

.ic-warning-fill:before {
  content: '\e778';
}

.ic-warning:before {
  content: '\e779';
}

.ic-workbench-fill:before {
  content: '\e77a';
}

.ic-workbench:before {
  content: '\e77b';
}

.ic-search:before {
  content: '\e77c';
}

.ic-searchfill:before {
  content: '\e77d';
}

.ic-qianniu:before {
  content: '\e77e';
}

.ic-publishgoods-fill:before {
  content: '\e77f';
}

.ic-shop-fill:before {
  content: '\e780';
}

.ic-transaction-fill:before {
  content: '\e781';
}

.ic-packup:before {
  content: '\e782';
}

.ic-unfold:before {
  content: '\e783';
}

.ic-wangwang:before {
  content: '\e784';
}

.ic-financial-fill:before {
  content: '\e785';
}

.ic-marketing-fill:before {
  content: '\e786';
}

.ic-shake:before {
  content: '\e787';
}

.ic-decoration-fill:before {
  content: '\e788';
}

.ic-budaidise:before {
  content: '\e789';
}

.ic-qianniudaidise:before {
  content: '\e78a';
}

.ic-questions:before {
  content: '\e78b';
}

.ic-supply:before {
  content: '\e78c';
}

.ic-tools:before {
  content: '\e78d';
}

.ic-int:before {
  content: '\e78e';
}

.ic-commodity:before {
  content: '\e78f';
}

.ic-zhtn:before {
  content: '\e790';
}
