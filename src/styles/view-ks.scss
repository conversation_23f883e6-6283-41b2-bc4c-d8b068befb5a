
.view_content {
  width: 100%;
  font-size: 0.9rem;
  min-height: 300px;
  display: flex;
  justify-content: space-between;
  gap: 25px;
  flex-wrap: wrap;
  flex-direction: row;
}

.view_card {
  width: 270px;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  box-shadow: var(--box-shadow);
  padding: 5px;
  border-radius: 5px;

  .stat {
    width: 100%;
    display: flex;
    align-items: center;
    color: #4183c4;
    justify-content: flex-end;
  }

  .item {
    display: flex;
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    border: 1px solid #dcdfe6;
    background-color: #fff;
    color: #606266;
    cursor: pointer;
    border-radius: 4px;
    margin-bottom: 5px;
    padding: 5px 10px;
    gap: 15px;

    .ques_type {
      width: 100%;
      font-size: 0.9rem;
      font-weight: bold;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .ques_tips {
      width: 100%;
      font-size: 0.6rem;
    }

    .card_box {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-items: center;
      justify-content: flex-start;
      gap: 2px;

      .sn {
        width: 30px;
        height: 30px;
        border: 1px #ccc solid;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        align-items: center;
        justify-content: center;
        font-size: 0.6rem;
        cursor: pointer;
      }

      .sn:hover {
        color: #409eff;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }
    }

  }

  .curr_do {
    border-color: rgba(117, 224, 16, 0.5) !important;
    background-color: rgba(224, 241, 207, 0.5) !important;
  }

  .sn_do_curr {
    border-color: #e03710 !important;
  }

  .do {
    background-color: green;
    color: #fff;
  }

}

.view_body {
  flex: 1;
  padding: 15px;
  overflow-x: hidden;
  max-height: calc(100vh - 120px);
  overflow-y: auto;

  .item {
    padding-top: 30px;
    padding-bottom: 30px;

    .type {
      text-align: center;
      font-weight: bold;
      color: #4183c4;
      font-size: 1rem;
      border-bottom: 1px solid #ccc;
      padding-bottom: 30px;
      margin-bottom: 30px;
    }

    .title {
      width: 100%;
      color: #606266;
      font-size: 1rem;
      font-weight: bold;
      margin-top: 10px;
      margin-bottom: 15px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      flex-wrap: wrap;
      gap: 5px;

      span {
        font-size: 1rem;
      }

      .btn_label {
        font-size: 0.4rem;
        margin-left: 5px;
        text-align: center;
        font-weight: normal;
      }
    }

    .content {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      flex-wrap: wrap;
      gap: 15px;

    }

    .tips {
      width: 100%;
      line-height: 36px;
      color: #4183c4;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
  }
}

.title_body {
  font-size: 1rem;
  font-weight: bold;
  color: #414246;
}

.content_51 {
  list-style-type: none;
}

.content_51 > li {
  width: 100%;
  height: 100%;
  margin-top: 15px;
  margin-bottom: 15px;
}

.content_51 > li > .title_child {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  font-weight: bold;
  color: #606266;
  font-size: 0.9rem;
}

.content_51 > li > .content_child {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 5px;
}

.sub_data_content {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 15px !important;
  flex-wrap: wrap;
}

.sub_data_content > div {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 5px;
}

.textarea_61 {
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
  border-bottom: 1px #ccc solid;
  outline: none;
  width: 150px;
  padding: 11px 11px 10px 16px;
  border-radius: 1px;
  overflow: visible;
}

.textarea_61:focus {
  border-color: rgba(82, 168, 236, 0.8);
  box-shadow: 0 1px 1px rgb(0 0 0 / 8%), 0px 1px 5px rgb(82 168 236 / 60%) inset;
  border-radius: 0px 0px 4px 4px;
}