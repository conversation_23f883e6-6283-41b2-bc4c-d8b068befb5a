.hand {
  cursor: pointer !important;
}

.unhand {
  cursor: default !important;
}

.green {
  color: #42b983;
}

.f_b {
  font-weight: bold !important;
}

.els {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.red {
  color: red !important;
}

.success {
  color: green !important;
}

.warning {
  color: #e6a23c !important;
}

.danger {
  color: red !important;
}

.blue {
  color: #1890ff !important;
}

.gray {
  color: #777 !important;
}

.f_l {
  float: left !important;
}

.f_r {
  float: right !important;
}

.clear {
  clear: both !important;
}

.tdC {
  text-align: center !important;
}

.tdL {
  text-align: left !important;
}

.tdR {
  text-align: right !important;
}

.m_l {
  margin-left: 5px !important;
}

.m_r {
  margin-right: 5px !important;
}

.p_l {
  padding-left: 5px !important;
}

.p_r {
  padding-right: 5px !important;
}

.p_10 {
  padding: 10px !important;
}

.fs_12 {
  font-size: 12px !important;
}

.fs_14 {
  font-size: 14px !important;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.l_h_20 {
  line-height: 20px !important;
}

.l_h_30 {
  line-height: 30px !important;
}

.l_h_32 {
  line-height: 32px !important;
}

.l_h_28 {
  line-height: 28px !important;
}

.h_28 {
  height: 28px !important;
}

.h_30 {
  height: 30px !important;
}

.h_40 {
  height: 40px !important;
}

.h_44 {
  height: 40px !important;
}

.vw_100 {
  width: 100% !important;
}

.vw_50 {
  width: 50% !important;
}

.w_45 {
  width: 45px !important;
}

.w_48 {
  width: 48px !important;
}

.w_60 {
  width: 60px !important;
}

.w_80 {
  width: 80px !important;
}

.w_89 {
  width: 89px !important;
}

.w_90 {
  width: 90px !important;
}
.w100 {
  width: 100%!important;
}
.w_100 {
  width: 100px !important;
}

.w_120 {
  width: 120px !important;
}

.w_125 {
  width: 125px !important;
}

.w_130 {
  width: 130px !important;
}

.m_w_130 {
  min-width: 130px !important;
}

.m_w_140 {
  min-width: 140px !important;
}

.m_w_150 {
  min-width: 150px !important;
}

.w_150 {
  width: 150px !important;
}

.w_155 {
  width: 155px !important;
}

.w_160 {
  width: 160px !important;
}

.w_180 {
  width: 180px !important;
}

.w_200 {
  width: 200px !important;
}

.w_250 {
  width: 250px !important;
}

.w_255 {
  width: 255px !important;
}

.w_300 {
  width: 300px !important;
}

.w_320 {
  width: 320px !important;
}

.w_330 {
  width: 330px !important;
}

.w_340 {
  width: 340px !important;
}

.w_350 {
  width: 350px !important;
}

.w_600 {
  width: 600px !important;
}

.v100 {
  width: 100% !important;
}

.v100_45 {
  width: calc(100% - 45px) !important;
}

.bg_curr {
  background-color: #b9f28f !important;
}

.bg_click {
  background-color: #bef1ec !important;
}

.bg_hover {
  background-color: #eaf1f4 !important;
}

.m_r_5 {
  margin-right: 5px !important;
}

.m_r_10 {
  margin-right: 10px !important;
}

.m_r_0 {
  margin-right: 0px !important;
}

.m_l_5 {
  margin-left: 5px !important;
}

.m_l_10 {
  margin-left: 10px !important;
}

.m_b_5 {
  margin-bottom: 5px !important;
}

.m_t_5 {
  margin-top: 5px !important;
}

.m_t_10 {
  margin-top: 10px !important;
}

.m_t_8_ {
  margin-top: -8px !important;
}

.border_b_1 {
  border-bottom: 1px solid #dfe6ec !important;
}

.m_l-5 {
  margin-left: -5px !important;
}

.m_r_20 {
  margin-right: 20px !important;
}

.mr_10 {
  margin-right: 10px !important;
}

#app {

}

.page {
  margin: 5px;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: flex-start;
  overflow: hidden;

  .header {
    display: flex;
    align-items: center;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 5px;
    width: 100%;
  }

  .body {
    margin-top: 5px;
    width: 100%;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;

    .el-table {
      //background-color: #CCC;
      width: 100%;
      height: 100%;
    }
  }

  .footer {
    margin-top: 5px;
    width: 100%;
    display: flex;
    align-items: center;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;

  }
}
.default {
  color: #ccc;
}

.primary {
  color: #409eff;
}

.info {
  color: #909399;
}

.warning {
  color: #e6a23c;
}

.danger {
  color: red;
}

.success {
  color: green;
}

.el-checkbox__label {
  /*padding-left: 5px !important;*/
}

.el-checkbox {
  margin-right: 5px !important;
}

.text_indent {
  text-indent: 2em;
}

/*
滚动条--------------------------------------------------------
*/
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  border-radius: 8px;
  background-color: #F5F5F5;

}

::-webkit-scrollbar-thumb {
  border-radius: 8px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0.33, #e0e3e5), color-stop(0.55, #5652d9), color-stop(0.88, #e0e3e5));
  transition: 0.3s ease-in-out;
}

::-webkit-scrollbar-track {
  border-radius: 8px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #F5F5F5;
}

/*
--------------------------------------------------------滚动条
*/

/*
光标--------------------------------------------------------
*/
textarea, input {
  caret-color: red !important;
}

/*
--------------------------------------------------------光标
*/

