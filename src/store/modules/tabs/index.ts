import {defineStore} from "pinia"
import tabStateTypes from './types'

export const useTabStore=defineStore("tab",{
 state() {
     return{
      tabsOption:[
        {
         route: '/',
         meta:{
          title:"首页"
         },
         name: 'home'
       }
      ],
      currentIndex: '/home',
      breadcrumbList: [],
     }
 },actions:{
  ADD_TAB(data:any){
   this.tabsOption.push(data)
  }, DELETE_TAB ( route: string) {
   const index = this.tabsOption.findIndex((tab) => tab.route === route)
   this.tabsOption.splice(index, 1)
  },SET_TAB (index: string) {
   this.currentIndex = index
  },CLEAR_TAB(){
   this.tabsOption = [
    {
      route: '/',
      meta:{
       title:"首页"
      },
      name: 'home'
    }
   ]
  }
 },persist: {
   enabled: true
 }
})