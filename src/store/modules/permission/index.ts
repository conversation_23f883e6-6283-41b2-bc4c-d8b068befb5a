import { defineStore } from "pinia"
import router, { routes } from '@/router'
import { RouteRecordRaw } from 'vue-router'
import { getAuthMenu } from '@/api/user'
import Layout from '@/layout/index.vue'

export function generaMenu(routes: any, data: any) {
  const modules = import.meta.glob('/src/views/*/*.vue') 
 
  data.forEach((item: any) => {
    var menu: any = {}
    if (item.component === 'Layout') {
      menu = {
        path: item.path,
        component: Layout,
        redirect: 'noRedirect',
        children: [],
        name: item.name,
        meta: { title: item.title, icon: item.icon, keepAlive: true }
      }
    } else {
      var url = item.component
      var page = modules[`/src/views${url}.vue`]
      menu = {
        path: item.path,
        component: page,
        children: [],
        name: item.name,
        meta: {
          title: item.title,
          icon: item.icon,
          perms: item.perms,
          keepAlive: true
          // roles: ['admin'],
        }
      }
    }
    if (item.children) {
      generaMenu(menu.children, item.children)
    }
    if (routes.some((item: any) => item.name === menu.name) === false) {
      routes.push(menu)
    }
  })
}


export const usePermissionStore = defineStore("Permission", {
  state() {
    return {
      permissions: [],
      accessRoutes: routes, // 可访问路由集合
      routes: new Array<RouteRecordRaw>, // 所有路由集合
      authedRoutes: []
    }
  },
  actions: {
    generateRoutes(roles: any) {
      return new Promise((resolve, reject) => {
        const loadMenuData: any[] = []
        getAuthMenu()
          .then((response: any) => {
            const result = response
            if (result.code !== 200) {
              reject('菜单数据加载失败!')
            } else {
              const data = result.data
              Object.assign(loadMenuData, data)
              //var sing:Array<RouteRecordRaw>=router.getRoutes()
              const cRoutes: Array<RouteRecordRaw> = []
              generaMenu(cRoutes, loadMenuData)
              const accessedRoutes = cRoutes
              router.isReady().then(() => {

                this.accessRoutes = routes.concat(accessedRoutes)
                //commit('setAccessRoutes', accessedRoutes)
                // commit('setRoutes', accessedRoutes)

              })
              resolve(accessedRoutes)
            }

          })
          .catch((error: any) => {
            console.log(error)
          })
            
      })
    }
  }
})