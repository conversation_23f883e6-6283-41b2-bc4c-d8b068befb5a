/* eslint-disable no-restricted-syntax */
import { Module } from 'vuex'
import { RouteRecordRaw } from 'vue-router'
import router, { routes } from '@/router'
import permissionStateTypes from './types'
import RootStateTypes from '../../types'
import {getAuthMenu} from '@/api/user'
import Layout from '@/layout/index.vue'
import { defineAsyncComponent } from 'vue'

const roles = localStorage.getItem('role') || ''



export function generaMenu(routes:any, data:any) {
 const modules=import.meta.glob('/src/views/*/*.vue')

 data.forEach((item:any) => {
   var menu:any = {}
   if (item.component === 'Layout') {
     menu = {
       path: item.path,
       component: Layout,
       redirect: 'noRedirect',
       children: [],
       name: item.name,
       meta: { title: item.title, icon: item.icon,zh:item.zhtitle }
     }
   } else {
     var url = item.component
      var page=modules[`/src/views${url}.vue`]
      //var si=defineAsyncComponent(() => import(`@/views${url}.vue`))
     menu = {
       path: item.path,
       component:page,
       //()=>defineAsyncComponent(() => import('@/views'+url+'.vue')),
       //defineAsyncComponent(() => import(`@/views${url}.vue`)), //(resolve: (...modules: any[]) => void) => require([`@/views${url}`], resolve), // import(`@/views${url}`),  //eslint Error
       children: [],
       name: item.name,
       meta: {
         title: item.title,
         icon: item.icon,
         perms: item.perms,
         zh:item.zhtitle
         // roles: ['admin'],
       }
     }
   }
   if (item.children) {
     generaMenu(menu.children, item.children)
   }
   if (routes.some((item:any) => item.name === menu.name) === false) {
     routes.push(menu)
   }
 })
}



// create a new Store Modules.
const permissionModule: Module<permissionStateTypes, RootStateTypes> = {
  namespaced: true,
  state: {
    permissions: [], // 用户指定局部操作权限
    accessRoutes: routes, // 可访问路由集合
    routes: new Array<RouteRecordRaw>, // 所有路由集合
    authedRoutes: []
  },
  mutations: {
    setAccessRoutes: (state: permissionStateTypes, routescc) => {
      state.accessRoutes = routes.concat(routescc)
    }
  },
  actions: {
   generateRoutes({ commit }, roles) {
    return new Promise(resolve => {
      const loadMenuData: any[] = []
      getAuthMenu()
        .then((response: any) => {
          const result = response
          if (result.code !== '200') {
            
          } else {
            const data = result.data.menuList
            Object.assign(loadMenuData, data)
            //var sing:Array<RouteRecordRaw>=router.getRoutes()
            const cRoutes:Array<RouteRecordRaw> =[]

            generaMenu(cRoutes, loadMenuData)
            
            const accessedRoutes = cRoutes
            router.isReady().then(()=>{
           
            commit('setAccessRoutes', accessedRoutes)
            // commit('setRoutes', accessedRoutes)
            
            })
            resolve(accessedRoutes)
          }
          
        })
        .catch((error: any) => {
          console.log(error)
        })
    })
  }
 },
  getters: {
    getAccessRoutes(state: permissionStateTypes) {
      return state.routes
    }
  }
}
export default permissionModule
