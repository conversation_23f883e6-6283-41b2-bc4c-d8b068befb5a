import { defineStore } from "pinia"
import { userLogin, getInfo,loginAuto } from '@/api/user'
import { setToken, getToken, setUserId, removeToken } from '@/utils/auth'

export const useUserStore = defineStore("user", {
  state: () => {
    return {
      roles: [] as Array<number>,
      token: "",
      userid: "",
      user_type_id: "",
      rolesTitle: "",
      name: "",
      avatar: "",
      introduction: "",
      uid: "",
      zd_bm: ""
    }
  },
  actions: {
    login(userInfo: any) {
      // eslint-disable-line no-unused-vars
      const { username, password } = userInfo
      return new Promise((resolve, reject) => {
       
        userLogin(username.trim(), password).then(res => {
          const { code, data, msg } = res
          if (code === 200 && data) {
            
            this.token = data._t
            this.userid = data.user_id
            setToken(data._t)
            setUserId(data.user_id)
          } else {
            reject(msg)
          }
          resolve(res)
        }).catch(error => {
          reject(error)
        })
          
      })
    }, getInfo() {
      return new Promise((resolve, reject) => {
        getInfo().then(response => {
          const { data } = response
          // console.log(response)
          if (!data) {
            reject('Verification failed, please Login again.')
          }
          // const { roles, name, avatar, introduction } = data
          const roles = data.role.map((x: any) => {
            return x.id
          })

          const rolesTitle = data.role.map((x: any) => {
            return x.title
          })
          const name = data.name
          // const avatar = data.avatar
          // const introduction = data.introduction
          const avatar = ""
          const introduction = ""
          const uid = data.uid
          const zd_bm = data.zd_bm
          const ids = data.id

          // roles must be a non-empty array
          if (!roles || roles.length <= 0) {
            reject('getInfo: roles must be a non-null array!')
            removeToken()
          }
          this.roles = roles
          this.name = name
          this.rolesTitle = rolesTitle
          this.avatar = avatar
          this.uid = uid
          this.user_type_id = data.user_type_id
          this.zd_bm = data.zd_bm
        
          resolve(data)
        }).catch(error => {
          reject(error.msg || error)
          removeToken()
        })
          
      })
    },autoLogin(utoken:string){
      return new Promise((resolve, reject)=>{ 
        loginAuto(utoken).then((response:any)=>{
          const { code, data, msg } = response
          if (code === 200 && data) {
            //loginOld({ username: username.trim(), password: password })
            this.token = data._t
            this.userid = data.user_info_id
            // this.user_type_id = data.userInfo.user_type_id
            //commit('setTOKEN', data.token)
            // commit('setUSERID', data.user_id)
            setToken(data._t)
            setUserId(data.user_info_id)
          } else {
            reject(msg)
            alert(msg)
          }
          resolve(response)
        }).catch((error:any) => {
          reject(error)
        })
      })
    }
  },
})
