/* eslint-disable no-restricted-syntax */
import { Module } from 'vuex'
import { RouteRecordRaw } from 'vue-router'
import router, { routes } from '@/router'
import userStateTypes from './types'
import RootStateTypes from '../../types'
import {userLogin,getInfo} from '@/api/user'
import {setToken,getToken,setUserId} from '@/utils/auth'

const roles = localStorage.getItem('role') || ''
// create a new Store Modules.
const userModule: Module<userStateTypes, RootStateTypes> = {
  namespaced: true,
  state: {
   roles:[],
   token:"",
   userid:"",
   rolesTitle:[],
   name:"",
   avatar:"",
   introduction:"",
   uid:"",
   user_type_id:""
  },
  mutations: {
    setRole: (state: userStateTypes,roles) => {
      state.roles = roles
    },
    setTOKEN: (state: userStateTypes,token) => {
     state.token = token
   },
   setUSERID: (state: userStateTypes,userid) => {
    state.userid = userid
   },
   setROLESTITLE: (state: userStateTypes,rolesTitle) => {
    state.rolesTitle = rolesTitle
   },
   setNAME: (state: userStateTypes,name) => {
    state.name = name
   },
   setAVATAR: (state: userStateTypes,avatar) => {
    state.avatar = avatar
   },
   setUID: (state: userStateTypes,uid) => {
    state.uid = uid
   },
  },
  actions: {
   login({ commit }, userInfo) {
    // eslint-disable-line no-unused-vars
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      userLogin( username.trim(), password ).then(res => {
        const { code,data } = res
        if (code === '200' && data) {
       //loginOld({ username: username.trim(), password: password })
          commit('setTOKEN', data.token)
          commit('setUSERID', data.user_id)
          setToken(data.token)
          setUserId(data.user_id)
        } else {
          reject(data.message)
        }
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
   }, getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo().then(response => {
        const { data } = response
        // console.log(response)
        if (!data) {
          reject('Verification failed, please Login again.')
        }
        // const { roles, name, avatar, introduction } = data
        const roles = data.roles.map((x:any) => { return x.key })
        const rolesTitle = data.roles.map((x:any)=> { return x.title })
        const name = data.name
        const avatar = data.avatar
        const introduction = data.introduction
        const uid = data.uid
        const zd_bm = data.zd_bm
        const ids = data.ids

        // roles must be a non-empty array
        if (!roles || roles.length <= 0) {
          reject('getInfo: roles must be a non-null array!')
        }
        commit('setRole', roles)
        commit('setNAME', name)
        commit('setROLESTITLE', rolesTitle)
        commit('setAVATAR', avatar)
       // commit('SET_INTRODUCTION', introduction)
        commit('setUID', uid)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  },
  getters: {
   getRoles(state: userStateTypes) {
    return state.roles
   },
   getToken(state: userStateTypes) {
    return state.token
   }, getUserid(state: userStateTypes) {
     return state.userid
   },getName(state: userStateTypes) {
    return   state.name
   }
  }
}
export default userModule
