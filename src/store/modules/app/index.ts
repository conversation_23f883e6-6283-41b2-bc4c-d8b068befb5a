import { defineStore } from "pinia"
import cookies from 'js-cookie'
export const useAppStore = defineStore("app", {
  state() {
    return {
      sidebar: {
        opened: true,
        withoutAnimation: false
      },
      device: "desktop",
      siteData: { zd_bm: "", zd_mc: "", dw_dz: "", dw_fzr: "" }
    }
  }, actions: {
    toggleSideBar() {
      this.sidebar.opened = !this.sidebar.opened
      this.sidebar.withoutAnimation = false
      if (this.sidebar.opened) {
        // cookies.set('sidebarStatus', '1')
      } else {
        // cookies.set('sidebarStatus', '0')
      }
    }, close_sidebar(withoutAnimation: boolean) {
      cookies.set('sidebarStatus', '0')
      this.sidebar.opened = false
      this.sidebar.withoutAnimation = withoutAnimation
    }, toggle_device(device: string) {
      this.device = device
    }
  }
})