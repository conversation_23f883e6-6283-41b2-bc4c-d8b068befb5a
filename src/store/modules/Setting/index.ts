import {defineStore} from "pinia"

export const useSettingStore =defineStore("Setting",{
 state() {
     return{
      title:"",
      fixedHeader:true,
      sideBarLogo:true,
      showSettings:null,
      tableHeight: 600, // 表格宽度
      hideHeader: false,
      lang: '/zh-CN'
     }
 },actions:{
   toToggleHeader() {
    this.hideHeader = !this.hideHeader
  },
  toToggleFixedHeader() {
   this.fixedHeader = !this.fixedHeader
  },
  toToggleSidebarLogo() {
   this.sideBarLogo = !this.sideBarLogo
  },
  toToggleLang(payload:any) {
   this.lang = payload.lang
  }
 }
})