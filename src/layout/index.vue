<!--
 * @Author: GeekQiaQia
 * @Date: 2022-02-21 13:45:30
 * @LastEditTime: 2022-02-21 15:24:34
 * @LastEditors: GeekQiaQia
 * @Description: 
 * @FilePath: /test-vue3/src/layout/index.vue
-->
<template>
  <div :class="classObj" class="app-wrapper">
   

   
    <Sidebar class="sidebar-container" />
  
    <div class="main-container">
        <navbar  />
      <AppMain />
    </div>
  
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, computed, toRefs, ref, watchEffect } from 'vue'

import Navbar from './components/Navbar.vue'
import Sidebar from './components/Sidebar/index.vue'
import AppMain from './components/AppMain.vue'
import { useAppStore } from '@/store/modules/app'
import { usePermissionStore } from '@/store/modules/permission'
import { useTabStore } from '@/store/modules/tabs'
import { useSettingStore } from '@/store/modules/Setting'

export default defineComponent({
  name: 'Layout',
  components: {
    Navbar,
    Sidebar,
    AppMain,
    
  },
  setup() {
    const appStore = useAppStore()
    const tabStore=useTabStore()
    const settingStore=useSettingStore()
    const permissionStore=usePermissionStore()


    const originalStyle = ref('')
    const colors = reactive({
      primary: '#fff'
    })
    

   // const showSetting = computed(() => store.state.settingsModule.showSettings)
    const opened = computed(() =>appStore.sidebar.opened)
    const hideHeader = computed(() => settingStore.hideHeader)
    const fixedHeader = computed(() => settingStore.fixedHeader)

    const device = computed(() => appStore.device)
   // const withoutAnimation = computed(() => store.getters['appModule/getSidebarAnimation'])
    const originalStylesheetCount = computed(() => document.styleSheets.length || -1)
    const classObj = computed(() => ({
      hideSidebar: !opened.value,
      openSidebar: opened.value,
    //  withoutAnimation: withoutAnimation.value,
      mobile: device.value === 'mobile'
    }))
    /**
     * @description 监听device && opend
     * */



    /**
     * @description 切换内容显示
     */
    const handleHeaderChange = () => {
      // 改变state
     // store.dispatch('settingsModule/toToggleHeader')
     settingStore.toToggleHeader()
    }
    /**
     * @description 是否固定头部
     */

    const handleFixedHeaderChange = () => {
      // 改变state
     // store.dispatch('settingsModule/toToggleFixedHeader')
     settingStore.toToggleFixedHeader()
    }
    const handleSidebarLogoChange = () => {
      // 改变state
     // store.dispatch('settingsModule/toToggleSidebarLogo')
     settingStore.toToggleSidebarLogo()
    }


    return {
     // opened,
      device,
      hideHeader,
      fixedHeader,
      //handleClickOutside,
      handleHeaderChange,
      handleFixedHeaderChange,
      handleSidebarLogoChange,
      classObj,
      ...toRefs(colors)
    }
  }
})
</script>


<style lang="scss" scoped>
@import '@/styles/mixin.scss';
@import '@/styles/variables.scss';
.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;
  overflow: auto;
  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}
.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}
.setting-item {
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  .setting-draw-title {
    margin-bottom: 12px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    line-height: 22px;
  }
  .divider {
    width: 100%;
    display: flex;
    clear: both;
    min-width: 100%;
    margin: 10px 0;

    box-sizing: border-box;
    padding: 0px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    border-top: 1px solid rgba(0, 0, 0, 0.09);
    list-style: none;
    line-height: 1.5715;
  }
}
.hide-header {
  display: none;
}
.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 63px);
}
.mobile .fixed-header {
  width: 100%;
}
</style>
