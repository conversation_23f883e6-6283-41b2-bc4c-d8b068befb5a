<template>
    <section class="app-main">
        <el-tabs id="Tabs" class="top_tabs" v-model="currentIndex" type="card" @tab-click="clickTab"
            @tab-remove="removeTab">
            <el-tab-pane v-for="item in tabsOption" :key="item.route"
                :closable="item.route !== '/home' && item.route !== '/'" :label="item.meta.title" :name="item.route" />
        </el-tabs>
        <div class="router_main">
            <router-view v-if="$route.meta.keepAlive" v-slot="{ Component }">
                <div name="fade" mode="out-in">
                    <component :is="Component" :key="$route.path" />
                </div>
            </router-view>

            <router-view v-if="!$route.meta.keepAlive" v-slot="{ Component }">
                <div name="fade" mode="out-in">
                    <keep-alive>
                        <component :is="Component" :key="$route.path" />
                    </keep-alive>
                </div>
            </router-view>
        </div>
    </section>
</template>
<script lang="ts">
import { computed, defineComponent, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAppStore } from '@/store/modules/app';
import { usePermissionStore } from '@/store/modules/permission';
import { useTabStore } from '@/store/modules/tabs';
import { useSettingStore } from '@/store/modules/Setting';

export default defineComponent({
    name: 'AppMain',
    setup() {
        // store 中获取当前路由以及所有的路由对象；
        const appStore = useAppStore();
        const tabStore = useTabStore();
        const settingStore = useSettingStore();
        const permissionStore = usePermissionStore();

        const tabsOption = computed(() => tabStore.tabsOption)
        const currentIndex = computed(() => tabStore.currentIndex);
        const lang = computed(() => settingStore.lang);

        const router = useRouter();

        // mothods
        /**
         * @description 移除tab
         * */
        const removeTab = (tabName: string) => {
            if (tabName === '/home' || tabName === '/') {
                return;
            }
            //store.commit('tabModule/DELETE_TAB', tabName)
            tabStore.DELETE_TAB(tabName);
            if (currentIndex.value === tabName) {
                if (tabsOption.value && tabsOption.value.length) {
                    //  store.commit('tabModule/SET_TAB', tabsOption.value[tabsOption.value.length - 1].route)
                    tabStore.SET_TAB(tabsOption.value[tabsOption.value.length - 1].route);
                    router.replace({ path: currentIndex.value });
                } else {
                    router.replace({ path: '/' });
                }
            }
        };
        /**
         * @description 点击tab
         */
        const clickTab = (tabName: { paneName: string }) => {
            // eslint-disable-next-line no-console
            // store.commit('tabModule/SET_TAB', tabName.paneName)
            tabStore.SET_TAB(tabName.paneName);
            router.replace({ path: currentIndex.value });
        };
        onMounted(() => {

        })
        return {
            tabsOption,
            lang,
            currentIndex,
            removeTab,
            clickTab,
        };
    },
});
</script>
<style scoped>
.app-main {
    /*50 = navbar  */
    min-height: calc(100vh - 55px);
    width: 100%;
    position: relative;
    overflow: hidden;
    background-color: white;
}

.router_main {
    height: calc(100vh - 95px);
    overflow: auto;
}

.fixed-header+.app-main {
    padding-top: 50px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
    .fixed-header {
        padding-right: 15px;
    }
}

.top_tabs .el-tabs__header {
    margin-bottom: 0px !important;
}
</style>
