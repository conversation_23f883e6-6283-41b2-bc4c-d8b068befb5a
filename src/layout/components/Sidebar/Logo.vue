<template>
  <div class="sidebar-logo-container">
    <transition name="sidebarLogoFade">
      <router-link v-if="isOpen" key="collapse" class="sidebar-logo-link" to="/">
        <img style="margin-top: -5px;" src="https://apps.swufe-online.com/file/images/logo_write.png" class="sidebar-logo" />
        <h1 class="sidebar-title" >{{ title }}</h1>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <img style="margin-top: -5px;" src="https://apps.swufe-online.com/file/images/logo_write.png" class="sidebar-logo" />
        <h1 class="sidebar-title" >{{ title }}</h1>
      </router-link>
    </transition>
  </div>
</template>
<script lang="ts">
import { computed, defineComponent, reactive, toRefs, watch,ref } from 'vue'
import logo from '@/assets/xclogo.fd4b6a73.png'
import { useUserStore } from '@/store/modules/user';
import { useAppStore } from '@/store/modules/app';
//import { useStore } from '../../../store/index'

export default defineComponent({
  name: 'SidebarLogo',
  setup() {
    var userStore = useUserStore()
    var app = useAppStore()
    // const store = useStore()
    // eslint-disable-next-line no-console
    const reactiveData = reactive({
      title: "自考考试课程资源"
    })
    const isOpen = ref(true)
    var app_name = '自考考试课程资源管理系统'
   
    isOpen.value=app.sidebar.opened

    console.log("app", app.sidebar.opened)
    reactiveData.title = app_name


    watch(isOpen, (newValue, oldValue) => {
      console.log('newValue:', newValue)
      console.log('oldValue:', oldValue)
    })
    return {
      logo,
      isOpen,
      ...toRefs(reactiveData)
    }
  }
})
</script>
<style lang="scss">
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 50px;
  line-height: 50px;
  background: #1F2D3D;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 40px;

      vertical-align: middle;
      margin-right: 12px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      line-height: 50px;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
