<template>
    <div :class="{ 'has-logo': showLogo }">
        <logo v-if="showLogo" :collapse="isCollapse" />
        <el-input prefix-icon="Search" v-model="key" placeholder="搜索" class="menu_search_input" @change="searchMenu()" />
        <el-scrollbar wrap-class="scrollbar-wrapper">
            <el-menu :default-openeds="openeds"  v-if="refreshTable" :router="true" 
                :default-active="activeMenu" class="el-menu-vertical" :collapse="!isCollapse" background-color="#304156"
                text-color="#fff">
                <!--递归路由对象-->
                <sidebar-item v-for="route in routes" :key="route.path" :index="route.path" :item="route" :base-path="route.path" />
            </el-menu>
        </el-scrollbar>
    </div>
</template>
<script lang="ts">
import { computed, defineComponent, onMounted,nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { isExternal } from '@/utils/validate'
import sidebarItem from '@/layout/components/Sidebar/sidebarItem.vue'
import logo from './Logo.vue'
import { useAppStore } from '@/store/modules/app'
import { usePermissionStore } from '@/store/modules/permission'
import { useTabStore } from '@/store/modules/tabs'
import { useSettingStore } from '@/store/modules/Setting'
import { ref } from 'vue'
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
export default defineComponent({
    components: {
        logo,
        sidebarItem
    },
    setup() {
        const route = useRoute()

        const appStore = useAppStore()
        const tabStore = useTabStore()
        const settingStore = useSettingStore()
        const permissionStore = usePermissionStore()
        const key = ref('')
        const showLogo = computed(() => settingStore.sideBarLogo)
        const routes = computed(() => permissionStore.accessRoutes)
        console.log(`output-> routes`, routes)
        var openeds = ref(['']) as any
        var uniqueopened=ref(false)
        var refreshTable = ref(true)
       
        var i=0;
        var index=[] as any
        setTimeout(() => {
          //openeds.value.push('/jk')
          routes.value.forEach((e:any)=>{
             
              if(e['children']?.length>1){
                i++;
                index.push(e['path'])
              }
            })
           
            if(i==1 && index.length>0){
             openeds.value.push(index[0])
             refreshTable.value=false
             nextTick().then(()=>{
              refreshTable.value=true
             })
            
            
            
            }
        }, 100);
       
       
        const isCollapse = computed(() => appStore.sidebar.opened)
        const activeMenu = computed(() => tabStore.currentIndex)
        
        
        const router = useRouter();
        onMounted(() => {
            const routePath = route.path
            tabStore.SET_TAB(routePath)
            setTimeout(() => {
              uniqueopened.value=true
            }, 500);
            //store.commit('tabModule/SET_TAB', routePath)
        })

        // methods
        // eslint-disable-next-line consistent-return
        const resolvePath = (routePath: string) => {
            if (isExternal(routePath)) {
                return routePath
            }
        }
        const searchMenu = () => {
            console.log(routes.value)
            var isFind = 0
            routes.value.forEach((e: any) => {
                e.children?.forEach((c: any) => {
                    if (c?.meta?.title.indexOf(key.value) > -1) {
                        router.push(c.path)
                        throw Error()
                        isFind++
                    }
                })
            })
            if (isFind == 0) {
                ElMessage({
                    message: "未找到！"
                });
            }
        }
        return {
            activeMenu,
            resolvePath,
            routes,
            showLogo,
            isCollapse,
            refreshTable,
            key,
            searchMenu,
            openeds,
            uniqueopened
        }
    }
})
</script>
<style>
.menu_search_input {
    border-width: 0px;
    border-bottom: 1px solid #606266;
    color: #fff !important;
}

.menu_search_input input {
    color: #fff !important;
}

.menu_search_input .el-input__wrapper {
    border-radius: 0px !important;
    background: #545C64;
    border: 0px solid #fff !important;
    border-bottom: 1px solid #CDD0D6;
    box-shadow: 0 0 0 0px !important;
    color: #fff !important;
}
</style>
<style  scoped>
.el-menu-vertical:not(.el-menu--collapse) {
    width: 200px;
    min-height: 400px;
    text-align: left;
}

.is-active {
    background-color: #263445 !important;
}
</style>
