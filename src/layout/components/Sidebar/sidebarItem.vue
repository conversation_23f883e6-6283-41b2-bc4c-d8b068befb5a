<template>
  <template v-if="hasOneShowingChild(item.children, item) && !item.meta.hidden">
    <el-menu-item :key="onlyOneChild.path" :index="onlyOneChild.path" :route="onlyOneChild.path"
      @click="clickMenu(onlyOneChild.path, onlyOneChild.meta.title)">
      <i class=" el-icon">
        <component class="icons el-icon" :is="onlyOneChild.meta.icon" size='10' />
      </i>
      <template #title>
        <span>{{ onlyOneChild.meta.title }}
          <a href="#" :name="item.meta.title"></a>
        </span>
      </template>
    </el-menu-item>
  </template>
  <el-sub-menu v-else-if="!item.meta.hidden" ref="subMenu" index="1" popper-append-to-body>
    <template #title>
      <i class=" el-icon" v-if="item.meta.icon">
        <component class="icons el-icon" :is="item.meta.icon" size='10' />
      </i>
      <span>{{ item.meta.title }}<a href="#" :name="item.meta.title"></a></span>

    </template>
    <sidebar-item v-for="child in item.children" :index="child.path" :key="child.path" :is-nest="true" :item="child"
      :base-path="child.path" class="nest-menu" />
  </el-sub-menu>
</template>
<script lang="ts">
import { defineComponent, onMounted, ref, toRef, computed } from 'vue'
import { UpdateMenuLog } from '@/api/user'

import { useRouter } from 'vue-router';
//import { useStore } from '@/store/index'

interface childType {
  path: string
  name?: string
  component: Function
  meta: {
    title: Object
    icon: string
    hidden?: boolean
    [key: string]: any
  }
}
export default defineComponent({
  name: 'SidebarItem',
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNested: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  setup(props) {

    // 是否只有一个孩子
    const onlyOneChild = ref()
    //  const store = useStore()

    // 析构获取 props 属性 basePath
    const basePath = toRef(props, 'basePath')
    //  const lang = computed(() => store.getters['settingsModule/getLangState'])
    onMounted(() => {
      // eslint-disable-next-line no-console
      // console.log('basePath.value', basePath.value)

    })
    // methods
    /**
     * @description 展示只有一个孩子的情况
     */
    const hasOneShowingChild = (children: childType[] = [], parent: any) => {
      // RouteRecordRaw 只能在meta中配置额外属性，过滤是否展示路由；
      // if (children) {
      const showingChildren = children.filter((item) => {
        // 如果meta 配置隐藏该路由，则返回false;

        if (item?.meta?.hidden) {
          return false
        }
        onlyOneChild.value = item
        return true
      })

      // 判断当前路由，是否有孩子children，以及孩子个数；
      if (showingChildren.length === 1) {
        return true
      }

      // 如果没有孩子，则展示父级路由；
      if (showingChildren.length === 0) {
        onlyOneChild.value = { ...parent, noShowingChildren: true }
        return true
      }

      return false
    }

    const router = useRouter();

    // 获取当前路由对象

    const clickMenu = (path: string, title: string) => {
      const currentRoute = router.currentRoute.value;
      console.log(currentRoute);
      if (currentRoute.path === path) {
        return;

      }
      UpdateMenuLog({ path, title }).then((res: any) => {
        console.log('UpdateMenuLog', res)
      }).catch((err: any) => {
        console.error('UpdateMenuLog error', err)
      })
    }
    return {
      onlyOneChild,
      hasOneShowingChild,
      clickMenu
      //lang
      // resolvePath,
    }
  }
})
</script>
<style scoped></style>
