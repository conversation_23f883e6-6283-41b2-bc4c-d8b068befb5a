<template>
  <div class="navbar">
    <el-header height="50px">
      <hamburger id="Hamburger" :is-active="opened" class="hamburger-container" @toggleClick="toggleSideBar"/>
      <breadcrumb class="breadcrumb-container"/>

      <div class="right-menu">
        <div style="display: flex;align-items: center;">
          <el-icon v-if="appStore.siteData && appStore.siteData.zd_bm" @click="showTips()"
              style="color:#909399;font-size:20px;margin-right:10px;cursor: pointer;">
            <InfoFilled/>
          </el-icon>
        </div>
        <el-dropdown class="avatar-container" trigger="hover">
          <div class="avatar-wrapper">
            <el-avatar
                src="https://apps.swufe-online.com/file/vueup/Avatar/1584012665.jpg?imageView2/1/w/80/h/80"></el-avatar>
            <div class="nickname">{{ nickname }}</div>
          </div>
          <template #dropdown>
            <el-dropdown-menu class="user-dropdown">
              <router-link to="/">
                <el-dropdown-item>首页</el-dropdown-item>
              </router-link>

              <el-dropdown-item divided>
                <span style="display: block" @click="logout">注销</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

  </div>
</template>
<script lang="ts">
import {ElMessageBox} from 'element-plus'
import {defineComponent, computed, ref, onMounted} from 'vue';
import {Message, FullScreen, BottomLeft} from '@element-plus/icons-vue';
import {useRouter} from 'vue-router';
import Hamburger from '@/components/Hamburger/Hamburger.vue';
import Breadcrumb from '@/components/Breadcrumb/index.vue';
import {removeToken, removeUserId} from '@/utils/auth';
import {useAppStore} from '@/store/modules/app';
import {useUserStore} from '@/store/modules/user';
import {useTabStore} from '@/store/modules/tabs';
import cookies from "js-cookie";

export default defineComponent({
  name: 'Navbar',
  components: {
    Hamburger,
    Breadcrumb,
    Message,
    FullScreen,
    BottomLeft,
  },
  props: {
    primary: {
      default: '#fff',
      type: String,
    },
  },
  setup() {
    const router = useRouter();
    const appStore = useAppStore();
    const userStore = useUserStore();
    const tabStore = useTabStore();
    const siteData = ref([{
      zd_bm: "",
      zd_mc: "",
      dw_dz: "",
      dw_fzr: ""
    }])
    const opened = computed(() => appStore.sidebar.opened);
    const fullScreen = ref(false);
    const siteCode = ref("")
    // const messageNum = computed(() => store.getters['messageModule/getMessageNum'])
    //const lang = computed((): string => store.getters['settingsModule/getLangState'])
    const nickname = computed(() => userStore.name);
    const dialogVisible = ref(false)
    const selectDisabled = ref(false)
    // methods
    const toggleSideBar = () => {
      // store.dispatch('appModule/toggleSideBar')
      appStore.toggleSideBar();
    };

    const logout = () => {
      // clear()
      ElMessageBox.confirm(
          '是否注销账号?',
          '提示',
          {
            confirmButtonText: 'OK',
            cancelButtonText: 'Cancel',
            type: 'warning',
          }
      )
          .then(() => {
            removeToken();
            removeUserId()
            tabStore.CLEAR_TAB();
            cookies.remove('selected_zd_bm')
            router.replace('/login');
          })
          .catch(() => {

          })

    };
    const hendleChange = (v: any) => {
      var zd = siteData.value.filter(x => {
        return x.zd_bm == v
      })[0]
      if (zd) {
        appStore.siteData = zd
      } else {
        appStore.siteData = {
          zd_bm: "",
          zd_mc: "",
          dw_dz: "",
          dw_fzr: ""
        }
      }
      console.log('appStore.siteData', appStore.siteData)
      cookies.set('selected_zd_bm', appStore.siteData.zd_bm)
    }
    const showTips = () => {
      var obj = appStore.siteData
      ElMessageBox.alert(
          '<p><strong>(' + obj.zd_bm + ')' + obj.zd_mc + '</strong></p>'
          + '<p>助学点地址：' + obj.dw_dz + '</p>'
          + '<p>负责人：' + obj.dw_fzr + '</p>'
          ,
          '助学点信息',
          {
            dangerouslyUseHTMLString: true,
          }
      )
    }
    onMounted(() => {
    })

    return {
      //messageNum,
      fullScreen,
      nickname,
      //lang,
      toggleSideBar,
      opened,
      logout,
      siteData,
      siteCode,
      hendleChange,
      appStore,
      showTips,
      dialogVisible,
      selectDisabled
    };
  },
});
</script>
<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.28);
  z-index: 1;

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .nickname {
    float: right;
    padding: 0px 25px 0px 25px;
    line-height: 40px;
    outline: none;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;

    &:focus {
      outline: none;
    }

    .right-menu-box {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .message-badge {
      .is-fixed {
        top: 12px !important;
        right: 28px !important;
      }

      .message {
        border: none;
        padding: 5px 20px;

        i {
          background-color: transparent;
          border: none;
          color: #2c3e50;
          font-size: 25px;
        }
      }
    }

    .full-screen {
      background-color: transparent;
      border: none;
      padding: 5px 20px;

      i {
        background-color: transparent;
        border: none;
        color: #2c3e50;
        font-size: 28px;
      }
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;
        cursor: pointer;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.site_select_box {
  min-width: 300px;
  .el-input__wrapper {
    box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset !important;
    border-left: 1px solid var(--el-input-border-color, var(--el-border-color));
    border-radius: 0px;
    border-right: 1px solid var(--el-input-border-color, var(--el-border-color));
    margin-right: 15px;
    height: 50px;
    width: 300px;
  }
}
</style>