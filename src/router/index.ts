import {
  createRouter,
  createWebHistory,
  createWebHashHistory,
  NavigationGuardNext,
  RouteLocationNormalized,
  RouteRecordRaw
} from 'vue-router'
import { ElMessage } from 'element-plus'
import Home from '@/views/home.vue'
import Vuex from '@/views/login/login.vue'
import { setToken, getToken, removeToken } from '@/utils/auth'

import { useUserStore } from '@/store/modules/user'
import { useAppStore } from '@/store/modules/app'
import { usePermissionStore } from '@/store/modules/permission'

import layout from '@/layout/index.vue'
import { defineAsyncComponent } from 'vue'
import { useTabStore } from '@/store/modules/tabs'

import { store } from '@/store/index'

export const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    component: layout,
    meta: {
      title: "首页",
    },
    children: [
      {
        path: '/',
        component: () => import('@/views/home.vue'),
        name: "home",
        meta: {
          title: "首页",
          icon: 'HomeFilled'
        }
      }

    ]
  }, {
    path: '/login',
    name: '登录',
    component: Vuex,
    meta: {
      hidden: true,
      hiddenTab: true
    }
  }, {
    path: '/temp_question',
    name: '临时题库',
    component: () => import('@/views/other/temp_question.vue'),
    meta: {
      hidden: true,
      hiddenTab: true
    }
  },{
    path: '/dzs_info',
    name: '电子书预览',
    component: () => import('@/views/books/dzs_info.vue'),
    meta: {
      hidden: true,
      hiddenTab: true
    }
  },
    /*{
    path: '/test',
    component: layout,
    meta: {
      title: "test",
      icon: 'ic ic-homepage-fill'
    },
    children: [
      {
        path: '/t1',
        component: defineAsyncComponent(() => import(`@/views/test/t1.vue`)),
        meta: {
          title: "测试页面",
          icon: "Lightning"
        }
      }
    ]
  },*/ {
    path: '/other',
    component: layout,
    meta: {
      hidden: true,
      title: "",
      icon: 'ic ic-homepage-fill'
    },
    children: [
      {
        path: '/404',
        component: defineAsyncComponent(() => import('@/views/other/notFound.vue')),
        meta: {
          title: "",
          hidden: true,
          hiddenTab: false
        }
      }
    ]
  }
]
export const asyncRoutes = [
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

const router = createRouter({
  history: createWebHashHistory(),
  scrollBehavior: () => ({
    top: 0
  }),
  routes: routes
})


const whiteList = ['/login', '/ip', '/temp_question','/dzs_info']
// 赋值标题
router.beforeEach(
  async (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
    if (to.query['qs']) {
      setToken(to.query['qs'] as string)
      delete to.query['qs']
    }
    const userStore = useUserStore(store)
    const app = useAppStore()
    const permissionStore = usePermissionStore()
    const tabStore = useTabStore()
    if (!to.meta.title) {
      var _r: any = router.getRoutes().filter(x => {
        return x.path === to.path
      })
      if (_r.length > 0) {
        to.meta = _r[0].meta
      }
    } else {
      const tabsOption = tabStore.tabsOption //store.getters['tabModule/getTabsOption']
      // 判断当前路由中是否已经入栈
      const flag = tabsOption.findIndex((tab: { route: string }) => tab.route === to.path) > -1

      if (!flag && !to.meta.hiddenTab) {
        // store.commit('tabModule/ADD_TAB', { route: to.path, meta: to.meta, name: to.name })
        tabStore.ADD_TAB({ route: to.path, meta: to.meta, name: to.name })
      }
      // store.commit('tabModule/SET_TAB', to.path)
      tabStore.SET_TAB(to.path)
    }
    var app_name = '自考学习平台'



    if (to.name) {
      document.title = `${String(to.meta.title ? to.meta.title : to.name)} - ${app_name}`
    } else {
      document.title = app_name
    }

    // next()
    // 进行权限拦截
    if (to.query['_t']) {
      var utt = to.query['_t']
      setToken(to.query['_t'] as any)
      delete to.query['_t']
      await userStore.autoLogin(utt as any)
    }
    // if(to.query['ut']){
    //   var ut=to.query['ut']
    //   delete to.query['ut']
    //   debugger
    //   await userStore.autoLogin(ut as any)
    // }
    const hasToken = getToken()
    if (whiteList.indexOf(to.path) > -1) {
      next()
    } else if (hasToken) {
      if (to.path === '/login') {
        next({ path: '/' })
      } else {
        const roless = userStore.roles
        const hasRoles = roless && roless.length > 0
        if (hasRoles) {
          //next()
          if (to.matched.length === 0) {
            next('/404')
          } else {
            next()
          }
        } else {
          try {
            const { roles, zd_bm }: any = await userStore.getInfo()
            app.siteData.zd_bm = zd_bm;
            const accessRoutes: any = await permissionStore.generateRoutes(roles)
            accessRoutes.forEach((route: RouteRecordRaw) => {
              const routeName: any = route.name
              if (!router.hasRoute(routeName)) {
                router.addRoute(route)
              }
            })
            //router.addRoute({ path: '/:catchAll(.*)', redirect: '/404' })
            console.log("跳转..", router.getRoutes())
            next({ ...to, replace: true })
            //next({ path: '/' })
          } catch (error: any) {
            // remove token and go to login page to re-login
            //  await store.dispatch('user/resetToken')
            // Message.error(error || 'Has Error')
            ElMessage({
              message: error || 'Has Error',
              type: 'error',
              duration: 5 * 1000
            })
            next(`/login?redirect=${to.path}`)

          }
        }
      }
    } else {
      /* has no token*/

      if (whiteList.indexOf(to.path) !== -1) {
        // in the free login whitelist, go directly
        next()
      } else {
        // other pages that do not have permission to access are redirected to the login page.
        next(`/login?redirect=${to.path}`)
      }
    }
  }
)


export default router