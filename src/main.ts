import {createApp} from 'vue'
import App from './App.vue'
import router from './router/index'
import axios from './utils/axios'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import 'element-plus/dist/index.css'
import {setupStore} from './store/index'
import {isPermission} from './directive/permission'
import '@/styles/index.scss'
import {usePermissionStore} from '@/store/modules/permission'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import {ElMessage} from "element-plus";
import EncryptionPlugin from "vue3-encryption-plugin"

import {library} from '@fortawesome/fontawesome-svg-core'
import {fas} from '@fortawesome/free-solid-svg-icons'

import {FontAwesomeIcon} from '@fortawesome/vue-fontawesome'
import mavonEditor from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'


//markdown 开始 按需引入 mermaidPlugin是流程图显示，很大
import VMdEditor from '@kangc/v-md-editor'
import createKatexPlugin from '@kangc/v-md-editor/lib/plugins/katex/npm'
// import createKatexPlugin from '@kangc/v-md-editor/lib/plugins/katex/cdn';
// import mermaidPlugin from '@kangc/v-md-editor/lib/plugins/mermaid/npm'
// import emojiPlugin from '@kangc/v-md-editor/lib/plugins/emoji/index';
import lineNumberPlugin from '@kangc/v-md-editor/lib/plugins/line-number/index';
// import '@kangc/v-md-editor/lib/plugins/mermaid/mermaid.css'
import '@kangc/v-md-editor/lib/style/base-editor.css'
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js'
import '@kangc/v-md-editor/lib/theme/style/github.css'
import '@kangc/v-md-editor/lib/plugins/emoji/emoji.css';
import hljs from 'highlight.js'
import VMdPreview from '@kangc/v-md-editor/lib/preview'
import '@kangc/v-md-editor/lib/style/preview.css'
import 'highlight.js/styles/default.css' // 引入默认样式
//markdown 结束

library.add(fas)
window.axios = axios
// 链式注册插件
const app = createApp(App)
setupStore(app)

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}

const roles = localStorage.getItem('role')
const permissionStore = usePermissionStore()
if (roles) {
    // permissionStore.generateRoutes( { roleName: roles })
}


app.use(EncryptionPlugin, {
    algorithm: 'AES-CBC',
    key: `${import.meta.env.PACKAGE_SECRET}`
})
app.use(router).use(ElementPlus,{
    locale: zhCn,
  })
app.component('font-awesome-icon', FontAwesomeIcon)
app.config.globalProperties.$message = ElMessage
// 挂载全局方法 isPermission
app.config.globalProperties.$isPermission = isPermission
app.config.errorHandler=(err)=>{
 
 // console.error(err);
}


VMdEditor.use(githubTheme).use(createKatexPlugin())

VMdPreview.use(githubTheme).use(createKatexPlugin())

app.use(VMdPreview)
app.use(VMdEditor)


app.use(ElementPlus);
app.use(mavonEditor)


// 现在所有的导航都是异步的，等路由ready以后再进行挂载组件；
router.isReady().then(() => app.mount('#app'))

// 在导航期间每次发生未捕获的错误时都会调用该处理程序
// eslint-disable-next-line no-console
router.onError((err: any) => {
    console.error("路由错误")
    router.push("/404")
})
