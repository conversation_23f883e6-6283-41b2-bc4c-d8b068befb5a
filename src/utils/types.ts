

export interface I_course_info {//在线课程信息
    sn: number,
    id: number,
    ver: number,
    course_base_id: number,
    is_online: string,
    is_pub: boolean,
    create_date: string,
    create_user: string,
    pub_date: string,
    pub_user: string,
    kc_bm: string,
    kc_mc: string,
    video_ids: number[]
}

export interface I_notice_info {//公告信息
    sn: number,
    id: number,
    title: string,
    content: string,
    pub_date: string,
    is_pub: boolean,
    pub_user: string
}


/**
 * 定义一个播放器接口I_player，用于描述视频播放器的各种属性。
 */
export interface I_player_options {
    kc_mc: string,//课程名称
    title: string,//视频名称
    remark: string,//视频主要内容
    duration_s: string,//视频时长
    ref: HTMLElement //播放器的唯一标识符 ref
    // id: string, // 播放器的唯一标识符
    source: string, // 视频源的URL
    cover: string, // 视频的封面图片URL
    width: string, // 播放器宽度
    height: string, // 播放器高度
    is_allow_drag: boolean, // 是否允许拖动
    currentTime: number, // 当前播放时间（单位：秒）
    volume: number, // 音量大小（0-1之间）
    mute: boolean, // 是否静音
    autoplay: boolean, // 是否自动播放
    controls: boolean, // 是否显示控制条
    isLive: boolean, // 是否为直播
    rePlay: boolean, // 是否支持重播
    videoWidth: string, // 视频实际宽度
    videoHeight: string, // 视频实际高度
    playsinline: boolean, // 是否支持内联播放
    controlBarVisibility: string, // 控制条的可见性
    isVBR: boolean, // 视频是否为可变比特率
    preload: boolean, // 是否预加载
    language: string, // 播放器语言
    useH5Prism: boolean, // 是否使用H5播放器核心
    clickPause: boolean, // 是否点击暂停
    skinLayout: any[], // 播放器皮肤布局配置
    progressMarkers: I_player_progress_marker[], // 进度标记数组
    logoUrl: string,//播放器logo
    sentences: I_player_sentence[],//转写数据
    hf_words: I_player_hf_word[],//高频词
    playList: []
}


/**
 * 进度标记接口
 * 用于定义一个进度标记的基本结构，包括偏移量、标题、是否自定义、封面URL和描述信息
 */
export interface I_player_progress_marker {
    offset: number, // 偏移量，表示标记在内容中的位置
    title: string, // 标题，用于描述标记的名称或主题
    isCustomized: boolean, // 是否为自定义标记，用于区分系统默认标记和用户自定义标记
    coverUrl: string, // 封面URL，用于显示标记的图标或图片
    describe: string // 描述信息，用于详细说明标记的内容或用途
}

export interface I_player_sentence {
    sn: number,
    start_time: number,
    start_time_str: string,
    end_time: number,
    end_time_str: string,
    content: string,
}

export interface I_player_hf_word {
    sn: number,
    num: number,
    title: string,
}


export interface I_app_info {
    title: string,
    version: string,
    visitor_id: string,
    is_open_console: boolean,
    is_small_screen: boolean,
}

// 定义菜单项的类型
export interface I_router_item {
    title: string;
    sn: number;
    is_menu: boolean;
    path: string;
    name: string;
    id: number;
    requiresAuth: boolean;
    pid: number;
    component: string;
    icon: string;
    children?: I_router_item[];
}

export interface I_router_curr {
    path: string,
    name: string,
    title: string,
    params: object,
    query: object,
    meta: I_router_meta
}

export interface I_router_meta {
    title: string,
    requiresAuth: boolean
}

export interface I_router_menu {
    path: string,
    name: string,
    title: string
}

export interface I_login_user {
    user_info_id: number,
    user_stu_id: number,
    uid: string,
    pwd: string,
    is_remember: boolean,
    token: string,
    time_expires: string,
    user_type_id: number,
    xs_xm: string,
    xs_bmh: string,
    xs_bm: string,
    xs_sfzh: string,
    pc_bm: string,
    pc_mc: string,
    zd_bm: string,
    zd_mc: string,
    xb_mc: string,
    yddh: string,
    url_photo: string,
    xjzt_mc: string,
    xjzt_bm: string,
    zy_bm: string,
    zy_mc: string,
    xlcc_bm: string,
    xlcc_mc: string,
    user_type_title: string,
    is_curr: number,
}

export interface I_ai_d_stat {
    d: string,
    num: number
}

export interface I_ai_p_stat {
    province: string,
    num: number
}

export interface I_ai_c_stat {
    city: string,
    num: number
}

export interface I_ai_stat {
    num_group: number,
    num_total: number
}

export interface I_upload_img_info {
    id: string,
    file: any,
    url_response: string,
    url_request_bm: string,
    url_request_1x: string,
    msg: string,
    upload_time: string
}

export interface I_zk_bm_item {
    id: string,
    upload_time: string,
    url_response: string,
    type: string,
    status: string
}

export interface I_upload_text_info {
    id: string,
    text: string,
    url_response: string,
    url_request: string,
    msg: string,
    upload_time: string
}

export interface I_text_to_speech_item {
    id: string,
    upload_time: string,
    url_response: string,
    status: string
}

export interface I_notice_info {
    sn: number,
    id: number,
    title: string,
    content: string,
    pub_date: string
}

export interface I_cg_kc_info {
    sn: number,
    course_info_id: number,
    course_base_id: number,
    cj_plan_info_id: number,
    is_course_page: number,
    kc_bm: string,
    kc_mc: string,
    kc_introduction: string,
    cover: string,
    children?: I_cg_kc_item[];
}

export interface I_cg_kc_item {
    sn: number,
    plate_id: number,
    icon: string,
    title: string,
    remark: string,
    per: number,
    is_list: number,
    is_blank: number,
    plate_type_id: number,
    course_plate_info_id: number
}

export interface I_cg_paper_ls_item {
    course_plate_info_id: number,
    plate_title: string,
    kc_bm: string,
    kc_mc: string,
    course_info_id: number,
    course_base_id: number,
    ver: string,
    source_id: number,
    source_title: string,
    num_total: number
}

export interface I_spwk_kc_info {
    sn: number,
    public_plan_info_id: number,
    course_info_id: number,
    course_base_id: number,
    kc_bm: string,
    kc_mc: string,
    cover: string,
    video_ids: number[]
}


export interface I_kc_video_info {
    sn: number,
    course_video_id: number,
    course_video_title: string,
    duration_s: string,
    size_s: string,
    url_cover: string,
    url_mp4: string,
    is_last: boolean
}

// ...
export interface I_cg_kc_chapter_item {
    sn: number,
    course_dzjy_id: number,
    course_base_id: number,
    course_info_id: number,
    chapter_no: number,
    chapter_title: string,
    chapter_content: string,
    kc_mc: string,
    kc_bm: string
}

export interface I_cg_kc_chapter_item_info {
    course_dzjy_id: number,
    chapter_no: number,
    chapter_title: string,
    chapter_content: string
}

export interface I_cg_kc_point_item {
    sn: number,
    linked_knowledge_point_code: string,
    linked_knowledge_point_name: string,
    kc_mc: string,
    kc_bm: string
}

///


export interface I_course_dzs_info {
    course_dzs_id: number,
    course_dzs_title: string,
    plate_title: string,
    course_base_id: number,
    course_info_id: number,
    content: number,
    create_date: string,
    kc_mc: string,
    kc_bm: string,
    menu: I_course_dzs_menu[],
    zsd: []
}


export interface I_course_dzs_menu {
    type: string,
    title: string,
    lineIndex: string,
    is_ext: boolean,
    children: I_course_dzs_menu[]
}

export interface I_markdown_menu {
    title: string,
    lineIndex: string,
    indent: number,
}

export interface I_paper_item {
    sn: number,
    ques_num: number,
    ques_num_do: number,
    ques_id: number,
    is_auto_score: number,
    in_markdown_section: number,
    ques_type: string,
    score_explain: string,
    children: any[]
}

export interface I_paper_info_ver {
    job_stu_course_cg_id: number,
    num_do: number,
    num_total: number,
    paper_data: I_paper_item[]
}

export interface I_paper_info {
    user_info_id: number,
    user_stu_id: number,
    course_plate_info_id: number,
    num_do: number,
    num_total: number,
    paper_data: I_paper_item[]
}

export interface I_paper_answer_stu_info {
    job_stu_course_cg_id: number,
    num_do: number,
    num_total: number,
    data: I_paper_answer_stu_item[]
}

export interface I_paper_answer_stu_item {
    job_stu_answer_cg_id: number,
    answer_stu: any
}
