import {ElMessageBox, ElMessage, ElNotification} from "element-plus";

/**
 * A function that pops up a message box.
 * @param {string} message - The content of the message box.
 * @param {string} type - The type of the message box, which can be success, warning, info, or error.
 * @param {string} [title] - The title of the message box.
 */
export function msgAlert(message: string, type: string, title: string = "") {
    if (!title) {
        title = ''
    }
    if(type === "error") {
        message=`<span class="red f_b">${message}</span>`
    }
    // @ts-ignore
    ElMessageBox.alert(
        message,
        title,
        {
            confirmButtonText: '确定',
            type: type,
            draggable: true,
            dangerouslyUseHTMLString: true
        }
    );
}

/**
 * The function takes two parameters, a string and a string, and returns nothing
 * @param {string} message - The message to be displayed
 * @param {string} type - The type of message box, which can be success, warning, info, or error.
 */
export function msgShow(message: string, type: 'success' | 'warning' | 'info' | 'error') {
    ElMessage({
        type: type,
        message: message,
        dangerouslyUseHTMLString: true,
        duration: 5 * 1000
    });
}

export function msgNotice(title:string,message: string, type: string="warning", delay: number = 10000) {
    if (type === 'success') {
        ElNotification.success({
            title: title,
            dangerouslyUseHTMLString: true,
            offset: 200,
            duration: delay,
            position:"top-right",
            message: `<span class="green b">${message}</span>`
        });
    }else{
        ElNotification.warning({
            title: "",
            dangerouslyUseHTMLString: true,
            offset: 200,
            duration: delay,
            position:"top-right",
            message: `<span class="red b">${message}</span>`
        });
    }

}
