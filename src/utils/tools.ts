let self: any

class ToolsUtils {
    constructor() {
        self = this
        self.timer = null
    }

    /**
     * 将秒转换为格式化的字符串
     * @param seconds 输入的秒数
     * @returns 格式化后的小时、分钟和秒的字符串。格式为 "HH:MM:SS" 或 "MM:SS"，不带秒数的小于一分钟的显示。
     */
    timeSeconds2Str = (seconds: number) => {
        // 计算小时、分钟和剩余秒数
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = seconds % 60;

        // 对小时、分钟和秒数进行格式化，确保它们都是两位数的字符串
        const formattedHours = String(hours).padStart(2, '0');
        const formattedMinutes = String(minutes).padStart(2, '0');
        const formattedSeconds = String(remainingSeconds).padStart(2, '0');

        // 根据是否有小时来返回不同的格式化字符串
        if (formattedHours == '00') {
            return `${formattedMinutes}:${formattedSeconds}`; // 仅返回分钟和秒
        } else {
            return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`; // 返回完整的小时、分钟和秒
        }
    }

    /**
     * 将时间字符串转换成总秒数
     * @param timeStr 时间字符串，格式为"hh:mm:ss,ms"，其中hh为小时，mm为分钟，ss为秒，ms为毫秒
     * @returns 返回时间的总秒数，其中小时换算成3600秒，分钟换算成60秒
     */
    timeStr2Seconds = (timeStr: string) => {
        // 将时间字符串根据":"、"."或","分割成四部分，并转换为数字
        let [hh, mm, ss, ms] = timeStr.split(/[:.,]/).map(Number);
        // 计算总秒数，毫秒转换成秒并向上取整
        return hh * 3600 + mm * 60 + ss + Math.ceil(ms / 1000);
    }
    scrollToElement = (element: HTMLElement, behavior: any) => {
        // element.scrollIntoView({behavior: behavior || "smooth", block: "nearest", inline: "start"});
        element.scrollIntoView({
            behavior: behavior || "smooth",
            block: "start",
            inline: "nearest",
        });
    }
    getImg = (fn: Function, imgBlob: Blob) => {
        let imgFile = new FileReader()
        imgFile.onload = function (e) {
            let image = new Image()
            //@ts-ignore
            image.src = e.target.result // base64数据
            image.onload = function () {
                fn(image)
            }
        }
        imgFile.readAsDataURL(imgBlob)
    }
    getBrowerData = () => {
        const appVersion = navigator.userAgent //获取浏览器版本信息
        const index = appVersion.indexOf('Chrome/') //谷歌浏览器的版本信息位置
        const ChromeStr = appVersion.substring(index + 7, appVersion.length) //以谷歌浏览器信息开始的字符串('Chrome/'占7个字符)
        const index_point = ChromeStr.indexOf('.') //第一个.的位置
        const ChromeVersions = ChromeStr.substring(0, index_point) //谷歌浏览器大版本号

        if (appVersion.includes('Chrome')) {
            //包含Chrome字样则判断版本
            return {is_chrome: true, version: parseInt(ChromeVersions)}

        } else {
            //不包含Chrome字样直接返回false
            return {is_chrome: false, version: parseInt(ChromeVersions)}
        }
    }

    getRandomRange(m: number, n: number) {
        return Math.floor(Math.random() * (m - n) + n)
    }

    getMinutesBySeconds = (seconds: number): number => {
        if (seconds === 0) {
            return 0;
        }
        return Math.ceil(seconds / 60);
    }
    isExpired = (time_expires: string) => {
        if (time_expires) {
            // 将字符串时间转换为 Date 对象
            const time_expires__ = new Date(time_expires);
            // 获取当前时间
            const time_curr__ = new Date();
            // 比较时间
            return time_expires__ < time_curr__;
        } else {
            return true;
        }
    }

    getCurrTime = () => {
        let date_ = new Date();
        const year_ = date_.getFullYear();
        const month_ = String(date_.getMonth() + 1).padStart(2, '0');
        const day_ = String(date_.getDate()).padStart(2, '0');
        const hours_ = String(date_.getHours()).padStart(2, '0');
        const minutes_ = String(date_.getMinutes()).padStart(2, '0');
        const seconds_ = String(date_.getSeconds()).padStart(2, '0');
        return `${year_}-${month_}-${day_} ${hours_}:${minutes_}:${seconds_}`;
    }
    getExpiredTime = () => {
        // 获取当前时间
        const currentDate = new Date();
// 增加2天的毫秒数（1天 = 24小时 * 60分钟 * 60秒 * 1000毫秒）
        const twoDaysInMillis = 7 * 24 * 60 * 60 * 1000;
// 计算增加2天后的时间
        const date_ = new Date(currentDate.getTime() + twoDaysInMillis);
        const year_ = date_.getFullYear();
        const month_ = String(date_.getMonth() + 1).padStart(2, '0');
        const day_ = String(date_.getDate()).padStart(2, '0');
        const hours_ = String(date_.getHours()).padStart(2, '0');
        const minutes_ = String(date_.getMinutes()).padStart(2, '0');
        const seconds_ = String(date_.getSeconds()).padStart(2, '0');
        return `${year_}-${month_}-${day_} ${hours_}:${minutes_}:${seconds_}`;
    }

    getCurrTimeWithNumStr() {
        let date_ = new Date();
        let year_ = date_.getFullYear();
        let month_ = date_.getMonth() + 1 < 10 ? '0' + (date_.getMonth() + 1) : date_.getMonth() + 1;
        let day_ = date_.getDate() < 10 ? '0' + date_.getDate() : date_.getDate();
        let hour_ = date_.getHours() < 10 ? '0' + date_.getHours() : date_.getHours();
        let minute_ = date_.getMinutes() < 10 ? '0' + date_.getMinutes() : date_.getMinutes();
        let second_ = date_.getSeconds() < 10 ? '0' + date_.getSeconds() : date_.getSeconds();
        let milliseconds_ = date_.getMilliseconds() < 1000 ? '000' + date_.getMilliseconds() : date_.getMilliseconds();
        return year_.toString() + month_.toString() + day_.toString() + hour_.toString() + minute_.toString() + second_ + milliseconds_.toString();
    }

    openUrl = (url: string, target = '_blank') => {
        let a = document.createElement('a');
        let tmp_r__ = url.includes('?')
        if (tmp_r__) {
            a.setAttribute('href', `${url}&r=${new Date().getTime()}`);
        } else {
            a.setAttribute('href', `${url}?r=${new Date().getTime()}`);
        }

        a.setAttribute('style', 'display:none');
        a.setAttribute('target', target);
        document.body.appendChild(a);
        a.click();
        // @ts-ignore
        a.parentNode.removeChild(a);
    }
    trimSpace = (str: string) => {
        return str.replace(/\s+/g, '')
    }
    replaceSpace = (str: string) => {
        return str.replace(/\s+/g, '+')
    }
    replaceBr2Newline = (str: string) => {
        // 匹配 <br>、<br/>、<BR>、<Br> 等各种形式
        return str.replace(/<br\s*\/?>/gi, '\n');
    }

    removeAllEscapes = (str: string) => {//去掉转义符
        return str.replace(/\\\\/gi, '\\')
    }
    format2Md = (str: string) => {//转义符转成markdown
        return this.replaceBr2Newline(this.removeAllEscapes(str));
    }
    #_preventDefault = (event: Event) => {
        try {
            event.preventDefault()
        } catch (e) {
        }
    }
    // 定义keydown事件处理函数
    #_keydownHandler = (event: KeyboardEvent) => {
        const {ctrlKey, key} = event;
        // if (ctrlKey && (key === 'c' || key === 'v')) {
        //     event.preventDefault();
        // }
        // 这里添加了对所有可能的加号键的检查
        if (ctrlKey && ['61', '107', '173', '109', '187', '189', '+', '-', 'p'].includes(key)) {
            event.preventDefault();
        }
    };
    disabledCtr = (is_allow_zoom = true) => {
        // 解除所有事件绑定
        document.removeEventListener("selectstart", self.#_preventDefault);
        document.removeEventListener("contextmenu", self.#_preventDefault);
        // document.removeEventListener("copy", self.#_preventDefault);
        // document.removeEventListener("cut", self.#_preventDefault);
        // document.removeEventListener("paste", self.#_preventDefault);
        document.removeEventListener("keydown", self.#_keydownHandler);
        document.removeEventListener("mousewheel", self.#_preventDefault);
        document.removeEventListener("DOMMouseScroll", self.#_preventDefault);
        document.removeEventListener("dragover", self.#_preventDefault);
        if (!is_allow_zoom) {
            document.removeEventListener('mousewheel', self.#_preventDefault);
            document.removeEventListener('DOMMouseScroll', self.#_preventDefault);
        }
        // 重新绑定事件
        document.addEventListener("contextmenu", self.#_preventDefault);
        // document.addEventListener("copy", self.#_preventDefault);
        // document.addEventListener("cut", self.#_preventDefault);
        // document.addEventListener("paste", self.#_preventDefault);
        document.addEventListener("keydown", self.#_keydownHandler);
        document.addEventListener("dragover", self.#_preventDefault);
        if (!is_allow_zoom) {
            document.addEventListener('mousewheel', self.#_preventDefault, {passive: false});
            document.addEventListener('DOMMouseScroll', self.#_preventDefault, {passive: false});
        }
    }
    HexToBase64 = (sha1: any) => {
        var digits = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
        var base64_rep = ""
        var ascv
        var bit_arr = 0
        var bit_num = 0

        for (var n = 0; n < sha1.length; ++n) {
            if (sha1[n] >= 'A' && sha1[n] <= 'Z') {
                ascv = sha1.charCodeAt(n) - 55
            } else if (sha1[n] >= 'a' && sha1[n] <= 'z') {
                ascv = sha1.charCodeAt(n) - 87
            } else {
                ascv = sha1.charCodeAt(n) - 48
            }

            bit_arr = (bit_arr << 4) | ascv
            bit_num += 4
            if (bit_num >= 6) {
                bit_num -= 6

                base64_rep += digits[bit_arr >>> bit_num]
                bit_arr &= ~(-1 << bit_num)
            }
        }

        if (bit_num > 0) {
            bit_arr <<= 6 - bit_num
            base64_rep += digits[bit_arr]
        }
        var padding = base64_rep.length % 4

        if (padding > 0) {
            for (var n = 0; n < 4 - padding; ++n) {
                base64_rep += "="
            }
        }
        return base64_rep
    }
    isMobile = () => {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }
    downloadFileByUrlName = (url: string, file_name: string) => {
        let _OBJECT_URL;
        let request = new XMLHttpRequest();
        request.addEventListener('readystatechange', function (e) {
            if (request.readyState == 4) {
                _OBJECT_URL = URL.createObjectURL(request.response);
                let a = document.createElement('a')
                a.setAttribute("href", _OBJECT_URL)
                a.setAttribute("download", file_name);
                a.click();
            }
        });
        request.responseType = 'blob';
        request.open('get', `${url}?${new Date().getTime()}`);
        request.send()
    }
    getGreeting = (): string => {
        const hour = new Date().getHours();
        return hour >= 5 && hour < 12 ? "上午好。"
            : hour >= 12 && hour < 14 ? "中午好。"
                : hour >= 14 && hour < 18 ? "下午好。"
                    : "晚上好。";
    }
    hasZmAndNumber = (str: string) => {//验证是否包含字母和数字
        return /^(?=.*[a-zA-Z])(?=.*\d).+$/.test(str)
    }
    showMaskFull = () => {
        let dom_ref_mask_full = document.querySelector('.ref_mask_full')
        if (dom_ref_mask_full) {
            //@ts-ignore
            dom_ref_mask_full.style.display = 'block';
        }
    }
    hideMaskFull = () => {
        let dom_ref_mask_full = document.querySelector('.ref_mask_full')
        if (dom_ref_mask_full) {
            //@ts-ignore
            dom_ref_mask_full.style.display = 'none';
        }
    }
    delIndexedDB = async (name: string) => {
        return new Promise((resolve, reject) => {
            const request = indexedDB.deleteDatabase(name);
            const res = { msg: '', code: '' };

            request.onsuccess = () => {
                res.msg = '本站点浏览器数据删除成功';
                res.code = '200';
                resolve(res);
            };

            request.onerror = () => {
                res.msg = '本站点浏览器数据删除失败';
                res.code = '500';
                reject(res);
            };

            request.onblocked = () => {
                res.msg = '本站点浏览器数据删除操作被阻止，可能有其他连接未关闭，请关闭浏览器后重试';
                res.code = '204';
                reject(res);
            };
        });
    }
    #_getScrollParent = (element: any) => {
        let node = element;
        while (node && node !== document.body) {
            const {overflowY} = window.getComputedStyle(node);
            if (overflowY === 'auto' || overflowY === 'scroll') return node;
            node = node.parentElement;
        }
        return window;
    }
    highlightMatch = (element: any) => {
        const originalBg = element.style.backgroundColor;
        element.style.backgroundColor = '#ffff99';
        element.style.transition = 'background-color 0.3s';

        setTimeout(() => {
            element.style.backgroundColor = originalBg;
            setTimeout(() => {
                element.style.backgroundColor = '#ffff99';
                setTimeout(() => element.style.backgroundColor = originalBg, 300);
            }, 300);
        }, 1000);
    }
    scrollToFirstMatch = (textToFind: string) => {
        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: (node:any) =>
                    ['SCRIPT', 'STYLE'].includes(node.parentElement?.tagName)
                        ? NodeFilter.FILTER_SKIP
                        : NodeFilter.FILTER_ACCEPT
            }
        );

        let firstMatch:any = null;

        while (walker.nextNode()) {
            const text:any = walker.currentNode.textContent;
            // 必须包含指定文本
            if (text && text.includes(textToFind)) {
                firstMatch = walker.currentNode;
                break;
            }
        }

        if (firstMatch) {
            const scrollParent = this.#_getScrollParent(firstMatch.parentElement);
            const rect = firstMatch?firstMatch.parentElement.getBoundingClientRect():null;

            scrollParent.scrollTo({
                top: rect.top + scrollParent.scrollTop - 100,
                behavior: 'smooth'
            });
            console.log('firstMatch.parentElement', firstMatch.parentElement)
            this.highlightMatch(firstMatch.parentElement);
            return true;
        }
        return false;
    }
    generateMarkdownToc(element:HTMLElement) {
        // 获取所有h1-h6标签
        const anchors = element.querySelectorAll('h1, h2, h3');

        // 过滤掉空标题并映射为包含必要信息的对象数组
        const titles = Array.from(anchors)
            .filter(title => title.textContent?.trim())
            .map(el => ({
                type: el.tagName.toLowerCase(),
                title: el.innerHTML,
                lineIndex: parseInt(el.getAttribute('data-v-md-line') || '0'),
                level: parseInt(el.tagName.charAt(1)), // 从标签名中提取级别，如h1为1
                element: el
            }));

        // 如果没有标题，返回空数组
        if (titles.length === 0) return [];

        // 创建一个虚拟根节点
        const root = { level: 0, children: [] };
        // 跟踪每个级别的最后一个标题节点
        const lastNodes:any = { 0: root };

        // 构建嵌套结构
        titles.forEach(title => {
            // 找到最近的上级标题（级别比当前小的最后一个标题）
            let parentLevel = title.level - 1;
            while (parentLevel > 0 && !lastNodes[parentLevel]) {
                parentLevel--;
            }

            const parent = lastNodes[parentLevel] || root;
            // 如果父节点不存在子数组，创建一个
            if (!parent.children) {
                parent.children = [];
            }

            // 从节点信息中删除原始DOM元素，避免循环引用
            const { element, ...nodeInfo } = title;
            const newNode = { ...nodeInfo, children: [] };

            // 添加当前标题作为子节点
            parent.children.push(newNode);
            // 更新当前级别对应的最后一个节点
            lastNodes[title.level] = newNode;
        });

        // 返回根节点的子节点数组（忽略虚拟根节点）
        return root.children;
    }
}

const toolsUtils = new ToolsUtils()
export {
    toolsUtils
}

