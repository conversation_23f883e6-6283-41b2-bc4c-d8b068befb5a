import cookies from 'js-cookie'

const Token<PERSON>ey = 'Admin-Token-srm'


const UserIdKey = 'Admin-UserId-srm'

export function getToken() {
  return cookies.get(TokenKey)
}

export function setToken(token: string) {
  return cookies.set(Token<PERSON><PERSON>, token)
}

export function removeToken() {
  cookies.remove(TokenKey)
  return cookies.remove(TokenKey)
}

export function getUserId() {
  return cookies.get(UserIdKey)
}

export function setUserId(user_id: string) {
  return cookies.set(UserIdKey, user_id)
}

export function removeUserId() {
  return cookies.remove(UserIdKey)
}




