<template>
  <div class="page" >
    <div class="header"> 
          <el-button type="success" plain size="default" icon="refresh" round @click="getData"></el-button>
          <el-input v-model="rolesData.keySearch" placeholder="名称" size="default" style="width: 260px;margin:0 10px;" clearable @change="getData">
            <template #append>
              <el-button icon="Search" @click="getData"/>
            </template>
          </el-input>
          <el-button type="primary"  plain size="default" round icon="CirclePlus" @click="AddData()">新增</el-button>
          <el-button type="success" plain size="default" round icon="edit" @click="handleEdit()">编辑</el-button> 
    </div>
    <div class="body">
      <el-table
        ref="multipleRolesTable"
        class="tb-edit"
        :data="rolesData.dataTable"
        border
        v-loading="rolesData.loading" 
        :height="rolesData.tableHeight"
        size="mini"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="60" align="center" />
        <el-table-column prop="" type="index" label="序号"  align="center" width="60" header-align="center" />
        <el-table-column prop="title" label="名称" min-width="120" align="center" sortable />
        <el-table-column prop="num" label="人数" min-width="120" align="center" sortable>
          <template #default="scope">
            <el-link type="primary" :underline="false">{{ scope.row.num }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="date_created" label="创建时间" min-width="160" align="center" sortable>
          <template #default="scope">
            <span>{{ scope.row.date_created }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="create_name" label="操作人" min-width="100" align="center" sortable />
      </el-table>
    </div>


    <el-dialog v-model="DialogVisible" ref="kdroomDialog" :title="Dialog_title" draggable width="600px"
      :close-on-press-escape="false" :close-on-click-modal="false">
      <div style="width:100%;text-align:center;">
        <el-form :model="FromModule" :inline="true" :label-width="formLabelWidth">
          <el-form-item label="用户类型名称" >
            <el-input v-model="FromModule.title" placeholder="用户类型名称" style="width:400px" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="DialogVisible = false" icon="Close">关闭</el-button>
          <el-button type="success" @click="SaveFrom()" icon="CircleCheck"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>



  </div>
</template>

<script lang="ts" setup>
 import { defineComponent, onMounted, reactive, ref, toRefs } from "vue";
 import { GetUserTypeData,SaveUserTypeData } from '@/api/settings'
 import { ElMessage,ElMessageBox } from 'element-plus'
 const rolesData= reactive({
    loading:false,
    tableHeight:window.innerHeight - 180,
    keySearch:'',
    dataTable:[]
  })



const DialogVisible = ref(false)
const Dialog_title = ref('')
const formLabelWidth = '100px'
const handlecurrentRow = ref([]) as any
const FromModule = ref({}) as any;
 

  const getData=()=> {
    rolesData.loading = true
      const pars = {
        key: rolesData.keySearch
      }
      GetUserTypeData(pars).then((msg:any) => {
        rolesData.dataTable = msg.data
        rolesData.loading = false
      }).catch((err:any) => { console.log(err); rolesData.loading = false })
  }

const AddData = () => {
    FromModule.value={
      id:0,
      name:'',
      title:'',
      remark:''
    }
    Dialog_title.value = '新增用户类型'
    DialogVisible.value = true
  }

  const handleEdit = () => {
    if (handlecurrentRow.value != null && handlecurrentRow.value.length === 1) {
      Dialog_title.value = '编辑【' + handlecurrentRow.value[0].title + '】'
      DialogVisible.value = true
      FromModule.value = handlecurrentRow.value[0]
      } else {
        ElMessage({
            message: '请勾选一条数据进行编辑！',
            type: 'error',
          })
        return false
      }
  }
   //保存数据
   const SaveFrom=() => {
      if(FromModule.value.title==='' || FromModule.value.title===null || FromModule.value.title === undefined){
        ElMessage({
              message: '用户类型名称不能为空',
              type: 'info',
            })
            return
      }
      SaveUserTypeData(FromModule.value).then((res:any) => {
        if (res.data > 0) {
          DialogVisible.value = false
          ElMessage({
            message: '保存成功！',
            type: 'success',
          })
          getData()
        }else{
          ElMessage({
            message: '保存失败！'+res.msg,
            type: 'error',
          })
        }
      })
    }
    
    
const handleSelectionChange=( rows:any )=> {
  handlecurrentRow.value = rows
}
const filterMethod = (query: any, item:any) => {
  if (item.zd_bm === null) {
          item.zd_bm = ''
  }
  return item.name.indexOf(query) > -1 || item.uid.indexOf(query) > -1
}

  onMounted(() => {
    getData();
  });
</script>

<style lang="scss">
.addRolsDia{
  .el-dialog__body{
    padding: 5px 5px !important;
}
.el-transfer-panel__item.el-checkbox .el-checkbox__label{
  margin-left: 20px !important;
}
.el-transfer-panel__body {
  height:550px
}
.el-transfer-panel__list.is-filterable{
  height: 400px;
}
.el-transfer-panel__item.el-checkbox .el-checkbox__label{
    width: auto!important;
  }
 .el-transfer-panel{
  width: 350px !important;
  text-align: left !important;
}
.el-transfer__buttons {
    padding: 0 20px !important;
}
}


</style>
