<template>
  <div class="userManage page">
    <div class="header"> 
      <el-button type="success" plain size="default" icon="refresh" round @click="getData"></el-button>
          <el-input v-model="userData.keySearch" placeholder="用户名|姓名|身份证|手机号" size="default" style="width: 260px;margin:0 10px;" clearable @change="getData">
            <template #append>
              <el-button icon="Search" @click="getData"/>
            </template>
          </el-input>
      <el-tag class="el_tag_5">用户类型</el-tag>
      <el-select v-model="userData.user_type_id" clearable filterable style="width: 160px;" placeholder="用户类型" @change="getData">
          <el-option v-for="item in user_type_title" :key="item.id" :label="item.title" :value="item.id" style="width: 260px;">
            <span style="float: left">{{ item.id}}</span> 
            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.title }}</span>
          </el-option> 
      </el-select>
      <el-button type="primary"  plain size="default" round icon="CirclePlus" @click="handleAddData"> 新增</el-button>
      <el-button type="success" plain size="default" round icon="edit" @click="handleEdit">编辑</el-button>
      <el-button type="danger" plain size="default" round icon="delete" @click="handleDelete">删除</el-button>
      
    </div>
    <div class="body">
      <el-table
        :data="userData.dataTable"
        border
        class="modTable"
        :height="userData.tableHeight"
        style="width: 100%;"
        v-loading="userData.loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" align="center" /> 
        <el-table-column prop="user_type_title" label="用户类型" min-width="100" align="center" header-align="center" show-overflow-tooltip/>
        <el-table-column prop="uid" label="用户名" min-width="120" align="center" show-overflow-tooltip/>
        <el-table-column prop="pwd" label="密码" min-width="120" align="left" header-align="center"/>
        <el-table-column prop="pwd_md5" label="密码MD5" min-width="120" align="left" header-align="center" show-overflow-tooltip/>
        <el-table-column prop="name" label="姓名" min-width="120" align="center" header-align="center" show-overflow-tooltip/>
        <el-table-column prop="status" label="系统角色" align="center" header-align="center" min-width="140" show-overflow-tooltip>
          <template #default="scope">
            <div style="width:100%;"> 
              <el-button link type="primary"  style="float:right;color:#1890FF;cursor:pointer" size="small" @click="openRoleDia(scope.row)"><el-icon><CirclePlusFilled /></el-icon></el-button>
              <div class="role_titleClass">
                <el-tooltip class="item" effect="dark" :content="scope.row.role_title.replace(/,/g, '  | ')" placement="bottom-start">
                  <span v-html="scope.row.role_title.replace(/,/g, '&nbsp;&nbsp;|&nbsp; ')" />
                </el-tooltip>
              </div>
            </div>
          </template>
        </el-table-column>
       
        <el-table-column prop="mobile" label="手机号" min-width="100" align="center" header-align="center"/>
        <el-table-column prop="sfzh" label="身份证号" min-width="140" align="center" header-align="center"/>
        <el-table-column prop="email" label="邮箱" min-width="100" align="center" header-align="center" show-overflow-tooltip/>
        <el-table-column prop="status" label="状态" align="center" min-width="60" show-overflow-tooltip>
          <template #default="scope">
            <span v-if="scope.row.status===1" style="color:blue">启用</span>
            <span v-else style="color:Red">禁用</span>
          </template>
        </el-table-column>
        <el-table-column prop="create_date" label="创建时间" min-width="140" align="center" header-align="center"/>
        <el-table-column prop="create_user" label="创建人" min-width="80" align="center" header-align="center"/>
        
        <!-- <el-table-column prop="url_photo" label="照片" min-width="100" align="left" header-align="center" show-overflow-tooltip>
          <template #default="scope">
            <el-link :href="scope.row.url_photo" type="primary" style="font-size: 12px;" target="_blank">{{scope.row.url_photo}}</el-link>
          </template>
        </el-table-column> -->
        <el-table-column prop="zd_bm" label="站点编码" min-width="80" align="center" header-align="center"/>
        <el-table-column prop="zd_mc" label="站点名称" min-width="140" align="left" header-align="center" show-overflow-tooltip/>
        
      </el-table>
      <div style="margin-top: 16px; text-align: right;">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="userData.total"
          :page-size="userData.pageSize"
          :current-page="userData.currentPage"
          :page-sizes="[20, 50, 100, 500, userData.total]"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>


     <!--增加角色-->
     <el-dialog title="添加角色" v-model="roleDialogVisible" ref="DialogroleVisible" draggable width="600px"
      :close-on-press-escape="false" :close-on-click-modal="false">
      <div style="width:550px;margin-left:40px">
        <el-checkbox-group v-model="checkRole" class="roleCheckGroup">
          <el-checkbox style="width:150px;" v-for="item in roleCheckList" :key="item.id" :label="item.id">
            {{ item.id }}-{{ item.title }}
          </el-checkbox>
        </el-checkbox-group>
        <p />
      </div>
      <div style="width:100%;text-align:center">
        <el-button size="mini" type="primary" @click="saveRole()">保存</el-button>
      </div>
    </el-dialog>


    <!--用户信息维护-->
    <el-dialog v-model="userDialogVisible" ref="userDialog" :title="Dialog_title" draggable width="840px"
      :close-on-press-escape="false" :close-on-click-modal="false">
      <div style="width:100%;text-align:center;">
        <el-form :model="FromModule" :inline="true" :label-width="formLabelWidth" :rules="rulesformUsers">
          <el-form-item label="用户类型" prop="user_type_id">
            <el-select v-model="FromModule.user_type_id" clearable filterable style="width: 250px;" placeholder="用户类型" @change="getData">
                <el-option v-for="item in user_type_title" :key="item.id" :label="item.title" :value="item.id" style="width: 250px;">
                </el-option> 
            </el-select>
          </el-form-item>
          <el-form-item label="是否启用" prop="status">
            <el-radio-group v-model="FromModule.status" style="width:235px;">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="登录名(UID)" prop="uid">
            <el-input v-model="FromModule.uid" placeholder="登录名(UID)" style="width:250px" />
          </el-form-item>
          <el-form-item label="密码" prop="pwd">
            <el-input v-model="FromModule.pwd" placeholder="密码" style="width:250px" />
          </el-form-item> 
          <el-form-item label="名称(姓名)" prop="name">
            <el-input v-model="FromModule.name" placeholder="名称(姓名)" style="width:250px" />
          </el-form-item>
          <el-form-item label="联系电话" prop="mobile">
            <el-input v-model="FromModule.mobile" placeholder="联系电话" style="width:250px" />
          </el-form-item>
          <el-form-item label="身份证号" prop="sfzh">
            <el-input v-model="FromModule.sfzh" placeholder="身份证号" style="width:250px" />
          </el-form-item>
          <el-form-item label="电子邮箱" prop="email">
            <el-input v-model="FromModule.email" placeholder="电子邮箱" style="width:250px" />
          </el-form-item>
          <el-form-item label="站点编码" >
            <el-input v-model="FromModule.zd_bm" placeholder="站点编码" style="width:250px" />
          </el-form-item>
          <el-form-item label="站点名称" >
            <el-input v-model="FromModule.zd_mc" placeholder="站点名称" style="width:250px" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="userDialogVisible = false" icon="Close">关闭</el-button>
          <el-button type="success" @click="SaveUserFrom()" icon="CircleCheck"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>



  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { GetUserInfoList,GetUserTypeData,GetRoleslistData,SaveRoleOfUserData,SaveUserInfoFromData,DelUserInfoData } from '@/api/settings'

const userData = reactive({
  loading: false,
  tableHeight: window.innerHeight - 220,
  keySearch: '',
  user_type_id:2,
  dataTable: [] as any,
  total: 0,
  pageSize: 30,
  currentPage: 1
})
const user_type_title = ref([]) as any
const handlecurrentRow = ref([]) as any
 
const roleDialogVisible = ref(false)
const roleSelectRow = ref([]) as any
const checkRole = ref([]) as any
const roleCheckList = ref([]) as any


const FromModule = ref({}) as any;
const formLabelWidth = '100px' 
const userDialogVisible = ref(false)
const Dialog_title = ref('') 
const rulesformUsers= ref({
        uid: [{ required: true, message: '请输入登录名(UID)', trigger: 'blur' }],
        user_type_id: [{ required: true, message: '请选择用户类型', trigger: 'blur' }],
        status: [{ required: true, message: '请选择用户状态', trigger: 'blur' }],
        name: [{ required: true, message: '请输入名称(姓名)', trigger: 'blur' }],
        mobile: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
        // email: [
        //   { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        //   {
        //     type: 'email',
        //     message: '请输入正确的邮箱地址',
        //     trigger: 'blur,change'
        //   }
        // ],
        pwd: [
          {
            required: true,
            maxlength: 16,
            minlength: 3,
            message: '请输入密码3~16位之间',
            trigger: 'blur'
          }
        ]
      })

const getTypeData = () => {
  userData.loading = true
  GetUserTypeData({}).then((res:any) => {
    console.log(res)
    user_type_title.value = res.data
    userData.user_type_id =res.data.length>0? res.data[0].id : ''
    getData()
  }).catch((err:any) => {
        ElMessage({
          type: 'error',
          message: '出错了!' + err
        })
    })
}
const getData = () => {
  userData.loading = true
  const pars = {
    keySearch: userData.keySearch,
    user_type_id: userData.user_type_id,
    currentPage: userData.currentPage,
    pageSize: userData.pageSize,
  }
  // 假设 GetUserInfoList 能分页返回数据，否则请替换为实际的用户列表接口
  GetUserInfoList(pars).then((msg: any) => {
    userData.dataTable = msg.data || []
    userData.total = msg.total || userData.dataTable.length
    userData.loading = false
  }).catch((err: any) => {
    console.log(err)
    userData.loading = false
  })
}

const handleAddData = () => {
    FromModule.value={
      id:0,
      uid:'',
      pwd:'',
      pwd_md5:'',
      status:1
    }
    Dialog_title.value = '新增用户'
    userDialogVisible.value = true
  }

  const handleEdit = () => {
    if (handlecurrentRow.value != null && handlecurrentRow.value.length === 1) {
      Dialog_title.value = '编辑【' + handlecurrentRow.value[0].uid + '】'
      userDialogVisible.value = true
      FromModule.value = handlecurrentRow.value[0]
      } else {
        ElMessage({
            message: '请勾选一条数据进行编辑！',
            type: 'error',
          })
        return false
      }
  }
   //保存数据
   const SaveUserFrom=() => {
      if(FromModule.value.uid==='' || FromModule.value.uid===null || FromModule.value.uid === undefined){
        ElMessage({
              message: '登录名(UID)不能为空',
              type: 'info',
            })
            return
      }
      if(FromModule.value.user_type_id==='' || FromModule.value.user_type_id===null || FromModule.value.user_type_id === undefined){
        ElMessage({
              message: '用户类型不能为空',
              type: 'info',
            })
            return
      }
      if(FromModule.value.name==='' || FromModule.value.name===null || FromModule.value.name === undefined){
        ElMessage({
              message: '名称不能为空',
              type: 'info',
            })
            return
      }

      SaveUserInfoFromData(FromModule.value).then((res:any) => {
        if (res.data > 0) {
          userDialogVisible.value = false
          ElMessage({
            message: '保存成功！',
            type: 'success',
          })
          getData()
        }else{
          ElMessage({
            message: '保存失败！'+res.msg,
            type: 'error',
          })
        }
      })
    }
    // 删除
    const handleDelete=() => {
      if (handlecurrentRow != null && handlecurrentRow.value.length > 0) {
        const row = handlecurrentRow.value
        ElMessageBox.confirm(
          '此操作将永久删除【' + row.length + '】条数据, 是否继续?',
          '提示',
          {
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        .then(() => {
          const ids = []
          for (var i = 0; i < row.length; i++) {
            ids.push(row[i].id)
          }
          var idstr = ids.join(',')
          const pars = { id: idstr }
          DelUserInfoData(pars).then((msg:any) => {
            if (msg.data > 0) {
              ElMessage({
                type: 'success',
                message: '成功删除!' + msg.data + '条'
              })
              getData()
            } else {
              ElMessage({
                type: 'info',
                message: '删除失败!' + msg.msg
              })
            }
          }).catch((err:any) => {
            ElMessage({
              type: 'info',
              message: '删除失败!' + err
            })
          })
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '取消删除!',
          })
        })
      } else {
        ElMessage({
          message: '请至少勾选一条数据进行删除！',
          type: 'error'
        })
        return false
      }
    }


const openRoleDia = (rows: any) => {
    roleDialogVisible.value = true
    roleSelectRow.value = rows
    checkRole.value = []
    var tmpArray = roleSelectRow.value.role_id.split(',')
    for (var i = 0; i < tmpArray.length; i++) {
      var x = parseInt(tmpArray[i])
      if (checkRole.value.indexOf(x) < 0) {
        checkRole.value.push(x)
      }
    }
    getRoleData()
}
const getRoleData = () => {
    const pars = { key:''}
    GetRoleslistData(pars).then((msg:any) => {
      roleCheckList.value = msg.data 
    }).catch((err:any) => { console.log(err); })
}
const saveRole = () => {
  var str = checkRole.value.join(',').replace('NaN,', '')
  const pars = { 
    user_id:  roleSelectRow.value.id, 
    role_ids: str
  }
  SaveRoleOfUserData(pars).then((res:any) => {
        if (res.data > 0) {
          roleDialogVisible.value  = false
          ElMessage({
            message: '保存成功！',
            type: 'success',
          })
          getData()
        }else{
          ElMessage({
            message: '保存失败！'+res.msg,
            type: 'error',
          })
        }
      }).catch(err => {
        console.log(err)
        // this.printLog(err); this.loading = false
      })

}

const handleSelectionChange = (rows: any) => {
  handlecurrentRow.value = rows
}

onMounted(() => {
  getTypeData()
})

const handleSizeChange = (size: number) => {
  userData.pageSize = size
  getData()
}

const handlePageChange = (page: number) => {
  userData.currentPage = page
  getData()
}
</script>

<style lang="scss"> 
.role_titleClass{
  color:red;
  font-size:12px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow:ellipsis;
}
</style>
