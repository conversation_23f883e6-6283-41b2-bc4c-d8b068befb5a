<template>
 <div class="moduleListfrom page" >
  <div class="header"> 
      <el-button type="success" plain size="default" icon="refresh" round @click="getData"></el-button>
      <el-button type="primary" plain @click="openRowExpansion()" size="default" round icon="View" aria-label="">{{openButtonText}}</el-button>
      <el-button type="success" plain @click="openAddNodeDialog()"  icon="CirclePlus" round size="default">添加父节点</el-button>
      <el-checkbox style="margin-left: 10px;" v-model="isShow" size="default" @change="getData()">显示全部</el-checkbox> 
  </div>
  <div class="body"> 
   <el-table
      border
      :height="tableHeight"
      :data="tableData.tableDataData" 
      :row-class-name="tableRowClassName"
      row-key="id"
      class="moduleTable"
      v-loading="tableData.loading" 
      :default-expand-all="false"
      highlight-current-row
      ref="tablexTree"
    >
      <el-table-column prop=""  label="#" align="center" width="60" />
      <el-table-column prop="id" label="节点编码" align="center"  min-width="80" />
      <el-table-column prop="pid" label="父节点" align="center" min-width="80" />
      <el-table-column prop="title" label="中文名称" header-align="center" align="left" width="160" show-overflow-tooltip />
      <el-table-column prop="path" label="节点路径" header-align="center" align="left"  min-width="250" />
      <el-table-column prop="icon" label="图标" align="center" min-width="60" show-overflow-tooltip>
        <template #default="scope">
            <el-icon :size="16">
              <component :is="scope.row.icon"></component>
            </el-icon>
        </template>
      </el-table-column>
     
      <el-table-column prop="sn" label="排序" header-align="center" align="center" min-width="80" />
      <el-table-column prop="icon" label="状态" align="center" min-width="60" show-overflow-tooltip>
        <template #default="scope">
          <span v-if="scope.row.status===1" style="color:blue">启用</span>
          <span v-else style="color:Red">禁用</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="操作" align="center" width="200" >
        <template #default ="scope">
          <el-button v-if="scope.row.pid===0" link type="primary" size="small" @click="openEditNode(scope.row)"><el-icon><Edit /></el-icon>编辑</el-button>
          <el-button v-if="scope.row.pid!==0" link type="primary" size="small" @click="openEditNode(scope.row)"><el-icon><Edit /></el-icon>编辑</el-button>
          <el-button v-if="((scope.row.pid===0 && scope.row.children.length===0) || scope.row.pid!==0) && scope.row.status!==1" link type="danger" size="small" @click="DetailClick(scope.row)"><el-icon><Delete /></el-icon>删除</el-button>
          <el-button v-if="scope.row.pid===0" link type="primary" size="small" @click="openAddChildNode(scope.row)"><el-icon><CirclePlusFilled /></el-icon>添加子节点</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
     <!--父节点编辑-->
    <el-dialog v-model="parentNodeVisible" 
      :title="node_title" 
      custom-class="parentNodeclass"
      draggable
      width="800"
      :close-on-press-escape="false"
      :close-on-click-modal="false">
      <div style="width:100%;text-align:center;">
        <el-form :model="modulefrom" :inline="true" :label-width="formLabelWidth">
          <el-form-item label="节点类型" >
            <el-radio-group v-model="modulefrom.type" :disabled="modulefrom_nodeType" style="width:235px;">
              <el-radio label="menu" >功能</el-radio>
              <el-radio label="menu_dir" >父节点</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="中文名称" >
            <el-input v-model="modulefrom.title"   style="width:235px;"/>
          </el-form-item>
          <el-form-item label="父节点" >
            <el-input v-model="modulefrom.pid" :disabled="modulefrom_pid"  style="width:235px;" />
          </el-form-item>
          <el-form-item label="节点编码" >
            <el-input v-model="modulefrom.id" :disabled="modulefrom_id"  style="width:235px" />
          </el-form-item>
          <el-form-item label="路径" >
            <el-input v-model="modulefrom.path"  style="width:590px;" placeholder="/文件夹名/文件名" />
          </el-form-item>
          <el-form-item label="是否启用" >
            <el-radio-group v-model="modulefrom.status" style="width:235px;">
              <el-radio :label="1"  @change="checkStatus()">启用</el-radio>
              <el-radio :label="0"  @change="checkStatus()">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="图标:">
            <div style="width:235px" >
              <span>
                <el-icon :size="20">
                  <component :is="modulefrom.icon"></component>
                </el-icon>
              </span>
              <a style="color:blue;float: right;" @click="iconVisible= true">查看</a>
            </div>
          </el-form-item>
          <el-form-item label="排序" >
             <el-input-number v-model="modulefrom.sn" :min="0" placeholder="排序" style="width:235px"  />
          </el-form-item>
          <el-form-item label="备注" >
            <el-input v-model="modulefrom.reamrk" type="textarea" style="width:235px;" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="parentNodeVisible = false">关闭</el-button>
          <el-button type="success" @click="SaveParent()"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      draggable
      title="请选择图标"
      custom-class="icondialogclass"
      v-model="iconVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div class="dialog_icon_div">
        <el-radio-group v-model="modulefrom.icon">
          <span v-for="item in svgIcons" :key="item" style="padding:20px;line-height:40px;">
            <el-radio :label="item">
              <el-icon :size="20">
                <component :is="item"></component>
              </el-icon>
            </el-radio>
          </span>
        </el-radio-group>
      </div>
      <template #footer>
          <span class="dialog-footer">
            <el-button type="success" @click="iconVisible=false">确定</el-button>
          </span>
      </template>
    </el-dialog>
    


 </div>
</template>

<script lang="ts" setup>
    import { defineComponent, onMounted, reactive, ref, toRefs } from "vue";
    import { GetKjZjModulesData,SaveKjZjModuleData,DelModuleData,DisablePerentNode } from '@/api/settings'
    import { ElMessage,ElMessageBox } from "element-plus";
    const svgIcons=ref( ['InfoFilled','CircleCheckFilled','SuccessFilled','WarningFilled','CircleCloseFilled','QuestionFilled','WarnTriangleFilled','UserFilled','MoreFilled','Tools','HomeFilled','Menu','UploadFilled','Avatar','HelpFilled','Share','StarFilled','Comment','Histogram','Grid','Promotion','DeleteFilled','RemoveFilled','CirclePlusFilled',
    'Plus','Minus','CirclePlus','Search','Female','Male','Aim','House','FullScreen','Loading','Link','Service','Pointer','Star','Notification','Connection','ChatDotRound','Setting','Clock','Position','Discount','Odometer','ChatSquare','ChatRound','ChatLineRound','ChatLineSquare','ChatDotSquare','View','Hide','Unlock','Lock','RefreshRight','RefreshLeft','Refresh','Bell','MuteNotification','User','Check','CircleCheck','Warning','CircleClose','Close','PieChart','More','Compass','Filter','Switch','Select','SemiSelect','CloseBold','EditPen','Edit','Message','MessageBox','TurnOff','Finished','Delete','Crop','SwitchButton','Operation','Open','Remove','ZoomOut','ZoomIn'
    ,'ArrowLeft','ArrowUp','ArrowRight','ArrowDown','ArrowLeftBold','ArrowUpBold','ArrowRightBold','ArrowDownBold','DArrowRight','DArrowLeft','Download','Upload','Top','Bottom','Back','Right','TopRight','TopLeft','BottomRight','BottomLeft','Sort','SortUp','SortDown','Rank','CaretLeft','CaretTop','CaretRight','CaretBottom','DCaret','Expand','Fold','DocumentAdd','Document','Notebook','Tickets','Memo','Collection','Postcard','ScaleToOriginal','SetUp','DocumentDelete','DocumentChecked','DataBoard','DataAnalysis','CopyDocument','FolderChecked','Files','Folder','FolderDelete','FolderRemove','FolderOpened','DocumentCopy','DocumentRemove','FolderAdd','FirstAidKit','Reading'
    ,'DataLine','Management','Checked','Ticket','Failed','TrendCharts','List','Microphone','Mute','Mic','VideoPause','VideoCamera','VideoPlay','Headset','Monitor','Film','Camera','Picture','PictureRounded','Iphone','Cellphone','VideoCameraFilled','PictureFilled','Platform','CameraFilled','BellFilled','Location','LocationInformation','DeleteLocation','Coordinate','Bicycle','OfficeBuilding','School','Guide','AddLocation','MapLocation','Place','LocationFilled','Van','Watermelon','Pear','NoSmoking','Smoking','Mug','GobletSquareFull','GobletFull','KnifeFork','Sugar','Bowl','MilkTea','Lollipop','Coffee','Chicken','Dish','IceTea','ColdDrink','CoffeeCup','DishDot','IceDrink','IceCream','Dessert','IceCreamSquare','ForkSpoon','IceCreamRound','Food','HotWater','Grape','Fries','Apple','Burger','Goblet','GobletSquare','Orange','Cherry','Printer','Calendar','CreditCard','Box','Money','Refrigerator','Cpu','Football','Brush','Suitcase','SuitcaseLine','Umbrella','AlarmClock','Medal','GoldMedal','Present','Mouse','Watch','QuartzWatch','Magnet','Help','Soccer','ToiletPaper','ReadingLamp','Paperclip','MagicStick','Basketball','Baseball','Coin','Goods','Sell','SoldOut','Key','ShoppingCart','ShoppingCartFull','ShoppingTrolley','Phone','Scissor','Handbag','ShoppingBag','Trophy','TrophyBase','Stopwatch','Timer','CollectionTag','TakeawayBox','PriceTag','Wallet','Opportunity','PhoneFilled','WalletFilled','GoodsFilled','Flag','BrushFilled','Briefcase','Stamp','Sunrise','Sunny','Ship','MostlyCloudy','PartlyCloudy','Sunset','Drizzling','Pouring','Cloudy','Moon','MoonNight','Lightning','ChromeFilled','Eleme','ElemeFilled','ElementPlus','Shop','SwitchFilled','WindPower'])
    const permsArray=ref(['申报', '搜索', '导出', '申请', '查看', '添加', '编辑', '删除'])
    const permsVisible=ref(false)
    
    const tableHeight= ref(window.innerHeight - 155)
    const isShow = ref(false);
    const parentNodeVisible = ref(false);
    const iconVisible = ref(false);
    const modulefrom_id = ref(false);
    const modulefrom_pid = ref(false);
    const modulefrom_nodeType = ref(false);
    const node_title = ref('');
    const formLabelWidth = '80px'
    const tablexTree = ref();
    const tableExpansion = ref(false);
    var modulefrom = ref({
        puid: 0,
        id: 0,
        pid:0,
        title: '',
        path: '',
        component: '',
        icon: '',
        sn: 0,
        reamrk: '',
        status: 1,
        type:'menu_dir'
      });
    const tableData = reactive({
      tableDataData:[],
      loading:false

    });
    const openButtonText = ref('展开全部');
    
    const getData=() => {
      tableData.loading=true
      var par = {
        status : isShow.value ? '' : '1'
      };
      GetKjZjModulesData(par).then((res:any) => {
        tableData.tableDataData = res.data
        tableData.loading=false
      })
    }
    //添加父节点
    const openAddNodeDialog=() => {
      modulefrom.value = {
        puid: 0,
        id: getPerentID(0),
        pid:0,
        title: '',
        path: '',
        component: '',
        icon: '',
        sn: 0,
        reamrk: '',
        status: 1,
        type:'menu_dir'
      }
      node_title.value="添加父节点"
      parentNodeVisible.value = true
      modulefrom_id.value = false;
      modulefrom_pid.value = false
      modulefrom_nodeType.value = false
    }
    //添加子节点
    const openAddChildNode=(row:any) => {
      modulefrom.value = {
        puid: 0,
        id: getPerentID(row.id),
        pid: row.id,
        title: '',
        path: '',
        component: '',
        icon:  '',
        sn: 0,
        reamrk:  '',
        status: 1,
        type: 'menu'
      }
      node_title.value="添加子节点"
      modulefrom_pid.value = true;
      modulefrom_nodeType.value = true;
      modulefrom_id.value = false;


      parentNodeVisible.value = true
    }
    //修改节点
    const openEditNode=(row:any) => {
      modulefrom.value = {
        puid: row.puid,
        id: row.id,
        pid: row.pid,
        title: row.title,
        path:  row.path,
        component:  row.component,
        icon:  row.icon,
        sn: row.sn,
        reamrk:  row.reamrk,
        status: row.status,
        type: row.pid===0? 'menu_dir':'menu'
      }
      node_title.value=row.pid===0? "编辑父节点":"编辑子节点"

      modulefrom_pid.value = row.pid ===0 ? true : false
      modulefrom_nodeType.value = row.pid === 0 ? true : false
      modulefrom_id.value = false

      parentNodeVisible.value = true

    }
    //获取ID值
    const getPerentID=(pid:any) => {
      var tsd:any=[]
      var djnum=0
      if (pid>0) {
        tsd = tableData.tableDataData.filter((item:any) => {
          if (item.id === pid) {
            return item
          }
        })
        tsd = tsd[0].children
        djnum=10
      }else{
        tsd = tableData.tableDataData.filter((item:any) => {
          if (item.pid === pid) {
            return item
          }
        })
        djnum=100
      } 
      var ids = tsd.map((item:any) => {
        return item.id
      }) 
      var maxid = Math.max(...ids)
      if(ids.length==0){
        maxid=pid
      }
      return maxid + djnum
    }
    //保存节点数据
    const SaveParent=() => {
      if (modulefrom.value.type === 'menu_dir') {
        modulefrom.value.component = 'Layout'
      } else {
        modulefrom.value.component = modulefrom.value.path
      }
      SaveKjZjModuleData(modulefrom.value).then((res:any) => {
        if (res.data === 1) {
          if (modulefrom.value.type === 'menu_dir') {
            if (modulefrom.value.status === 0) {
              // DisablePerentNode({ pid: modulefrom.value.id, status: modulefrom.value.status }).then((res:any) => {
              //   if (res.data < 0) {
              //     return
              //   }
              // })
            }
          }
          parentNodeVisible.value = false
          ElMessage({
            message: '保存成功！',
            type: 'success',
          })
          getData()
        }
      })
    }
    //禁用父节点时同步子节点状态
    const checkStatus=()=> {
      if (modulefrom.value.status === 0) {
        if (modulefrom.value.type === 'menu_dir') {
          ElMessage({
            message: '你正在禁用父节点，如果禁用其下所有子节点将会同时被禁用！！',
            type: 'warning',
          })
        }
      }
    }
    const tableRowClassName=( row:any )=> {
      if (row.status === 0) {
        return 'success-row'
      }
      return ''
    }
    //数据全部展开与关闭
    const openRowExpansion=()=> {
      if(tableExpansion.value){
        tableExpansion.value=false
        openButtonText.value='展开全部'
        dataTabdata(tableData.tableDataData,false)
      }else{
        tableExpansion.value=true
        openButtonText.value='全部折叠'
        dataTabdata(tableData.tableDataData,true)
      }
    }
    const dataTabdata=(data:any,sta:boolean)=>{
      data.forEach((i:any) => {
        tablexTree.value.toggleRowExpansion(i,sta)
        if(i.children){
          forArr(i.children,sta)
        }
      });
    }
    const forArr=(arr:any,sta:boolean)=>{
      arr.forEach((i:any) => {
        tablexTree.value.toggleRowExpansion(i,sta)
        if(i.children){
          forArr(i.children,sta)
        }
      });
    }
    const DetailClick=(row:any) => {
        ElMessageBox.confirm(
          '此操作将永久删除【' + row.title + '】节点功能, 是否继续?',
          '提示',
          {
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        .then(() => {
          const pars = { puid: row.puid }
          DelModuleData(pars).then((msg:any) => {
            if (msg.data > 0) {
              ElMessage({
                type: 'success',
                message: '成功删除!' + msg.data + '条'
              })
              getData()
            } else {
              ElMessage({
                type: 'info',
                message: '删除失败!' + msg.msg
              })
            }
          }).catch((err:any) => {
            ElMessage({
              type: 'info',
              message: '删除失败!' + err
            })
          })
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '取消删除!',
          })
        })
    }
    
    


  onMounted(() => {
      getData();
  });
  





</script>

<style  lang="scss">
.moduleListfrom{ 
  
  .parentNodeclass{
  margin-top: 10vh !important;
  width: 800px;
  }
  .icondialogclass{
  margin-top: 15vh !important;
  width: 700px;
  }
  .permsdialogclass{
  margin-top: 15vh !important;
  width: 700px;
  }
  .dialog_icon_div{
  height: 40vh;
  overflow: auto;
  }
  .elrow_4{
  margin-bottom: 10px;

  }
}

</style>
