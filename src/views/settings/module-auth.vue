<template>
  <div class="moduleAuth page">
    <div class="header"> 
        <el-button type="success" plain size="default" icon="refresh" round @click="getData"></el-button>
        <el-button type="primary" plain @click="openRowExpansion()" round icon="View" size="default" aria-label="">{{openButtonText}}</el-button>
        <el-tag size="large" style=" margin-left: 10px;margin-right: 5px;">角色:</el-tag>
        <el-select v-model="rolevalue" size="default" clearable filterable style="width: 200px;" @change="getData"  placeholder="专业编码及名称">
          <el-option
            v-for="item in roleData"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          >
          <span style="float: left">{{ item.id }}</span>
          <span style="float: right; color: #8492a6; font-size: 13px;margin-right: 10px;margin-left: 10px;">{{ item.title }}</span>
          </el-option>
        </el-select>
        <el-button type="success" plain size="default" round style=" margin-left: 10px;" icon="CircleCheck"  @click="saveAuth()">保存</el-button>
    </div>
    <div class="body"> 
      <el-table
        border
        :data="authDataTable" 
        :height="rAuthData.tableHeight"
        v-loading="rAuthData.loading" 
        row-key="id"
        class="modTable"
        ref="tableAuth"
        :row-class-name="tableRowClassName"
      >
        <el-table-column prop=""  label="#" align="center" width="60" />

        <el-table-column prop="id" label="节点编码" align="center"  width="80" />
        <el-table-column prop="pid" label="父节点" align="center" width="80" />
        <el-table-column prop="title" label="中文名称" header-align="center" align="center" width="160" show-overflow-tooltip />
        <!-- <el-table-column prop="title" label="节点功能" header-align="center" width="300" >
          <template #default="scope">{{ scope.row.id }}——{{ scope.row.title }}</template>
        </el-table-column> -->
        <el-table-column prop="address" label="操作" header-align="left" align="left" >
          <template #default="scope">
            <div v-if="scope.row.permsList.length!==0">
              <el-checkbox-group v-model="scope.row.checkList">
                <el-checkbox
                  v-for="item in scope.row.permsList"
                  :key="item"
                  :label="item"
                />
              </el-checkbox-group>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
 import { defineComponent, onMounted, reactive, ref, toRefs } from "vue";
 import { ElMessage } from "element-plus";
 import { GetRoleslistData,GetRoleUserAuthInfo,SetRoleUserAuth } from '@/api/settings'

 const rAuthData= reactive({
    loading:false,
    tableHeight:window.innerHeight - 180,
    keySearch:''
  })
  // const authDataTable: User[]=[]
  const openButtonText = ref('展开全部');
  const tableExpansion = ref(false);
  const tableAuth = ref();

  const authDataTable = ref<User[]>([])
  interface User {
    puid: number
    id: number
    pid: string
    path: string
    component: string
    title: string
    icon: string
    name: string
    permsList:[]
    checkList:[]
    hasChildren: boolean
    children: User[]
  }
  const rolevalue = ref('')
  const roleData = ref<roleoptionItem[]>([])
  interface roleoptionItem {
    id: number
    title: string
  }

  const getRoleAuth=()=> {
      const pars = {
        key: ""
      }
      GetRoleslistData(pars).then((msg:any) => {
        roleData.value = msg.data
        rolevalue.value= msg.data[0].id
        getData()
      }).catch((err:any) => { console.log(err); })
  }

  const getData=()=> { 
    rAuthData.loading = true
      const pars = {
        role_id: rolevalue.value
      }
      GetRoleUserAuthInfo(pars).then((msg:any) => { 
        permsCheck(msg.data) 
        rAuthData.loading = false
      }).catch((err:any) => { console.log(err); rAuthData.loading = false })
  }
  const permsCheck=(data:any)=> {
    var tableData = data
      for (var i = 0; i < tableData.length; i++) {
        tableData[i].permsList = []
        tableData[i].checkList = tableData[i].hasperms.split(',')
        for (var j = 0; j < tableData[i].children.length; j++) {
          var pars = tableData[i].children[j].perms
          tableData[i].children[j].permsList = pars.split(',')
          tableData[i].children[j].checkList = tableData[i].children[j].hasperms.split(',')
        }
      }
      authDataTable.value = tableData
  }
  
  const saveAuth=()=> {
    var roleAuthArray = []
      var table = authDataTable.value
      for (var i = 0; i < table.length; i++) {
        var authChild=table[i].children
        for (var j = 0; j < authChild.length; j++) {
          if (authChild[j].checkList.length !== 0) {
            var perms = authChild[j].checkList.join(',')
            if (perms !== '') {
              if (perms.indexOf(',') === 0) {
                perms = perms.replace(',', '')
              }
              roleAuthArray.push({ role_id: rolevalue.value, module_id: authChild[j].puid, perms: perms })
            }
          }
        }
      }
    
      var jsonStr = JSON.stringify(roleAuthArray)
      const pars = {
        json: jsonStr
      }
      console.log(pars)
      SetRoleUserAuth(pars).then((res) => {
        if (res.data === '1') {
          ElMessage({
            message: '保存成功！',
            type: 'success',
          })
          getData()
        } else {
          ElMessage({
            message: '保存失败！'+ res.msg,
            type: 'error',
          })
        }
      })
  }

  const tableRowClassName = ({
    row,
    rowIndex,
  }: {
    row: any
    rowIndex: number
  }) => {
    if (row.pid === 0) {
      return 'warning-row'
    } 
    return ''
  }

   //数据全部展开与关闭
   const openRowExpansion=()=> {
      if(tableExpansion.value){
        tableExpansion.value=false
        openButtonText.value='展开全部'
        dataTabdata(authDataTable.value,false)
      }else{
        tableExpansion.value=true
        openButtonText.value='全部折叠'
        dataTabdata(authDataTable.value,true)
      }
    }
    
    const dataTabdata=(data:any,sta:boolean)=>{
      data.forEach((i:any) => {
        tableAuth.value.toggleRowExpansion(i,sta)
        if(i.children){
          forArr(i.children,sta)
        }
      });
    }
    const forArr=(arr:any,sta:boolean)=>{
      arr.forEach((i:any) => {
        tableAuth.value.toggleRowExpansion(i,sta)
        if(i.children){
          forArr(i.children,sta)
        }
      });
    }
   
  onMounted(() => {
    getRoleAuth()
  });


</script>

<style lang="scss">
.moduleAuth{
  
  .el-table .warning-row {
    background: rgb(230, 242, 247);
  }

  .el-table .success-row {
    background: #f0f9eb;
  }
  .elrow_4{
    margin-bottom: 10px;
  }
}

</style>
