<template>
  <div>
    <div style="display: flex;justify-content: center;">
      <div style="width: 800px;padding: 0px 0px 0px 0px;height:calc(100vh - 50px);overflow-y: auto; ">
        <iframe style="width: 100%;height: 99%;border: 0px solid #fff;"
        :src="`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(fileurl)}&wdOrigin=BROWSELINK`"></iframe>
      </div>
      <div
        style="width: 800px;padding: 0px 0px 100px 0px;height:calc(100vh - 50px) ;overflow-y: auto; ">
        <el-skeleton v-if="loading" :rows="5" :animated="true" />
        <div v-for="(item, index) in questionList" :key="item.id" class="question-item">
          <div>{{ index + 1 }}、【id:{{ item.id }}】{{ item.questiontype }}</div>
          <div class="question-title" style="margin-top: 15px;">
            <div style="font-size: 17px;font-weight: bold;">题干：</div>
            <mavon-editor :toolbarsFlag="false" :boxShadow="false" :html="true" style="margin-top: 10px;"
              v-model="item.ques.title" />
          </div>
          <div v-if="item.ques.subQuestions">
            <div v-for="(sub, index) in item.ques.subQuestions" :key="index" style="margin-top: 10px;">
              {{ index+1 }}、{{sub.questionType}}
              <mavon-editor :toolbarsFlag="false" :boxShadow="false" :html="true" style="margin-top: 10px;"
                v-model="sub.title" />
              <div class="question-content" v-if="sub.options" style="background-color: antiquewhite;padding: 10px;">
                <div style="font-size: 17px;font-weight: bold;">选项：</div>
                <div v-for="(option, index) in sub.options" :key="index"
                  style="display: flex;align-items: start;gap: 10px;margin-top: 10px;">
                  <el-input v-model="option.listNo" style="width: 40px" />
                  <mavon-editor :toolbarsFlag="false" :boxShadow="false" :html="true" v-model="option.option"
                    class="content-show" style="flex: 1;height: 50px;" />
                </div>
              </div>
              <div class="question-answer" style="margin-top: 15px;">
                <div>
                  <div style="font-size: 17px;font-weight: bold;">答案：</div>
                  <mavon-editor :toolbarsFlag="false" :boxShadow="false" :html="true" v-model="sub.answer"
                    class="content-show" style="flex: 1;height: 50px;margin-top: 10px;" />
                </div>
              </div>
            </div>
          </div>
          <div v-else>
            <div class="question-content" v-if="item.ques.options"
              style="background-color: antiquewhite;padding: 10px;">
              <div style="font-size: 17px;font-weight: bold;">选项：</div>
              <div v-for="(option, index) in item.ques.options" :key="index"
                style="display: flex;align-items: start;gap: 10px;margin-top: 10px;">
                <el-input v-model="option.listNo" style="width: 40px" />
                <mavon-editor :toolbarsFlag="false" :boxShadow="false" :html="true" v-model="option.option"
                  class="content-show" style="flex: 1;height: 50px;" />
              </div>
            </div>
            <div class="question-answer" style="margin-top: 15px;">
              <div>
                <div style="font-size: 17px;font-weight: bold;">答案：</div>
                <mavon-editor :toolbarsFlag="false" :boxShadow="false" :html="true" v-model="item.ques.answer"
                  class="content-show" style="flex: 1;height: 50px;margin-top: 10px;" />
              </div>
            </div>
            <div class="question-answer" style="margin-top: 15px;">
              <div>
                <div style="font-size: 17px;font-weight: bold;">答案解析：</div>
                <mavon-editor :toolbarsFlag="false" :boxShadow="false" :html="true" v-model="item.answer_parsing"
                  class="content-show" style="flex: 1;height: 50px;margin-top: 10px;" />
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
    <div
      style="position: fixed;width: 100%;bottom: 0px;background-color: #fafafa;height: 60px;z-index: 9999;display: flex;align-items: center;justify-content: center;">
      <el-button type="primary" @click="openDia()">导入题库</el-button>
    </div>
    <el-dialog v-model="dialogVisible" title="导入" width="500">
      <div style="text-align: center;">
        <el-select v-model="kc" placeholder="选择课程" size="large" style="width: 240px">
          <el-option v-for="item in kcData" :key="item.id" :label="'【' + item.kc_bm + '】' + item.kc_mc"
            :value="item.id">

          </el-option>

        </el-select>

      </div>
      <div style="margin-top: 10px;text-align: center;">
        <el-select v-model="source" placeholder="选择来源" size="large" style="width: 240px">
          <el-option v-for="item in sourceData" :key="item.id" :label="item.title" :value="item.id" />
        </el-select>

      </div>
      <div style="margin-top: 10px;text-align: center;">
        <el-input v-model="ver" placeholder="版本" style="width: 240px" />
      </div>
      <template #footer>
        <div class="dialog-footer" style="text-align: center;">
          <el-button type="primary" @click="importKc()">
            导入
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog header-class="loadingHader" v-model="loading" width="300">
      <div style="display: flex;flex-direction: column;justify-content: center;align-items: center;">
        <img :src="loadinggif" style="width: 50px;">
        渲染中....
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang='ts'>
import { nextTick, onMounted, ref, computed } from "vue";
import { I_course_info } from "@/utils/types";
import { getQuestion, importJobbank } from "@/api/course";
import { GetJobSource } from "@/api/question";
import { msgShow } from "@/utils/message";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRoute } from 'vue-router';
import { getKcData } from '@/api/exam'
import loadinggif from '@/assets/loading.gif'
import { init } from "echarts";
import { id } from "element-plus/es/locale";
const sec = ref(1)
const questionList: any = ref([]); // 题库列表
const dialogVisible = ref(false); // 弹窗
const kcData: any = ref([])
const fileurl:any=ref("")
const kc = ref("2")
const source = ref("")
const route = useRoute();
const guid: any = ref("")
const loading = ref(false)
const sourceData: any = ref([])
const ver = ref("")
onMounted(() => {
  guid.value = route.query.guid
  if (route.query.guid) {
    initData();
    getSource();
    getKc()
  } else {
    ElMessage.error("缺少guid参数");
  }

  console.log(`output->route.query`, route.query)



})
const xssOptions = ref({
  ALLOW_UNKNOWN_PROTOCOLS: true, // 允许未知协议，例如允许javascript:等协议
  FORBID_TAGS: 'style', // 禁止某些HTML标签，例如style标签
  ADD_ATTR_ON_TAGS: ['a'], // 为某些HTML标签添加属性，例如给a标签添加target="_blank"
  ADD_TAG_HANDLER: {
    // 为特定标签添加自定义处理函数
    a(node: any) {
      node.attribs.target = '_blank';
      node.attribs.rel = 'noopener noreferrer';
    }
  }
})
const markdown = ref("")
const initData = () => {
  loading.value = true

  getQuestion(guid.value).then((res) => {
    if (res.code == 200) {
      const { b, q } = res.data
      console.log(`output->res.data`, res.data)
      if (q.length > 0) {
        var array: any = []
        q.forEach((e: any) => {
          e['ques'] = JSON.parse(e['title']) as any
          array.push(e)
        });
        questionList.value = array
        console.log(`output->questionList`, questionList.value)
      }
      if (b.length > 0) {
        var obj = b[0]
        console.log(obj)
        fileurl.value=obj.fileurl
        markdown.value = obj.markdown
      }
      msgShow("success", res.msg);
    } else {
      msgShow("error", res.msg);
    }
  }).catch((err) => {
    msgShow("error", err);
  }).finally(() => {
    // loading.value = false
    nextTick(() => {
      // DOM 更新完成后执行

      loading.value = false
      console.log("更新完成后执行")
    })
  })
}
const openDia = () => {
  //getKc()
  dialogVisible.value = true

}

const getKc = () => {
  getKcData({}).then((res: any) => {
    if (res.code == 200) {
      console.log(`output->res.data`, res.data)
      kcData.value = res.data
      kcData.value.forEach((e: any) => {
        e['id'] = e['id'].toString()
      });
      console.log(kcData.value)
    } else {
      msgShow("error", res.msg);
    }
  }).catch((err: any) => {
    msgShow("error", err);
  })
}
const importKc = () => {
  if (kc.value == "") {
    ElMessage.error("请选择课程");
    return
  }
  if (source.value == "") {
    ElMessage.error("请输入来源");
    return
  }
  if (questionList.value.length == 0) {
    ElMessage.error("没有题目");
    return
  }
  var questionArray: any = []

  questionList.value.forEach((e: any) => {
    var obj = {
      title: JSON.stringify(e.ques),
      questiontype: e.questiontype,
      is_autoscore: e.ques['options'] || e.ques['subQuestions'] ? 1 : 0,
      answer: e.ques.answer?e.ques.answer:e.answer,
      answer_parsing: e.answer_parsing,
      course_base_id: kc.value,
      jobbank_source_id: source.value,
      ver: e.ver?e.ver:ver.value,
      sn:e.sn?e.sn:"",
      knowledge_point: e.knowledge_point?e.knowledge_point:"",
      competence_level: e.competence_level?e.competence_level:"",
      difficulty_level: e.difficulty_level?e.difficulty_level:"",
      chapter_mc:e.chapter_mc?e.chapter_mc:"",
      chapter_no:e.chapter_no?e.chapter_no:""
    }
    questionArray.push(obj)
  });

  console.log(`output->questionArray`, questionArray)
  console.log(JSON.stringify(questionArray))
  let data = new FormData();
  data.append('jsonstr', JSON.stringify(questionArray))
  data.append('guid', guid.value)
  ElMessageBox.confirm('是否导入题库', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    importJobbank(data, { jsonstr: JSON.stringify(questionArray) }).then((res: any) => {
      if (res.code == 200) {
        //ElMessage.success('导入' + res.data + '道题');
        alert("导入" + res.data + "道题");
      } else {
        msgShow("error", res.msg);
      }
    }).catch((err: any) => {
      msgShow("error", err);
    })
  }).catch(() => {
    ElMessage.info("已取消导入");
  });
}
const getSource = () => {
  GetJobSource({}).then((res: any) => {
    sourceData.value = res.data
    console.log(`output->sourceData.value`, sourceData.value)
  })
}
</script>

<style>
.question-item {
  background-color: aliceblue;
  margin: 0px 0px 10px 0px;
  padding: 10px;
}

.v-note-wrapper {
  min-height: 20px !important;
  /* 注意使用 !important 来确保优先级 */
  height: auto !important;
  /* 注意使用 !important 来确保优先级 */
  background-color: initial;
  border-width: 0px !important;

}

::v-deep .v-note-wrapper {
  min-height: 60px;
}

.v-note-wrapper .v-note-panel .v-note-edit.divarea-wrapper .content-input-wrapper {
  padding: 5px 25px 5px 25px;
}

.v-note-wrapper .v-note-panel {
  flex-direction: column !important;
}

.v-note-wrapper .v-note-panel .v-note-edit.divarea-wrapper {
  flex: 0 0 100% !important;
  width: 100% !important;
}

.v-note-wrapper .v-note-panel .v-note-show {
  flex: 0 0 100% !important;
  width: 100% !important;

}

.v-note-wrapper .v-note-panel .v-note-show .v-show-content,
.v-note-wrapper .v-note-panel .v-note-show .v-show-content-html {
  padding: 5px 15px 5px 15px !important;
  margin-top: 10px;
  background-color: initial !important;
  font-size: 12px !important;

}

.markdown-body p {
  margin-bottom: 5px !important;
}

.auto-textarea-wrapper .auto-textarea-block {
  min-height: 20px !important;
  height: auto !important;
}
</style>