<template>
  <div class="quesbank page">
    <div class="header">
      <el-button type="primary" @click="getData" icon="RefreshRight"></el-button>
      <el-button type="primary" @click="openAdd()">添加</el-button>
    </div>

    <div class="body">
      <el-table :data="userData.dataTable" border class="modTable" :height="userData.tableHeight" style="width: 100%;"
        v-loading="userData.loading" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
        <el-table-column type="index" align="center" />
        <el-table-column prop="kc_mc" label="所属课程" width="100" align="center">
          <template #default="scope">
            <span v-if="scope.row.course_base_id">{{ scope.row.kc_mc }}</span>
            <span v-else>无课程</span>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" width="200" align="center" />
        <el-table-column prop="fileurl" label="文档url" align="left" show-overflow-tooltip header-align="center">
          <template #default="scope">
            <el-link type="primary" @click="openPreview(scope.row.fileurl)">{{ scope.row.fileurl }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="guid" width="120" label="guid" align="center" header-align="center"
          show-overflow-tooltip />
        <el-table-column prop="" width="120" label="AI分析" align="center" header-align="center">
          <template #default="scope">
            <el-button type="primary" @click="Analysis(scope.row.fileurl, scope.row.guid,scope.row.ex,scope.row.doc_type)">
              <svg viewBox="0 0 1024 1024" width="20" height="20">
                <path
                  d="M371.915599 1023.407835a40.06491 40.06491 0 0 1-34.950241-59.046017 470.435923 470.435923 0 0 0 25.971154-55.977214 340.977959 340.977959 0 0 0 17.048898-51.999139H372.938532a368.597173 368.597173 0 0 1-71.889519 8.979086 194.300607 194.300607 0 0 1-101.89558-29.949231 176.171945 176.171945 0 0 1-65.979235-128.037223 610.009568 610.009568 0 0 1-11.991058-114.966402 55.06794 55.06794 0 0 0-15.912305-11.024954c-10.00202-4.94418-25.00505-12.957162-38.019042-18.981106l-34.097796-15.969134A59.898461 59.898461 0 0 1 0.135964 500.461306c-1.989038-28.017022 18.015002-44.042986 51.999139-77.004189a592.335544 592.335544 0 0 0 49.896441-51.146694 98.428971 98.428971 0 0 0 18.981107-31.938269 301.992812 301.992812 0 0 1 21.993078-115.023231 344.387738 344.387738 0 0 1 72.912454-112.977364 401.274228 401.274228 0 0 1 295.51423-112.011259 306.539185 306.539185 0 0 1 265.735489 157.986454 522.83287 522.83287 0 0 1 64.899472 152.01934 39.837591 39.837591 0 0 1-77.856634 17.048898 437.986188 437.986188 0 0 0-55.977215-128.946498 228.171084 228.171084 0 0 0-196.801112-117.978374 319.155369 319.155369 0 0 0-236.752362 88.995247 244.367537 244.367537 0 0 0-71.88952 169.977513c0.966104 45.009091-48.930337 99.963372-93.882598 142.983424l-9.035916 8.922256h1.022934a353.366824 353.366824 0 0 1 56.82966 30.06289 83.369111 83.369111 0 0 1 42.963223 68.195592 496.577567 496.577567 0 0 0 10.968124 103.998277 120.819857 120.819857 0 0 0 30.972165 76.038085 140.539749 140.539749 0 0 0 114.852742 8.922257l30.00606-4.944181a61.83067 61.83067 0 0 1 61.8875 23.982117 158.782069 158.782069 0 0 1-8.012982 130.992365 743.729757 743.729757 0 0 1-32.904373 72.969284 39.780762 39.780762 0 0 1-36.996109 22.049908z m609.327611-485.041146h-161.850871a39.780762 39.780762 0 0 1-27.903362-12.218377l-138.834859-137.982414H461.87695a40.00808 40.00808 0 1 1 0-79.959331h207.826066a39.780762 39.780762 0 0 1 27.903363 11.991058l138.891688 137.982414h144.858803a40.00808 40.00808 0 1 1 0 79.959331z m-297.673757 229.989633H507.738485a40.00808 40.00808 0 1 1 0-80.016161h159.123048l82.914473-82.971303a39.780762 39.780762 0 0 1 27.960193-11.991058h147.757115a40.00808 40.00808 0 1 1 0 80.016161h-129.912602l-82.857644 82.971303a40.12174 40.12174 0 0 1-28.983126 11.991058z m-87.915484-187.992515h-193.78914a40.00808 40.00808 0 1 1 0-79.959331h193.78914a40.00808 40.00808 0 1 1 0 79.959331z m0 0"
                  fill="#00D0BD" p-id="12706"></path>
              </svg>
              &nbsp;&nbsp;AI解析
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="create_date" width="100" label="预览" align="center" header-align="center">
          <template #default="scope">
            <el-button type="primary" v-if="scope.row.question_num > 0" @click="toPreview(scope.row.guid)">预览
            </el-button>
            <div v-else></div>
          </template>
        </el-table-column>
        <el-table-column prop="question_num" width="50" label="题目数" align="center" header-align="center">
        </el-table-column>
        <el-table-column prop="import_num" width="80" label="导入题库数" align="center" header-align="center">
        </el-table-column>
        <el-table-column prop="create_date" label="创建日期" width="150" align="center" header-align="center" />
        <el-table-column prop="name" label="创建人" width="130" align="center" header-align="center" />
      </el-table>
    </div>
    <el-dialog v-model="dialogVisible" title="添加文档" width="600">
      <el-form label-position="right" label-width="auto" :model="formData" style="max-width: 600px">
        <el-form-item label="课程">
          <el-select v-model="formData.course_base_id" filterable placeholder="选择课程" size="large" style="width: 240px">
            <el-option v-for="item in kcData" :key="item.id" :label="'【' + item.kc_bm + '】' + item.kc_mc"
              :value="item.id">
            </el-option>

          </el-select>
        </el-form-item>
        <el-form-item label="标题">
          <el-input v-model="formData.title" />
        </el-form-item>
        <el-form-item label="类型">
          <el-radio-group v-model="formData.ex">
            <el-radio-button label="doc" value="doc"></el-radio-button>
            <el-radio-button label="pdf" value="pdf"></el-radio-button>
            </el-radio-group>
        </el-form-item>
         <el-form-item label="是否含有章节">
          <el-radio-group v-model="formData.doc_type">
            <el-radio-button label="真题" value="真题"></el-radio-button>
            <el-radio-button label="章节题" value="章节题"></el-radio-button>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="文件上传">
          <el-upload ref="uploadrefss" class="upload-demo"
            action="https://xczx7.swufe.edu.cn/oc/xczk/file/upload_to_drive" :headers="{
              'Authorization': 'Bearer ' + getToken()
            }" method="post" multiple :on-success="handleSuccess">
            <el-button type="primary">文件上传</el-button>
            <template #tip>
              <div class="el-upload__tip">

              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="saveData()">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="ResultdialogVisible" title="添加文档" width="600">
      <el-form label-position="right" label-width="auto" :model="formData" style="max-width: 600px">
        <el-form-item label="code">
          <el-input v-model="resultData.code" />
        </el-form-item>
        <el-form-item label="cost">
          <el-input v-model="resultData.cost" />
        </el-form-item>
        <el-form-item label="预览题库">
          <el-input v-model="resultData.out.output.preview_url" />
        </el-form-item>
        <el-form-item label="题目数量">
          <el-input v-model="resultData.out.output.result" />
        </el-form-item>
        <el-form-item label="debug_url">
          <el-input v-model="resultData.debug_url" />
        </el-form-item>
        <el-form-item label="msg">
          <el-input v-model="resultData.msg" />
        </el-form-item>
        <el-form-item label="token">
          <el-input v-model="resultData.token" />
        </el-form-item>
        <el-form-item label="返回数据">
          <el-input v-model="resultText" style="width: 100%" :rows="2" type="textarea" placeholder="Please input" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="ResultdialogVisible = false">Cancel</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :close-on-click-modal="false" :close-on-press-escape="false" header-class="loadingHader"
      v-model="LoadingdialogVisible" width="300">
      <div style="display: flex;flex-direction: column;justify-content: center;align-items: center;">
        <img :src="loadinggif" style="width: 50px;">
        AI解析中....{{ sec }}
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios'
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getTempJobbank, SaveTempJobbank, getTempJobbankCount } from '@/api/question'
import { getToken } from '@/utils/auth'
import { getKcData } from '@/api/exam'
import { CozeAPI, ChatStatus } from '@coze/api';
import { ArrowRight, ArrowLeft, Search, Loading } from '@element-plus/icons-vue'
import loadinggif from '@/assets/loading.gif'
import { RouteLocationMatched, useRoute, useRouter } from 'vue-router'
const CancelToken = axios.CancelToken

let uploadrefss = ref();
const router = useRouter();
const kcData: any = ref([])
const dialogVisible = ref(false)
const LoadingdialogVisible = ref(false)
const ResultdialogVisible = ref(false)
const userData = reactive({
  loading: false,
  tableHeight: window.innerHeight - 200,
  keySearch: '',
  learn_type: 0,
  dataTable: [] as any,
  total: 0,
  pageSize: 30,
  currentPage: 1
})
const handlecurrentRow = ref([]) as any
const resultText = ref("")
const resultData = ref({
  code: -1,
  cost: "",
  data: "",
  out: {
    output: {
      preview_url: "",
      result: ""
    }
  },
  debug_url: "",
  msg: "",
  token: ""
})
const cancel: any = ref(null)
const sec = ref(1)
const getData = () => {
  userData.loading = true
  const pars = {
    keySearch: userData.keySearch,
    learn_type: userData.learn_type,
    currentPage: userData.currentPage,
    pageSize: userData.pageSize,
  }
  getTempJobbank(pars).then((msg: any) => {
    userData.dataTable = msg.data || []
    userData.loading = false
  }).catch((err: any) => {
    console.log(err)
    userData.loading = false
  })
}
const formData = ref({ course_base_id: "", title: "", url: "",ex:"doc",doc_type:"真题" }) as any
const handleSelectionChange = (rows: any) => {
  handlecurrentRow.value = rows
}
const handleSizeChange = (size: number) => {
  userData.pageSize = size
  getData()
}
const getKc = () => {
  getKcData({}).then((res: any) => {
    if (res.code == 200) {
      console.log(`output->res.data`, res.data)
      kcData.value = res.data
      kcData.value.forEach((e: any) => {
        e['id'] = e['id'].toString()
      });
      console.log(kcData.value)
    } else {

    }
  }).catch((err: any) => {
    console.log("error", err);
  })
}
const handlePageChange = (page: number) => {
  userData.currentPage = page
  getData()
}
const saveData = () => {
  if (!formData.value.title || !formData.value.url) {
    ElMessage.error("缺少参数")
    return false
  }
  SaveTempJobbank(formData.value).then((res: any) => {
    if (res.data > 0) {
      ElMessage.success('保存成功！')
      dialogVisible.value = false
      formData.value = { course_base_id: "", title: "", url: "",ex:"doc",doc_type:"真题" }
      uploadrefss.value.clearFiles()
      getData()
    } else {
      ElMessage.error('保存失败！')
    }
  })
}

const calcTableHeight = () => {
  // 这里的220可根据实际页面头部、分页等高度调整
  userData.tableHeight = window.innerHeight - 200;
}

onMounted(() => {
  getData();
  calcTableHeight();

  window.addEventListener('resize', calcTableHeight);
});

onUnmounted(() => {
  window.removeEventListener('resize', calcTableHeight);
});
const openAdd = () => {
  dialogVisible.value = true
  formData.value = { course_base_id: "", title: "", url: "" }
  getKc()
  uploadrefss.value.clearFiles()
}
const handleSuccess = (response: any) => {
  const { success, url } = response
  if (success) {
    formData.value.url = url
    ElMessage.success('上传成功！')
  } else {
    ElMessage.error('上传失败！')
  }
}

const Analysis = async (url: string, guid: string,ex:String,doc_type:string) => {
  ElMessageBox.confirm(
    '是否进行AI分析，此过程可能耗时比较长，请耐心等待?',
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      AIrequest(url, guid,ex)
    })
    .catch(() => {

    })




}
const AIrequest = async (url: string, guid: string,ex:String,doc_type:string) => {
  LoadingdialogVisible.value = true

  var interval = setInterval(() => {
    sec.value = sec.value + 1
    if (sec.value % 10 == 0) {
      getTempCount(guid)
    }
  }, 1000);
  const baseurl = 'https://api.coze.cn/v1/workflow/run';
  var token = 'pat_9wrkHHVO3CClHe368Q1Dtr0f48YWLt81zvOUreBPU1MelJvs5JgWxq2PlW7dqKqy'
  // Headers
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };
  // 请求数据
  const data = {
    "workflow_id":doc_type=="真题"?(ex=="doc"?'7503831708500885514':'7517113904524197907'):'7516454105667633171',
    "parameters": {
      file_url: url,
      guid: guid
    }
  };

  // 发送请求
  try {
    const response: any = await axios.post(baseurl, data, {
      headers
      , cancelToken: new CancelToken(function executor(c) {
        cancel.value = c;
      })
    });
    if (response) {

      resultData.value = response.data
      if (resultData.value.code == 0) {
        ElMessage.success("分析成功")
        resultText.value = JSON.stringify(response.data)
        resultData.value.out = JSON.parse(resultData.value.data)
        ResultdialogVisible.value = true
        getTempCount(guid)
        getData()
      } else {
        ElMessage.error("分析失败")
      }

    }
    console.log(response)

  } catch (error) {

    if (axios.isCancel(error)) {
      console.log('Request canceled', error.message);
    } else {
      ElMessage.error("分析失败")
      console.log(error)
      // 处理错误情况
    }

  } finally {
    LoadingdialogVisible.value = false
    clearInterval(interval)
    sec.value = 0
  }
}
const toPreview = (guid: any) => {
  const { href } = router.resolve({
    path: '/temp_question',
    query: {
      guid
    }
  });
  window.open(href, "_blank");
}
const getTempCount = (guid: string) => {
  console.log("请求是否成功，guid:" + guid)
  getTempJobbankCount({ guid }).then((res: any) => {
    console.log(res.data)
    var total = res.data.total
    if (total > 0) {
      LoadingdialogVisible.value = false
      alert("解析成功：" + total + "题")
      getData()
      cancel.value('Operation canceled by the user.');
    }
  })
}
const tableRowClassName = ({
  row,
  rowIndex,
}: {
  row: any
  rowIndex: number
}) => {
  if (row.import_num > 0) {
    return 'success-row'

  } else if (row.question_num > 0) {
    return 'warning-row'
  } else {
    return 'error-row'
  }

}
const openPreview = (url: string) => {
  window.open(`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(url)}&wdOrigin=BROWSELINK`, '_blank');
}
</script>
<style>
.loadingHader {
  display: none;
}

.el-table .warning-row {
  --el-table-tr-bg-color: rgb(252.5, 245.7, 235.5);
}

.el-table .success-row {
  --el-table-tr-bg-color: rgb(239.8, 248.9, 235.3);
}

.el-table .error-row {
  --el-table-tr-bg-color: rgb(254, 240.3, 240.3);
}
</style>
<style lang="scss"></style>
