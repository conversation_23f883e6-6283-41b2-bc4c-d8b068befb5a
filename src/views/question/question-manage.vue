<template>
  <div class="questionManage page">
    <div>
      <div>
        
        <el-tag class="el_tag_5">课程编码</el-tag>
        <el-select v-model="searchForm.course_base_id" clearable filterable style="width: 160px;margin:0 10px;" placeholder="课程编码" @change="getSelData">
          <el-option v-for="item in course_base_list" :key="item.id" :label="item.kc_mc" :value="item.id" >
              <span style="float: left">{{ item.kc_mc }} <span> {{ '('+item.id+')' }} </span></span>
              <span style="float: right; color: #67C23A; font-size: 13px;">{{ item.kc_bm }}</span>
          </el-option> 
        </el-select>
        <el-tag class="el_tag_5">试题来源</el-tag>
        <el-select v-model="searchForm.jobbank_source_id" clearable filterable style="width: 160px;margin:0 10px;" placeholder="试题来源" @change="getSelData">
            <el-option v-for="item in jobbank_source_list" :key="item.id" :label="item.title" :value="item.id">
              <span style="float: left">{{ item.id }}</span>
              <span style="float: right; color: #67C23A; font-size: 13px;">{{ item.title }}</span>
            </el-option>
        </el-select>
        <el-tag class="el_tag_5">版本(套题)</el-tag>
        <el-select v-model="searchForm.job_ver" clearable filterable style="width: 120px;margin:0 10px;" placeholder="版本" @change="getData"> 
          <el-option v-for="item in job_ver_list" :key="item.ver" :label="item.ver" :value="item.ver" />
        </el-select>
        <el-button type="success" plain size="default" round icon="View" @click="previewJobClick('all')">预览套题</el-button>
      </div>
      <div style="margin-top: 10px;">
      <el-button type="success" plain size="default" icon="refresh" round @click="getData"></el-button> 
       <el-input v-model="searchForm.keySearch" placeholder="试题编号ID" size="default" style="width: 260px;margin:0 10px;" clearable @change="getData">
          <template #append>
            <el-button icon="Search" @click="getData"/>
          </template>
        </el-input>
        <!-- <el-button type="primary"  plain size="default" round icon="CirclePlus" @click="AddJobData()">新增</el-button> -->
        <el-button type="primary" plain size="default" round icon="edit" @click="handleJobEdit()">编辑</el-button>
        <!-- <el-button type="danger" plain size="default" round icon="delete" @click="DetailJobClick()">删除</el-button> -->
       
      </div>
    </div>
    <div class="body">
      <el-table
      :data="searchForm.dataTable"
        border
        class="modTable"
        :height="searchForm.tableHeight"
        style="width: 100%;"
        v-loading="searchForm.loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column prop="" type="index" label="序号"  align="center" width="60" header-align="center" />
        <el-table-column type="selection" width="50" align="center"/>
        <el-table-column prop="id" label="编码(ID)" min-width="80" align="center" header-align="center" show-overflow-tooltip />
        <el-table-column prop="questiontype" label="题型" min-width="120" align="center" header-align="center" show-overflow-tooltip />
        <el-table-column prop="question_title" label="试题内容"  min-width="200" align="left" header-align="center" show-overflow-tooltip />
        <el-table-column prop="answer" label="答案"  min-width="200" align="left" header-align="center" show-overflow-tooltip />
        <el-table-column label="操作" min-width="80" align="center" header-align="center">
          <template #default="scope">
            <el-link type="primary" @click="previewJobClick(scope.row)">预览</el-link>
            <el-link type="primary" @click="handleAITest(scope.row)">AI测试</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="sn" label="题序"  min-width="80" align="center" header-align="center" show-overflow-tooltip />
        <el-table-column prop="answer_parsing" label="答案解析"  min-width="200" align="left" header-align="center" show-overflow-tooltip />
        <el-table-column prop="knowledge_point" label="知识点" min-width="120" align="left" header-align="center" show-overflow-tooltip/>
        <el-table-column prop="jobbank_source_title" label="试题来源"  min-width="120" align="left" header-align="center" show-overflow-tooltip />
        <el-table-column prop="difficulty_level" label="难易度" min-width="120" align="center" header-align="center" show-overflow-tooltip />
        <el-table-column prop="chapter_mc" label="章节" min-width="120" align="left" header-align="center" show-overflow-tooltip />
        <el-table-column prop="is_autoscore" label="题类" min-width="80" align="center" header-align="center">
          <template #default="scope">
            <span :style="{color: scope.row.is_autoscore === 1 ? 'red' : '#222'}">{{ scope.row.is_autoscore === 1 ? '客观题' : '主观题' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="80" align="center" header-align="center">
          <template #default="scope">
            <span :style="{color: scope.row.status === 1 ? 'green' : 'red'}">{{ scope.row.status === 1 ? '启用' : '禁用' }}</span>
          </template>
        </el-table-column> 
        <el-table-column prop="in_markdown_section" label="markdown" min-width="80" align="center" header-align="center">
          <template #default="scope">
            <span :style="{color: scope.row.in_markdown_section === 1 ? 'red' : '#222'}">{{ scope.row.in_markdown_section === 1 ? '是' : '否' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="created_user" label="创建人" min-width="80" align="center" header-align="center" show-overflow-tooltip/>
        <el-table-column prop="created_date" label="创建时间" min-width="140" align="center" header-align="center" show-overflow-tooltip/>
        <el-table-column prop="modify_user_name" label="修改人" min-width="80" align="center" header-align="center" show-overflow-tooltip/>
        <el-table-column prop="modify_date" label="修改时间" min-width="140" align="center" header-align="center" show-overflow-tooltip/>
      </el-table>
      <div style="margin-top: 16px; text-align: right;">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="searchForm.total"
          :page-size="searchForm.pageSize"
          :current-page="searchForm.currentPage"
          :page-sizes="[20, 50, 100, 500, searchForm.total]"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>


    <!-- 预览 -->
    <el-dialog v-model="init_dialog_preview.dialogShow"
             align-center draggable
             v-if="init_dialog_preview.dialogShow"
             :show-close="true"
             :fullscreen="true"
             :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="dialog_content" v-loading="init_dialog_preview.loading"
          :element-loading-text="init_dialog_preview.loadingText"
          :element-loading-spinner="init_dialog_preview.loadingSvg"
          element-loading-svg-view-box="-10, -10, 50, 50"
          element-loading-background="rgba(122, 122, 122, 0.8)">
        <PreviewQuestionBank :coursebaseid="init_dialog_preview.course_base_id"  
          :jobbanksourceid="init_dialog_preview.jobbank_source_id"  
          :jobbankver="init_dialog_preview.jobbank_ver" 
          :jobbankid="init_dialog_preview.jobbank_id"></PreviewQuestionBank>
      </div>
    </el-dialog>

    <!-- 题库编辑 -->
    <el-dialog v-model="init_dialog_adup.dialogShow"
              align-center draggable
              v-if="init_dialog_adup.dialogShow"
              :show-close="true"
              :fullscreen="true"
              :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="dialog_content" v-loading="init_dialog_adup.loading"
          :element-loading-text="init_dialog_adup.loadingText"
          :element-loading-spinner="init_dialog_adup.loadingSvg"
          element-loading-svg-view-box="-10, -10, 50, 50"
          element-loading-background="rgba(122, 122, 122, 0.8)">
        <UpQuestionJob :jobbankid="init_dialog_adup.jobbankid"></UpQuestionJob>
      </div>
    </el-dialog>

    <!-- AI测试 -->
    <el-dialog v-model="init_dialog_ai_test.dialogShow"
             align-center draggable
             v-if="init_dialog_ai_test.dialogShow"
             :title="init_dialog_ai_test.title"
             :show-close="true"
             :fullscreen="true"
             :close-on-click-modal="false" 
             :close-on-press-escape="false">
      <div class="dialog_content" v-loading="init_dialog_ai_test.loading"
          :element-loading-text="init_dialog_ai_test.loadingText"
          :element-loading-spinner="init_dialog_ai_test.loadingSvg"
          element-loading-svg-view-box="-10, -10, 50, 50"
          element-loading-background="rgba(122, 122, 122, 0.8)">
        <QuestionAITest 
          :coursebaseid="init_dialog_ai_test.course_base_id"  
          :jobbanksourceid="init_dialog_ai_test.jobbank_source_id"  
          :jobbankver="init_dialog_ai_test.jobbank_ver" 
          :jobbankid="init_dialog_ai_test.jobbank_id"
        ></QuestionAITest>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted,onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 这里请替换为实际接口调用
import { GetQuestionsJobPage,GetBaseKcData,GetJobVerData,GetJobSource } from '@/api/question'
import PreviewQuestionBank from "@/components/question/preview_question_bank.vue";
import UpQuestionJob from "@/components/question/up_question_job.vue";
import QuestionAITest from "@/components/question/question_ai_test.vue";
import { useRouter } from 'vue-router';
const router = useRouter();
const searchForm = reactive({
  loading: false,
  tableHeight: window.innerHeight - 240,
  keySearch: '',
  course_base_id:3,
  jobbank_source_id:'',
  job_ver:'',
  dataTable: [] as any,
  total: 0,
  pageSize: 30,
  currentPage: 1
}) 
const handlecurrentRow = ref([]) as any
const course_base_list = ref([]) as any
const jobbank_source_list = ref([]) as any
const job_ver_list = ref([]) as any

// AI测试对话框
const init_dialog_ai_test = reactive({
  dialogShow: false,
  title: 'AI 测试',
  loading: false,
  loadingText: "初始化中，请稍后...",
  loadingSvg: `
    <path class="path" d="
      M 30 15
      L 28 17
      M 25.61 25.61
      A 15 15, 0, 0, 1, 15 30
      A 15 15, 0, 1, 1, 27.99 7.5
      L 15 15
    " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
  `,
  course_base_id: '' as number|string,
  jobbank_source_id: '' as number|string,
  jobbank_ver: '' as number|string,
  jobbank_id: '' as number|string
});

// 预览
const init_dialog_preview = reactive({
  dialogShow: false,
  title: '',
  loading: false,
  loadingText: "获取数据中，请稍后...",
  loadingSvg: `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `,
  course_base_id: '' as number|string,
  jobbank_source_id: '' as number|string,
  jobbank_ver: '' as number|string,
  jobbank_id: '' as number|string
})
//新增编辑
const init_dialog_adup = reactive({
  dialogShow: false,
  title: '',
  loading: false,
  loadingText: "获取数据中，请稍后...",
  loadingSvg: `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `,
  jobbankid: 0,
  data: [] as any
})

const getSelData = () => {
    getData()
    getVerData()
}

const getJobbankSource = () => {
    GetJobSource({}).then((res: any) => {
      if (res.code == 200) {
        jobbank_source_list.value = res.data
      } else {
        ElMessage.error(res.msg)
      }
    })
    
}
const getKcData = () => {
    GetBaseKcData({}).then((res: any) => {
      if (res.code == 200) {
        course_base_list.value = res.data
      } else {
        ElMessage.error(res.msg)
      }
    })
}

const getVerData = () => {
    const pars = {
      course_base_id: searchForm.course_base_id,
      jobbank_source_id: searchForm.jobbank_source_id===undefined?'':searchForm.jobbank_source_id
    }
    GetJobVerData(pars).then((res: any) => {
      if (res.code == 200) {
        job_ver_list.value = res.data
      } else {
        ElMessage.error(res.msg)
      }
    })
}

const getData = () => {
  searchForm.loading = true
  const pars = {
    keySearch: searchForm.keySearch,
    course_base_id: searchForm.course_base_id,
    jobbank_source_id: searchForm.jobbank_source_id===undefined?'':searchForm.jobbank_source_id,
    ver: searchForm.job_ver===undefined?'':searchForm.job_ver,
    currentPage: searchForm.currentPage,
    pageSize: searchForm.pageSize,
  }
  GetQuestionsJobPage(pars).then((msg: any) => {
    searchForm.dataTable = msg.data || []
    searchForm.total = msg.total || searchForm.dataTable.length
    searchForm.loading = false
  }).catch((err: any) => {
    console.log(err)
    searchForm.loading = false
  })
}

const AddJobData = () => {
    init_dialog_adup.title = '新增题库'
    init_dialog_adup.dialogShow = true
    init_dialog_adup.jobbankid = 0
  }

const handleJobEdit = () => {
  if (handlecurrentRow.value.length === 1) {
    init_dialog_adup.jobbankid = handlecurrentRow.value[0].id
    init_dialog_adup.dialogShow = true
  } else {
    ElMessage({
      message: '请勾选一条数据进行编辑！',
      type: 'error',
    })
    return false
  }
}

const previewJobClick = (row: any) => {
  if (row === 'all') {
    // 预览套题
    if (!searchForm.course_base_id) {
      ElMessage.warning('请选择课程编码');
      return;
    }
    if (searchForm.jobbank_source_id === '') {
      ElMessage.warning('请选择题库来源');
      return;
    }
    if (searchForm.job_ver === '') {
      ElMessage.warning('请选择版本');
      return;
    }
    init_dialog_preview.title = '预览套题';
    init_dialog_preview.course_base_id = searchForm.course_base_id;
    init_dialog_preview.jobbank_source_id = searchForm.jobbank_source_id;
    init_dialog_preview.jobbank_ver = searchForm.job_ver;
    init_dialog_preview.jobbank_id = '';
  } else {
    // 预览单题
    init_dialog_preview.title = '预览题目';
    init_dialog_preview.course_base_id = searchForm.course_base_id;
    init_dialog_preview.jobbank_source_id = searchForm.jobbank_source_id;
    init_dialog_preview.jobbank_ver = searchForm.job_ver;
    init_dialog_preview.jobbank_id = row.id;
  }
  init_dialog_preview.dialogShow = true;
};

// 处理AI测试
const handleAITest = (row: any) => {
  if (!row) {
    ElMessage.warning('请选择要测试的题目');
    return;
  }
  
  init_dialog_ai_test.title = 'AI 测试 - ' + row.questiontype;
  init_dialog_ai_test.course_base_id = searchForm.course_base_id;
  init_dialog_ai_test.jobbank_source_id = searchForm.jobbank_source_id;
  init_dialog_ai_test.jobbank_ver = searchForm.job_ver;
  init_dialog_ai_test.jobbank_id = row.id;
  init_dialog_ai_test.dialogShow = true;
};

const handleSelectionChange = (rows: any) => {
  handlecurrentRow.value = rows;
};

const handleSizeChange = (size: number) => {
  searchForm.pageSize = size;
  getData();
};

const handlePageChange = (page: number) => {
  searchForm.currentPage = page;
  getData()
}

const calcTableHeight = () => {
  // 这里的220可根据实际页面头部、分页等高度调整
  searchForm.tableHeight = window.innerHeight - 240;
}

onMounted(() => {
  getKcData()
  getJobbankSource()
  getSelData() 
  calcTableHeight();
  window.addEventListener('resize', calcTableHeight);
});

onUnmounted(() => {
  window.removeEventListener('resize', calcTableHeight);
});

 
</script>

<style lang="scss"> 


</style>
