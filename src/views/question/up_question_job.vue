<template>
  <div>
    <el-card>
      <div style="margin-bottom: 16px; display: flex; justify-content: space-between; align-items: center;">
        <div>
          <!-- <el-button type="success" plain size="default" round icon="CirclePlus" style="margin-right: 20px;" @click="addMainOption"> 预览显示</el-button> -->
          <span>题库维护（ Job ID: {{ jobId }} ）  课程：{{ datajob.kc_bm }} {{ datajob.kc_mc }} </span>
        </div>
        <div>
          <el-tag class="el_tag_5" >题型</el-tag>
          <el-input v-model="datajob.questiontype" placeholder="题型" style="width:200px;margin:0 10px;" />
        </div>
      </div>
      <div style="height: 700px;overflow-y: auto;">
        <el-form :model="datajob" :inline="true" :label-width="formLabelWidth">
          <el-form-item label="类型">
              <el-radio-group v-model="datajob.is_autoscore" style="width:200px;">
                <el-radio :label="1">客观题</el-radio>
                <el-radio :label="0">主观题</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="状态">
              <el-radio-group v-model="datajob.status" style="width:200px;">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="markdown">
              <el-radio-group v-model="datajob.in_markdown_section" style="width:200px;">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="试题来源">
              <el-select v-model="datajob.jobbank_source_id" clearable filterable style="width: 200px;" placeholder="试题来源">
                  <el-option v-for="item in jobbank_source_list" :key="item.id" :label="item.title" :value="item.id" style="width: 260px;">
                    <span style="float: left">{{ item.id }}</span>
                    <span style="float: right; color: #67C23A; font-size: 13px;">{{ item.title }}</span>
                  </el-option> 
              </el-select>
            </el-form-item>
            <el-form-item label="试题能力层次">
              <el-select v-model="datajob.competence_level" clearable filterable style="width: 200px;" placeholder="试题能力层次">
                  <el-option label="识记" value="识记" style="width: 200px;"/>
                  <el-option label="领会" value="领会" style="width: 200px;"/>
                  <el-option label="应用" value="应用" style="width: 200px;"/>
              </el-select>
            </el-form-item>
            <el-form-item label="难度能力层次">
              <el-select v-model="datajob.difficulty_level" clearable filterable style="width: 200px;" placeholder="难度能力层次">
                  <el-option label="易" value="易" style="width: 200px;"/>
                  <el-option label="较易" value="较易" style="width: 200px;"/>
                  <el-option label="中等" value="中等" style="width: 200px;"/>
                  <el-option label="较难" value="较难" style="width: 200px;"/>
                  <el-option label="难" value="难" style="width: 200px;"/>
              </el-select>
            </el-form-item>
            
            <el-form-item label="章节号">
              <el-input-number v-model="datajob.chapter_no" :min="0" placeholder="章节号" style="width:200px"  />
            </el-form-item>
            <el-form-item label="章节名称">
              <el-input v-model="datajob.chapter_mc" placeholder="章节名称" style="width:520px" />
            </el-form-item> 
            <el-form-item label="主题干">
              <div style="width: 740px; height: 410px;"> 
                  <v-md-editor v-model="datajob.title.title" height="400px"/> 
              </div>
            </el-form-item>

            <el-form-item v-if="!('subQuestions' in datajob.title)" label="主题答案" >
              <div style="width: 740px; height: 310px;">
                  <v-md-editor v-model="datajob.answer" height="300px"/>
              </div>
            </el-form-item>
            <el-form-item label="主题答案解析">
              <div v-if="datajob.in_markdown_section" style="width: 740px; height: 310px;">
                  <v-md-editor v-model="datajob.answer_parsing" height="300px"/>
              </div>
              <el-input  v-else v-model="datajob.answer_parsing" type="textarea" :autosize="{ minRows: 4, maxRows: 6}" style="width:360px;" />
            </el-form-item>
            <el-form-item label="知识点">
              <el-input v-model="datajob.knowledge_point" type="textarea" :autosize="{ minRows: 4, maxRows: 6}" style="width:360px;" />
            </el-form-item>
        </el-form> 
        <!-- 主题选择题 -->
        <div class="options_div" v-if="'options' in datajob.title">
          <el-button type="primary" plain size="default" round icon="CirclePlus" @click="addMainOption"> 新增选项</el-button>
          <el-table ref="questionTable" :data="datajob.title.options" border style="width: 100%;margin:10px;" v-loading="loading">
            <el-table-column prop="listNo" label="选项名" align="center" header-align="center"  width="60">
              <template #default="scope">
                <el-input v-model="scope.row.listNo" placeholder="请输入选项名" />
              </template>
            </el-table-column>
             <el-table-column prop="answer" label="是否为答案" align="center" header-align="center" width="80">
              <template #default="scope">
                <el-checkbox v-model="scope.row.answer" @change="handleAnswerChange(scope.$index, scope.row)"/>
              </template>
            </el-table-column> 
            <el-table-column prop="option" label="选项内容"  min-width="250">
              <template #default="scope">
                <div v-if="datajob.in_markdown_section" style="height: 210px;">
                    <v-md-editor v-model="scope.row.option" height="200px"/>
                </div>
                <el-input v-else v-model="scope.row.option" placeholder="选项内容" />
              </template>
            </el-table-column> 
            <el-table-column label="操作" align="center" header-align="center" width="100">
              <template #default="scope">
                <el-button type="danger" size="small" @click="removeMainOption(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="width: 100%;">
            <div style="display: flex; justify-content: space-between; align-items: center;float: left;margin:10px; ">
              <el-tag class="el_tag_5" >选项答案(A,B)</el-tag>
              <el-input v-if="'options' in datajob.title && datajob.title.options.length > 0" v-model="datajob.title.answer" placeholder="选项答案" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" style="width:360px;margin:0 10px;" />
            </div>
            <div style="display: flex; justify-content: space-between; align-items: center;float: left; margin:10px;">
              <el-tag class="el_tag_5" >选项答案解析</el-tag>
              <el-input v-if="datajob.title.options.length > 0" v-model="datajob.title.answer_parsing" placeholder="选项答案解析" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" style="width:360px;margin:0 10px;" />
            </div>
          </div>
         
        </div>
        <!-- 组合题子题 -->
        <div class="options_div" v-if="'subQuestions' in datajob.title">
          <el-button type="primary" plain size="default" round icon="CirclePlus" @click="addSubQuestion"> 新增子题</el-button>
          <el-collapse v-model="datajob.title.subQuestions" style="margin: 10px;">
            <el-collapse-item v-for="(item,index) in  datajob.title.subQuestions" :title="'第'+ (index+1) +'题'" :key="index" :name="index+1">
                  <el-button type="danger" plain size="default" round icon="Delete" @click="removeSubQuestion(index)"> 删除本子题</el-button>
                  <div style="width: 800px; height: 310px;margin:10px;">
                      <v-md-editor v-model="item.title" height="300px"/>
                  </div>
                  <div v-if="'options' in item" style="width: 800px;">
                      <el-button type="primary" plain size="default" round icon="CirclePlus" @click="addSubOption(index)"> 新增选项</el-button>
                      <el-table :data="item.options" border style="width: 100%;margin:10px;" v-loading="loading">
                        <el-table-column prop="listNo" label="选项名" align="center" header-align="center"  width="80">
                          <template #default="scope">
                            <el-input v-model="scope.row.listNo" placeholder="请输入选项名" />
                          </template>
                        </el-table-column>
                        <el-table-column prop="answer" label="是否为答案" align="center" header-align="center" width="80">
                          <template #default="scope">
                            <el-checkbox v-model="scope.row.answer" @change="handleSubQuestionAnswerChange(index, scope.$index, scope.row)" />
                          </template>
                        </el-table-column>
                        <el-table-column prop="option" label="选项内容" align="center" header-align="center"  min-width="250">
                          <template #default="scope">
                            <div v-if="datajob.in_markdown_section" style="height: 210px;">
                                <v-md-editor v-model="scope.row.option" height="200px"/>
                            </div>
                            <el-input v-else v-model="scope.row.option" placeholder="选项内容" />
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center" header-align="center" width="100">
                          <template #default="scope">
                            <el-button type="danger" size="small" @click="removeSubOption(index, scope.$index)">删除</el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                      <div style="width: 100%;margin:10px;">
                         <el-tag class="el_tag_5" >选项答案(A,B,C,D)</el-tag>
                          <el-input v-if="item.options.length > 0" v-model="item.answer" placeholder="选项答案" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" style="width:360px;margin:0 10px;" />
                      </div>
                      <div style="width: 100%;margin:10px;">
                        <el-tag class="el_tag_5" >选项答案解析</el-tag>
                        <el-input v-if="item.options.length > 0"  v-model="item.answer_parsing" placeholder="选项答案解析" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" style="width:360px;margin:0 10px;" />
                      </div>
                  </div>
                  <div v-else>
                      <div style="width: 100%;margin:10px;">
                         <el-tag class="el_tag_5" >答案</el-tag>
                         <div style="height: 310px;">
                            <v-md-editor v-model="item.answer" height="300px"/>
                         </div>
                      </div>
                      <div style="width: 100%;margin:10px;">
                        <el-tag class="el_tag_5" >答案解析</el-tag>
                        <el-input v-model="item.answer_parsing" placeholder="答案解析" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" style="width:360px;margin:0 10px;" />
                      </div>
                  </div>
            </el-collapse-item>
          </el-collapse>
          <div style="width: 100%;margin:10px;">
              <el-tag class="el_tag_5" style="margin: 10px;">
                组合题选项答案([{"sn":1,"answer":"B,D"},{"sn":2,"answer":"B"},{"sn":3,"answer":"C"},{"sn":4,"answer":"B"},{"sn":5,"answer":"B"}])
              </el-tag>
              <br>
              <el-input v-if="datajob.title.subQuestions.length > 0" v-model="datajob.answer" disabled placeholder="选项答案解析" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" style="width:500px;margin:0 10px;" />
          </div>
          <div style="width: 100%;margin:10px;">
            <el-tag class="el_tag_5" style="margin: 10px;">
              组合题选项答案解析
            </el-tag>
            <br>
            <el-input v-if="datajob.title.subQuestions.length > 0" v-model="datajob.answer_parsing" placeholder="选项答案解析" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" style="width:500px;margin:0 10px;" />
          </div>
        </div>
      </div>
      <div style="margin-top: 16px; text-align: right;">
        <el-button type="primary" @click="saveQuestions" :loading="saving">保存题库</el-button>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, defineProps,onUnmounted } from 'vue';
import { ElMessage,ElMessageBox } from 'element-plus'
import { GetJobIdQuestions,GetJobSource,SaveJobBankIdQuestions } from '@/api/question'
import {toolsUtils} from "@/utils/tools";

// 接收的参数
const props = defineProps<{ jobbankid: string | number }>(); 
const jobId = ref(props.jobbankid);

const datajob = ref([]) as any;
const formLabelWidth = '100px'
const jobbank_source_list = ref([]) as any;

const loading = ref(false);
const saving = ref(false);


// 新增主题选项
function addMainOption() {
  if (!datajob.value || !datajob.value.title) return;
  if (!Array.isArray(datajob.value.title.options)) {
    datajob.value.title.options = [];
  }
  
  // 获取当前已有选项数，自动分配A、B、C...
  const optionCount = datajob.value.title.options.length;
  let listNo = '';
  if (optionCount < 26) {
    listNo = String.fromCharCode(65 + optionCount); // 65是A的ASCII码
  } else {
    // 超过26个，用AA、AB等
    const first = String.fromCharCode(65 + Math.floor(optionCount / 26) - 1);
    const second = String.fromCharCode(65 + (optionCount % 26));
    listNo = first + second;
  }

  datajob.value.title.options.push({
    listNo: listNo,
    answer: false,
    option: ''
  });
}
// 删除主题选项
function removeMainOption(index: number) {
  if (!datajob.value || !datajob.value.title || !Array.isArray(datajob.value.title.options)) return;
  datajob.value.title.options.splice(index, 1);
  // 删除后同步答案
  handleAnswerChange(-1, null);
}
// 勾选答案时同步下方答案（主选择题）
function handleAnswerChange(index: number, row: any) {
  if (!datajob.value || !datajob.value.title || !Array.isArray(datajob.value.title.options)) return;
  const options = datajob.value.title.options;
  // 选中的选项下标
  const selectedIndexes = options
    .map((opt: any, idx: number) => (opt.answer ? idx : -1))
    .filter((idx: number) => idx !== -1);
  // 转为A,B,C...格式
  const answerStr = selectedIndexes.map((i : any)  => String.fromCharCode(65 + i)).join(',');
  datajob.value.title.answer = answerStr;
  datajob.value.answer = answerStr;
}




// 新增组合题子题
function addSubQuestion() {
  if (!datajob.value || !datajob.value.title) return;
  if (!Array.isArray(datajob.value.title.subQuestions)) {
    datajob.value.title.subQuestions = [];
  }
  datajob.value.title.subQuestions.push({
    title: '',
    options: [
      { listNo: 'A', answer: false, option: ''},
      { listNo: 'B', answer: false, option: ''}
    ], // 默认2个空选项，自动分配A、B
    answer: '',
    answer_parsing: ''
  });
}
// 删除组合题子题
function removeSubQuestion(subIndex: number) {
  if (!datajob.value || !datajob.value.title || !Array.isArray(datajob.value.title.subQuestions)) return;
  datajob.value.title.subQuestions.splice(subIndex, 1);
  syncAllSubQuestionAnswers()
}

// 新增组合题子题选项
function addSubOption(subIndex: number) {
  if (!datajob.value || !datajob.value.title || !Array.isArray(datajob.value.title.subQuestions)) return;
  const sub = datajob.value.title.subQuestions[subIndex];
  if (!sub) return;
  if (!Array.isArray(sub.options)) sub.options = [];

  // 获取当前已有选项数，自动分配A、B、C...
  const optionCount = sub.options.length;
  let listNo = '';
  if (optionCount < 26) {
    listNo = String.fromCharCode(65 + optionCount);
  } else {
    // 超过26个，用AA、AB等
    const first = String.fromCharCode(65 + Math.floor(optionCount / 26) - 1);
    const second = String.fromCharCode(65 + (optionCount % 26));
    listNo = first + second;
  }
  sub.options.push({
    listNo: listNo,
    answer: false,
    option: ''
  });
}

// 删除组合题子题选项
function removeSubOption(subIndex: number, optionIndex: number) {
  if (!datajob.value || !datajob.value.title || !Array.isArray(datajob.value.title.subQuestions)) return;
  const sub = datajob.value.title.subQuestions[subIndex];
  if (!sub || !Array.isArray(sub.options)) return;
  sub.options.splice(optionIndex, 1);
  // 删除后同步子题答案
  handleSubQuestionAnswerChange(subIndex, -1, null);
}


// 组合题子题的答案同步
function handleSubQuestionAnswerChange(subIndex: number, index: number, row: any) {
  if (!datajob.value || !datajob.value.title || !Array.isArray(datajob.value.title.subQuestions)) return;
  const sub = datajob.value.title.subQuestions[subIndex];
  if (!sub || !Array.isArray(sub.options)) return;
  const selectedIndexes = sub.options
    .map((opt: any, idx: number) => (opt.answer ? idx : -1))
    .filter((idx: number) => idx !== -1);
  const answerStr = selectedIndexes.map( (i : any) => String.fromCharCode(65 + i)).join(',');
  sub.answer = answerStr;
  datajob.value.title.subQuestions[subIndex].answer= answerStr;
  syncAllSubQuestionAnswers()
}


const getJobbankSource = () => {
    GetJobSource({}).then((res: any) => {
      if (res.code == 200) {
        jobbank_source_list.value = res.data
      } else {
        ElMessage.error(res.msg)
      }
    })
}

// 汇总组合题全部子题答案到外部答案框
function syncAllSubQuestionAnswers() {
  if (!datajob.value || !datajob.value.title || !Array.isArray(datajob.value.title.subQuestions)) return;
  // 只统计有效子题
  const result = datajob.value.title.subQuestions
    .filter((sub: any) => sub && Array.isArray(sub.options)) // 过滤掉无效子题
    .map((sub: any, idx: number) => {
      const selectedIndexes = sub.options
        .map((opt: any, i: number) => (opt.answer ? i : -1))
        .filter((i: number) => i !== -1);
      const answerStr = selectedIndexes.map((i: number) => String.fromCharCode(65 + i)).join(',');
      return { sn: idx + 1, answer: answerStr };
    });
  datajob.value.answer = JSON.stringify(result, null, 0);
}

// 答案同步
const syncOptionAnswers = () => {
  // 主题选项同步
  if (datajob.value && datajob.value.title && Array.isArray(datajob.value.title.options)) {
    const answerStr = datajob.value.title.answer;
    if (answerStr) {
      const answerArr = answerStr.split(',').map((s: string) => s.trim()).filter(Boolean);
      datajob.value.title.options.forEach((opt: any, idx: number) => {
        const letter = String.fromCharCode(65 + idx);
        opt.answer = answerArr.includes(letter);
      });
    }
  }
  // 组合题子题选项同步
  if (datajob.value && datajob.value.title && Array.isArray(datajob.value.title.subQuestions)) {
    datajob.value.title.subQuestions.forEach((sub: any) => {
      if (Array.isArray(sub.options) && typeof sub.answer === 'string') {
        const subAnswerArr = sub.answer.split(',').map((s: string) => s.trim()).filter(Boolean);
        sub.options.forEach((opt: any, idx: number) => {
          const letter = String.fromCharCode(65 + idx);
          opt.answer = subAnswerArr.includes(letter);
        });
      }
    });
  }
}

const getJobQuestions = async () => {
  // 根据答案自动勾选选项
  loading.value = true;
  try {
    const pars = {
      jobbankid: jobId.value
    }
    GetJobIdQuestions(pars).then((msg: any) => {
      datajob.value = msg.data
      // 渲染前处理公式斜杠
      if (datajob.value?.title?.title) {
        datajob.value.title.title = toolsUtils.format2Md(datajob.value.title.title);
      }
      syncOptionAnswers(); // 数据加载后同步勾选
      loading.value = false
    }).catch((err: any) => {
      console.log(err)
      loading.value  = false
    })
  } catch (e) {
    ElMessage.error('题库获取失败');
  } finally {
    loading.value = false;
  }
};

 
const saveQuestions = async () => {
  saving.value = true;
  try {
    ElMessageBox.confirm(
      '再次确认是否保存题库编号【' + jobId.value + '】的数据, 是否继续保存?',
      '提示',
      {
        confirmButtonText: '确认保存',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    .then(() => { 
         const pars = {
          data: datajob.value
        }
        SaveJobBankIdQuestions(pars).then((msg: any) => {
          if (msg.data > 0) {
              ElMessage.success('保存成功!');
              getJobQuestions();
          } else {
            ElMessage({
              type: 'info',
              message: '保存失败!' + msg.msg
            })
          } 
          saving.value = false 
        }).catch((err: any) => {
          console.log(err)
          saving.value  = false
        })
  
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消保存!',
      })
    }) 

  } catch (e) {
    ElMessage.error('保存失败');
  } finally {
    saving.value = false;
  }
};

onMounted(() => {
  getJobbankSource()//试题来源
  getJobQuestions();//试题详细
});

watch(() => props.jobbankid, (newVal) => {
  jobId.value = newVal;
  getJobQuestions();
});
</script>

<style scoped>
.el-card {
  max-width: 1200px;
  margin: 0 auto;
}
.options_div{
  margin: 10px !important;
  max-width: 1000px;
  height:auto;
}

.title-wrapper {
  display: flex;
  align-items: center;
  gap: 4px;
}
.header-icon{
  color: red;
  
}

</style>
