<template>
  <div class="login" :style="{ backgroundImage: 'url(' + logoBgUrl + ')' }">
    <div style="width: 100%;height: 70px;position: absolute;top: 0px;backdrop-filter: blur(15px);">
      <div style="width: 1300px;margin: 0 auto;">
        <img src="https://xczx.swufe.edu.cn/images/logo.png" style="width: 210px;">
      </div>
    </div>
    <div class="login-form" style="backdrop-filter: blur(15px);">

      <div class="form-box">
        <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules">
          <div class="right-box">
            <h3 class="title" style="letter-spacing:0.1em">
              <span>
                <font style="color:#3185DB">自考考试课程资源</font>
              </span>
              <p style="font-size: 39px;">管理系统</p>
            </h3>
            <p class="supplement" style="height:0px;"></p>
            <el-form-item prop="username" class="form-box-item">
              <el-input v-model="loginForm.username" size="large" class="form-box-input" type="text"
                 placeholder="账号">
                <template #prefix>
                  <el-icon>
                    <UserFilled />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input v-model="loginForm.password" size="large" class="form-box-input" type="password"
                 placeholder="密码" @keyup.enter="handleLogin">
                <template #prefix>
                  <el-icon>
                    <MoreFilled />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-checkbox v-model="loginForm.rememberMe" style="margin: 0px 0px 25px 0px">记住密码
            </el-checkbox>
            <el-form-item style="width: 100%">
              <el-button :loading="loading" size="large" type="primary" style="width: 100%; height: 42px"
                @click.prevent="handleLogin">
                <span v-if="!loading">登 录</span>
                <span v-else>登 录 中...</span>
              </el-button>
            </el-form-item>
          </div>
        </el-form>
        <div style="display: flex;padding: 0px 45px;gap: 20px;">
          <div class="icon_item">
            <svg viewBox="0 0 1024 1024" width="40" height="40">
              <path
                d="M0 0m76.8 0l870.4 0q76.8 0 76.8 76.8l0 870.4q0 76.8-76.8 76.8l-870.4 0q-76.8 0-76.8-76.8l0-870.4q0-76.8 76.8-76.8Z"
                fill="#3185db" p-id="10771"></path>
              <path
                d="M742.4 440.7808V751.104c0 13.7216-12.2368 24.832-27.3152 24.832H308.9152c-15.104 0-27.3152-11.1104-27.3152-24.832V440.832"
                fill="#FFFFFF" p-id="10772"></path>
              <path d="M197.8112 510.9248L510.4128 198.3488l312.6016 312.576" fill="#FFFFFF" p-id="10773"></path>
              <path
                d="M189.44 496.64m23.04 0l583.68 0q23.04 0 23.04 23.04l0 0q0 23.04-23.04 23.04l-583.68 0q-23.04 0-23.04-23.04l0 0q0-23.04 23.04-23.04Z"
                fill="#FFFFFF" p-id="10774"></path>
              <path
                d="M407.296 713.088a20.9408 20.9408 0 0 1 0-41.9072h216.192a20.9408 20.9408 0 0 1 0 41.9072H407.296z m216.192-125.696a20.9408 20.9408 0 0 1 0 41.9072H407.296a20.9408 20.9408 0 0 1 0-41.9072h216.2176z"
                fill="#3185db" p-id="10775"></path>
            </svg>
            <span style="font-size: 12px;">西财在线</span>
          </div>
          <div class="icon_item">
            <div style="width: 41px;height: 41px;background-color: #3185db;border-radius: 3px;display: flex;justify-content: center;align-items: center;">
              <svg t="1744774837147" class="icon" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" p-id="16893" width="26" height="26">
                <path
                  d="M111.2 711.2h799.2V152H111.2v559.2zM32 152c0-43.2 36-79.2 79.2-79.2h799.2c45.6 0 81.6 36 81.6 79.2v559.2c0 43.2-36 79.2-79.2 79.2H111.2c-43.2 0-79.2-36-79.2-79.2V152z"
                  p-id="16894" fill="#ffffff"></path>
                <path d="M471.2 711.2h79.2v199.2h-79.2V711.2z" p-id="16895" fill="#ffffff"></path>
                <path
                  d="M312.8 872h400.8v79.2H312.8V872zM778.4 348c-4-5.6-9.6-10.4-16.8-14.4L542.4 220.8c-14.4-7.2-32-7.2-46.4-0.8L265.6 333.6c-12 5.6-20.8 16.8-24 28.8-3.2 12-0.8 24 6.4 34.4 4.8 6.4 10.4 12 18.4 15.2l52 25.6v125.6c0 7.2 2.4 14.4 5.6 20.8 12 20 53.6 67.2 188.8 67.2 136.8 0 178.4-47.2 189.6-68 3.2-6.4 4.8-12.8 4.8-20V440l55.2-28c12-6.4 20.8-16.8 23.2-28.8 2.4-12.8 0-24.8-7.2-35.2z m-109.6 24.8l-150.4 77.6-157.6-77.6 157.6-77.6 150.4 77.6z m-270.4 104l97.6 48c14.4 7.2 32 7.2 46.4-0.8l83.2-43.2v69.6c-9.6 7.2-39.2 23.2-113.6 23.2-73.6 0-104-16-113.6-24v-72.8z"
                  p-id="16896" fill="#ffffff"></path>
              </svg>
            </div>

            <span style="font-size: 12px;">学生平台</span>
          </div>
        </div>
      </div>
    </div>
    <!--  底部  -->
    <div class="el-login-footer">
      <!-- <span>合作协议管理系统 V{{ version }}</span> -->
      <span>自考学习平台 </span>
    </div>

  </div>
  <Vcode :show="isShow" @success="success" @close="close" @fail="fail" :imgs="images"></Vcode>

</template>

<script lang="ts">

import { ElMessage } from 'element-plus'
import { defineComponent, onMounted, reactive, ref, toRefs } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { useRouter, useRoute } from 'vue-router'
import cookies from "js-cookie";
import Vcode from "vue3-puzzle-vcode"
import Sidentify from "./Sidentify.vue"

export default defineComponent({
  components: {
    Sidentify,
    Vcode
  },
  setup() {
    const loginLeftImgUrl = 'https://apps.swufe-online.com/file/images/bg/bg_home.jpg'//'./images/loginLeft.png'
    //const logoBgUrl = './images/login-background.jpg'
    var imgRad = Math.floor(Math.random() * (7 - 1 + 1)) + 1;
    const logoBgUrl = ref('./images/' + imgRad + '.png')
    setInterval(() => {
      logoBgUrl.value = './images/' + imgRad + '.png'
      var nowstamp = Math.floor(Date.now() / 1000);
      if (nowstamp % 60 == 0) {
        imgRad++;
        if (imgRad > 7) {
          imgRad = 1;
        }
      }
    }, 1000);

    const labelPosition = "top"

    const userStore = useUserStore()
    const router = useRouter()
    const route = useRoute()
    // const version = require('./package.json').version

    const isShow = ref(false);
    const images = [
      './images/0.png',
      './images/1.png',
      './images/2.png',
      './images/3.png',
      './images/4.png',
      './images/6.png',
    ]
    const success = (msg: any) => {
      isShow.value = false
      console.log("验证通过" + msg)
      loginFunc()
    }
    //用户点击遮罩层，关闭模态框
    const close = () => {
      isShow.value = false
    }
    //用户验证失败
    const fail = () => {
      console.log("验证失败")
    }
    const loginRules = reactive({
      username: [
        { required: true, trigger: 'blur', message: '用户名不能为空' },
      ],
      password: [{ required: true, trigger: 'blur', message: '密码不能为空' }],
    })

    // 点击登录
    const loading = ref(false)
    const loginForm = reactive({
      username: '',
      password: '',
      rememberMe: false,
    })
    const loginFormRef = ref()
    const handleLogin = () => {
      cookies.remove('selected_zd_bm')
      loginFormRef.value.validate((valid: any) => {
        if (valid) {
          isShow.value = true
        }
      })
    }
    const loginFunc = () => {
      loading.value = true
      userStore.login(loginForm).then(() => {
        // this.$router.push({ path: redirect || '/', query: this.otherQuery })
        if (route.query.redirect) {
          const path = route.query.redirect
          router.push({ path: path as string })
        } else {
          router.push('/')
        }
      }).catch((error) => {
        ElMessage({
          message: error,
          type: 'error',
          duration: 5 * 1000
        })
      })
      .finally(() => (loading.value = false))
      
    }


    return {
      loginLeftImgUrl,
      logoBgUrl,
      loginRules,
      loginForm,
      loading,
      loginFormRef,
      labelPosition,
      isShow,
      success,
      close,
      fail,
      images,
      handleLogin
    }
  }
})

</script>
<style></style>
<style lang="scss">
.icon_item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 5px;
  width: auto;

}

.form-box-input {
  .el-input__inner {
    padding-left: 10px !important;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
  }

  .el-input__wrapper {
    padding: 21px 2px 21px 15px !important;

  }

}

.login .el-form-item {

  margin-bottom: 28px !important;

  .el-form-item__error {
    margin-top: 10px !important;
  }

}
</style>

<style rel="stylesheet/scss" lang="scss" scoped>
.login {
  display: flex;
  justify-content: center;
  position: relative;
  align-items: center;
  height: 100%;
  // background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
}

.title {
  color: #707070;
  margin: 40px 0 0;
  overflow: hidden;
  white-space: nowrap;

  span {
    font-size: 40px;
    margin-right: 10px;
  }

  img {
    height: 25px;
    width: auto;
  }
}

.supplement {
  font-size: 28px;
  margin-top: 2px;
  margin-bottom: 40px;
  height: 50px;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.login-form {
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.7);
  width: 500px;
  height: 640px;
  box-shadow: 0px 0px 12px 5px rgba(188, 188, 188, 0.2);
  display: flex;

  input {
    background: rgba(255, 255, 255, 0) !important;
  }

  .left-img {
    width: 448px;
    border-right: 1px solid #f0f0f0;
    box-sizing: border-box;
    padding: 0px 0px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .right-box {
    padding: 50px;
  }

  .form-box {
    flex: 1;
  }

  .el-input {
    height: 38px;

    input {
      height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #a6a6a6;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
  font-weight: 400;
}

.login-code-img {
  height: 38px;
}

.login_left_img {
  object-fit: cover;

}
</style>