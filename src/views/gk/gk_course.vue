<template>
  <div class="gk_course" style="padding: 5px;">
    <el-button type="primary" @click="initData">刷新</el-button>
    <el-button type="success" @click="openDialog()">
      <svg viewBox="0 0 1024 1024" width="18" height="18">
        <path
          d="M687.496192 742.724608l0 152.704-101.801984 0L585.694208 742.724608 432.991232 742.724608l0-101.801984 152.704 0L585.695232 488.219648l101.801984 0 0 152.702976 152.701952 0 0 101.801984L687.496192 742.724608zM687.496192 182.813696 229.387264 182.813696l0 559.910912 152.702976 0L382.09024 844.526592l-254.50496 0L127.58528 81.011712l661.71392 0 0 356.306944L687.496192 437.318656 687.496192 182.813696z"
          p-id="7133" fill="#ffffff"></path>
      </svg>
      添加
    </el-button>
    <el-table :data="tableData" :Height="tableHeight" stripe style="width: 100%;margin-top: 5px;"
      v-loading="tableloading">
      <el-table-column type="index" label="#" align="center" width="60" />
      <el-table-column prop="kc_bm" label="课程编码" width="180" align="center" />
      <el-table-column prop="kc_mc" label="课程名称" width="180" align="center" />
      <el-table-column prop="chapter_num" width="100" label="状态" align="center">    
        <template #default="scope">
          <el-tag v-if="scope.row.status === 1" type="success">启用</el-tag>
          <el-tag v-else type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="chapter_num" label="" align="center">
        <template #default="scope">
          <el-button type="primary" size="small" @click="edit(scope.row)">编辑</el-button>
          <el-button type="danger" size="small" @click="deleteData(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog v-model="dialogVisible" title="编辑数据" width="500">
      <div style="width: 300px;margin: 0 auto;">
        <el-form :inline="true" :model="formData" :label-position="labelPosition" label-width="80px">
          <el-form-item label="选择课程">
            <el-select v-model="formData.course_base_id" class="m-2" placeholder="请选择课程" style="width: 200px;" @change="handleChangeCourse">
              <el-option v-for="item in courseBase" :key="item.id" :label="item.kc_mc" :value="item.id" />
            </el-select>
          </el-form-item><br/>
          <el-form-item label="课程编码">
            <el-input v-model="formData.kc_bm" disabled placeholder="请选择" style="width: 200px;"/>
          </el-form-item>
          <el-form-item label="课程名称">
            <el-input v-model="formData.kc_mc" disabled placeholder="请选择" style="width: 200px;"/>
          </el-form-item>
          <el-form-item label="状态">
            <el-radio-group v-model="formData.status">
              <el-radio label="1" border>启用</el-radio>
              <el-radio label="0" border>禁用</el-radio>
            </el-radio-group>
          </el-form-item>

        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveData">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang='ts'>
import { getGkCourse, getCourseBase,gkSaveCourse,deleteGkCourse } from "@/api/gk";
import { nextTick, onMounted, ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
const tableData = ref([])
const tableloading = ref(false);
const labelPosition = ref('right')
const dialogVisible = ref(false);
const courseBase: any = ref([])
const tableHeight = window.innerHeight - 200;
const formData = ref({
  id:0,
  course_base_id: "",
  kc_bm: "",
  kc_mc: "",
  status: "1"
})
onMounted(() => {
  initData();
});

const initData = () => {
  tableloading.value = true;
  getGkCourse({}).then((res: any) => {
    if (res.code === 200) {
      tableData.value = res.data
    } else {
      ElMessage({
        message: res.msg,
        type: "error",
      });
    }
  }).catch((err: any) => {
    ElMessage({
      message: err.message,
      type: "error",
    });
  }).finally(() => {
    tableloading.value = false;
  });
}
const getCourseBaseData = () => {
  getCourseBase({}).then((res: any) => {
    if (res.code === 200) {
      courseBase.value = res.data
    } else {
      ElMessage({
        message: res.msg,
        type: "error",
      });
    }
  }).catch((err: any) => {
    ElMessage({
      message: err.message,
      type: "error",
    });
  });
}
const openDialog = () => {
  dialogVisible.value = true;
  getCourseBaseData();
  nextTick(() => {
    formData.value = {
      id:0,
      course_base_id: "",
      kc_bm: "",
      kc_mc: "",
      status: "1"
    }
  });
}
const handleChangeCourse=()=>{
  console.log(formData.value.course_base_id)
  var kc=courseBase.value.find((item:any)=>item.id==formData.value.course_base_id)
  formData.value.kc_bm=kc.kc_bm
  formData.value.kc_mc=kc.kc_mc
}
const saveData = () => {
  gkSaveCourse(formData.value).then((res: any) => {
    if (res.code === 200) {
      ElMessage({
        message: res.msg,
        type: "success",
      });
      dialogVisible.value = false;
      initData();
    } else {
      ElMessage({
        message: res.msg,
        type: "error",
      });
    }
  }).catch((err: any) => {
    ElMessage({
      message: err.message,
      type: "error",
    });
  });
}
const edit=(item:any)=>{
  dialogVisible.value = true;
  getCourseBaseData();
  item.status=item.status.toString()
  formData.value=item
}
const deleteData=(item:any)=>{
  ElMessageBox.confirm('此操作将永久删除该课程, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteGkCourse({id:item.id}).then((res:any)=>{
      if(res.code==200){
        ElMessage({
          message: res.msg,
          type: "success",
        });
        initData();
      }else{
        ElMessage({
          message: res.msg,
          type: "error",
        });
      }
    }).catch((err:any)=>{
      ElMessage({
        message: err.message,
        type: "error",
      });
    })
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '已取消删除'
    });
  });
}
</script>

<style></style>