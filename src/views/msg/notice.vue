<template>
  <div class="page">
    <div class="header">
      <el-button plain
                 type="success"
                 size="small"
                 @click="reloadPageData"
                 icon="refresh">刷新
      </el-button>
      <el-input placeholder="关键字查询"
                v-model="init_data.key_search"
                size="small"
                clearable style="width: 230px;" @change="reloadPageData">
        <template #prepend>
          查询
        </template>
      </el-input>
      <el-tag type="info">筛选</el-tag>
      <el-checkbox-group v-model="init_data.is_pub" size="small" @change="reloadPageData">
        <el-checkbox v-for="item in init_data.pub_ls" :key="item.v" :label="item.t" :value="item.v" border>
          {{ item.t }}
        </el-checkbox>
      </el-checkbox-group>
      <el-tag type="info">操作</el-tag>
      <el-button type="success" size="small">添加</el-button>
    </div>
    <div class="body">
      <el-table
          :data="init_data.page_data"
          width="100%"
          height="calc(100vh - 165px)"
          v-loading="init_data.pageLoading"
          ref="multipleTable"
          highlight-current-row
          size="small"
          border
          :element-loading-text="init_data.pageLoadingText"
          :element-loading-spinner="init_data.pageLoadingSvg"
          element-loading-svg-view-box="-10, -10, 50, 50"
          element-loading-background="rgba(122, 122, 122, 0.8)"
      >
        <el-table-column label="序号"
                         align="center"
                         width="45"
                         header-align="center"
                         prop="sn">
        </el-table-column>
        <el-table-column label="标题"
                         sortable
                         align="left"
                         min-width="150"
                         header-align="center"
                         show-overflow-tooltip
                         prop="title">
        </el-table-column>
        <el-table-column label="操作"
                         sortable
                         align="center"
                         width="80"
                         header-align="center"
                         prop="ctrl">
          <template #default="scope">
            <el-button type="primary" size="small" plain @click="edit(scope.row.id)">编辑</el-button>
          </template>
        </el-table-column>
        <el-table-column label="年级"
                         align="center"
                         width="80"
                         header-align="center"
                         prop="pc_mc">
        </el-table-column>
        <el-table-column label="层次"
                         align="center"
                         width="80"
                         header-align="center"
                         prop="xlcc_mc">
        </el-table-column>
        <el-table-column label="专业"
                         align="center"
                         width="80"
                         header-align="center"
                         prop="zy_mc">
        </el-table-column>
        <el-table-column label="教学点"
                         align="center"
                         width="80"
                         header-align="center"
                         prop="zd_mc">
        </el-table-column>
        <el-table-column label="课程"
                         align="center"
                         width="80"
                         header-align="center"
                         prop="kc_mc">
        </el-table-column>
        <el-table-column label="发布状态"
                         sortable
                         align="center"
                         width="120"
                         header-align="center"
                         prop="is_pub">
          <template #default="scope">
            <el-switch size="small"
                       v-model="scope.row.is_pub"
                       inline-prompt
                       style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                       active-text="已发布"
                       inactive-text="未发布"
                       width="80px"
            />
          </template>
        </el-table-column>
        <el-table-column label="发布时间"
                         align="center"
                         sortable
                         width="120"
                         header-align="center"
                         prop="pub_date">
        </el-table-column>
        <el-table-column label="发布人员"
                         align="center"
                         width="80"
                         header-align="center"
                         prop="pub_user">
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <el-button plain
                 type="success"
                 @click="reloadPageData"
                 icon="refresh"
                 size="small">刷新
      </el-button>
      <el-pagination
          small
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="init_data.current_page"
          :page-sizes="init_data.page_sizes"
          :page-size="init_data.page_size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="init_data.page_total">
      </el-pagination>
    </div>
  </div>

  <el-dialog v-model="init_data.is_show_dialog_edit" fullscreen :append-to-body="true"
             :close-on-click-modal="false" :close-on-press-escape="false">
    <template #header>
      <div style="display: flex; align-items: center;justify-content: space-between;gap: 5px;">
        <div style="padding-left: 20px;" class="primary">编辑公告内容（支持markdown格式）</div>
        <div>
          <el-button type="danger" size="small">重置发布对象</el-button>
          <el-button type="success" size="small">仅保存</el-button>
          <el-button type="warning" size="small">保存并发布</el-button>
          <el-button type="info" size="small" plain @click="init_data.is_show_dialog_edit = false">关闭</el-button>
        </div>
      </div>
    </template>
    <div
        style="display: flex; align-items: center;gap: 10px;flex-wrap: wrap;flex-direction: row;justify-content: flex-start;">
      <div
          style="display: flex; align-items: center;justify-content: flex-start;gap: 15px;width: 100%;flex-wrap: wrap;">
        <div style="display: flex; align-items: center;justify-content: flex-start;gap: 5px;flex-wrap: nowrap;">
          <el-tag type="info" size="large">年级</el-tag>
          <el-select v-model="init_data.edit_data.pc_bm" placeholder="请选择" size="default" style="width: 100px;">
            <el-option
                v-for="item in init_data.edit_data.pc_ls"
                :key="item.v"
                :label="item.t"
                :value="item.v"
            >
              <div style="display: flex; align-items: center;justify-content: flex-start;gap: 5px;flex-wrap: nowrap;">
                <el-text size="default" type="primary"> {{ item.t }}</el-text>
              </div>
            </el-option>
          </el-select>
        </div>
        <div style="display: flex; align-items: center;justify-content: flex-start;gap: 5px;flex-wrap: nowrap;">
          <el-tag type="info" size="large">层次</el-tag>
          <el-select v-model="init_data.edit_data.xlcc_bm" placeholder="请选择" size="default" style="width: 100px;">
            <el-option
                v-for="item in init_data.edit_data.xlcc_ls"
                :key="item.v"
                :label="item.t"
                :value="item.v"
            >
              <div style="display: flex; align-items: center;justify-content: flex-start;gap: 5px;flex-wrap: nowrap;">
                <el-text size="default" type="primary"> {{ item.t }}</el-text>
              </div>
            </el-option>
          </el-select>
        </div>
        <div style="display: flex; align-items: center;justify-content: flex-start;gap: 5px;flex-wrap: nowrap;">
          <el-tag type="info" size="large">专业</el-tag>
          <el-select v-model="init_data.edit_data.zy_bm" placeholder="请选择" size="default" style="width: 200px;"
                     filterable :filter-method="filterZy">
            <el-option
                v-for="item in init_data.edit_data.zy_ls_filter"
                :key="item.v"
                :label="item.t"
                :value="item.v"
            >
              <div style="display: flex; align-items: center;justify-content: flex-start;gap: 5px;flex-wrap: nowrap;">
                <el-text size="default" type="info"> {{ item.v }}</el-text>
                <el-text size="default" type="primary"> {{ item.t }}</el-text>
              </div>
            </el-option>
          </el-select>
        </div>
        <div style="display: flex; align-items: center;justify-content: flex-start;gap: 5px;flex-wrap: nowrap;">
          <el-tag type="info" size="large">教学点</el-tag>
          <el-select v-model="init_data.edit_data.zd_bm" placeholder="请选择" size="default" style="width: 200px;"
                     filterable>
            <el-option
                v-for="item in init_data.edit_data.zd_ls"
                :key="item.v"
                :label="item.t"
                :value="item.v"
            >
              <div style="display: flex; align-items: center;justify-content: flex-start;gap: 5px;flex-wrap: nowrap;">
                <el-text size="default" type="info"> {{ item.v }}</el-text>
                <el-text size="default" type="primary"> {{ item.t }}</el-text>
              </div>
            </el-option>
          </el-select>
        </div>
        <div style="display: flex; align-items: center;justify-content: flex-start;gap: 5px;flex-wrap: nowrap;">
          <el-tag type="info" size="large">课程名称</el-tag>
          <el-select v-model="init_data.edit_data.kc_bm" placeholder="请选择" size="default" style="width: 200px;"
                     filterable>
            <el-option
                v-for="item in init_data.edit_data.kc_ls"
                :key="item.v"
                :label="item.t"
                :value="item.v"
            >
              <div style="display: flex; align-items: center;justify-content: flex-start;gap: 5px;flex-wrap: nowrap;">
                <el-text size="default" type="info"> {{ item.v }}</el-text>
                <el-text size="default" type="primary"> {{ item.t }}</el-text>
              </div>
            </el-option>
          </el-select>
        </div>
      </div>
      <div style="display: flex; align-items: center;justify-content: flex-start;gap: 5px;width: 100%;">
        <el-tag size="large" type="info">标题</el-tag>
        <el-input placeholder="请输入标题"
                  v-model="init_data.edit_data.title"
                  clearable>
        </el-input>
      </div>

      <mavon-editor :boxShadow="false" :xssOptions="{}" :html="true" v-model="init_data.edit_data.content"
                    style="height: calc(100vh - 200px);width: 100%;"/>
    </div>

  </el-dialog>
</template>

<script setup lang='ts'>
import {nextTick, onMounted, ref} from "vue";
import {msgShow} from "@/utils/message";
import {MsgNoticePageData} from "@/api/msg";
import {toolsUtils} from "@/utils/tools";
import {I_notice_info} from "@/utils/types";

const init_data = ref({
  pageLoading: false,
  pageLoadingText: "获取数据中，请稍后...",
  pageLoadingSvg: `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `,
  key_search: '',
  is_pub: ['1', '0'],
  pub_ls: [{t: '已发布', v: '1'}, {t: '未发布', v: '0'}],
  page_data: [] as I_notice_info[],
  current_page: 1,
  page_total: 0,
  page_index: 1,
  page_size: Math.floor((window.innerHeight - 160) / 32),
  page_sizes: [
    Math.floor((window.innerHeight - 160) / 32),
    100,
    200,
    500
  ],
  is_show_dialog_edit: false,
  edit_data: {
    id: 0,
    title: '',
    content: '',
    is_pub: false,
    pub_user: '',
    pub_date: '',
    zd_bm: '',
    pc_bm: '',
    kc_bm: '',
    xlcc_bm: '',
    zy_bm: '',
    pc_ls: [{t: '2024秋', v: '202409'}, {t: '2023秋', v: '202403'}],
    xlcc_ls: [{t: '专升本', v: '4'}, {t: '专科', v: '2'}],
    zy_ls: [{t: '会计学', v: 'W120203K'}, {t: '大数据与会计', v: 'W530302'}],
    zd_ls: [{t: '西南财经大学（光华校区）', v: '098'}, {t: '成都市武侯区燕园文化培训学校', v: '079'}],
    kc_ls: [{t: '财务管理学', v: '00067'}, {t: '商业银行业务与经营', v: '00072'}],
    zy_ls_filter: [{t: '会计学', v: 'W120203K'}, {t: '大数据与会计', v: 'W530302'}],
    zd_ls_filter: [{t: '西南财经大学（光华校区）', v: '098'}, {t: '成都市武侯区燕园文化培训学校', v: '079'}],
    kc_ls_filter: [{t: '财务管理学', v: '00067'}, {t: '商业银行业务与经营', v: '00072'}],
  }
})


onMounted(async () => {
  await nextTick(async () => {
    await reloadPageData()
  });
});

const edit = (id: number) => {
  init_data.value.is_show_dialog_edit = true
  let t_item = init_data.value.page_data.find((item: I_notice_info) => item.id === id)
  if (t_item) {
    init_data.value.edit_data.id = id
    init_data.value.edit_data.title = t_item.title
    init_data.value.edit_data.content = t_item.content
    init_data.value.edit_data.is_pub = t_item.is_pub
    init_data.value.edit_data.pub_user = t_item.pub_user
    init_data.value.edit_data.pub_date = t_item.pub_date
    init_data.value.edit_data.is_pub = true
  }
}

const filterZy = (query: any) => {
  console.log('query', query)
  if (query) {
    init_data.value.edit_data.zy_ls_filter = init_data.value.edit_data.zy_ls.filter(item => {
      return item.v.toLowerCase().includes(query.toLowerCase()) || item.t.toLowerCase().includes(query.toLowerCase());
    });
  } else {
    init_data.value.edit_data.zy_ls_filter = init_data.value.edit_data.zy_ls
  }
}
const handleSizeChange = (val: number) => {
  init_data.value.page_size = val
  reloadPageData()
}
const handleCurrentChange = (val: number) => {
  init_data.value.page_index = val
  init_data.value.current_page = val
  reloadPageData()
}
const reloadPageData = async () => {
  await getNoticePageData()
}
const getNoticePageData = async () => {
  await MsgNoticePageData(toolsUtils.trimSpace(init_data.value.key_search), init_data.value.is_pub.toString(), init_data.value.page_index, init_data.value.page_size).then(res => {
    if (res.code === 200) {
      init_data.value.page_data = res.data.page_data
      init_data.value.page_total = res.data.page_total
    } else {
      msgShow(res.msg, 'warning')
    }
  }).catch(err => {
    msgShow(err.toString(), 'error')
  })
}


</script>

<style lang="scss" scoped>
:deep .el-table--enable-row-hover .el-table__body tr:hover > td {
  background: transparent;
}

:deep .el-table__footer {
  font-weight: bold;
  height: 40px !important;

}

:deep .el-table__footer-wrapper tbody td.el-table__cell, .el-table__header-wrapper tbody td.el-table__cell {
  background-color: #bdd6f6;
}

:deep .el-table-fixed-column--left {
  //background:none!important;
}

</style>
