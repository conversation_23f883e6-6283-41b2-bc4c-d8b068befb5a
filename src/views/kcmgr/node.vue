<template>
  <div class="kcnode-list page">
    <div class="header">
      <el-button type="success" plain size="default" icon="refresh" round @click="getData"></el-button>
      <el-button type="primary" plain @click="openRowExpansion()" size="default" round icon="View" aria-label="">{{openButtonText}}</el-button>
    </div>
    <div class="body">
      <el-table
        border
        :height="tableHeight"
        :data="tableData.tableDataData"
        :row-class-name="tableRowClassName"
        row-key="id"
        class="kcNodeTable"
        v-loading="tableData.loading"
        :default-expand-all="false"
        highlight-current-row
        ref="tablexTree"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      >
        <el-table-column type="index" label="#" align="center" width="60" />
        <el-table-column label="课程代码" align="center" min-width="130">
          <template #default="scope">
            <span v-if="scope.row.level_type === 'course'">{{ scope.row.course_code }}</span>
            <span v-else-if="scope.row.level_type === 'chapter'">{{ scope.row.chapter_code }}</span>
            <span v-else-if="scope.row.level_type === 'node'">{{ scope.row.node_code }}</span>
          </template>
        </el-table-column>
        <el-table-column label="名称" align="center" min-width="200">
          <template #default="scope">
            <span v-if="scope.row.level_type === 'course'">{{ scope.row.course_name }}</span>
            <span v-else-if="scope.row.level_type === 'chapter'">{{ scope.row.chapter_name }}</span>
            <span v-else-if="scope.row.level_type === 'node'">{{ scope.row.node_name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="node_status" label="状态" align="center" min-width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.node_status"
              active-text="正常"
              inactive-text="禁用"
              :active-value="true"
              :inactive-value="false"
              @change="updateNodeStatus(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="create_date" label="添加时间" align="center" min-width="150">
          <template #default="scope">
            {{ scope.row.create_date }}
          </template>
        </el-table-column>
        <el-table-column prop="create_user" label="添加人" align="center" min-width="100" />
        <el-table-column label="是否关联知识点" align="center" min-width="120">
          <template #default="scope">
              <span v-if="scope.row.is_knowledge_point_linked" style="color:blue">是</span>
              <span v-else style="color:#909399">否</span>
          </template>
        </el-table-column>
        <el-table-column label="关联知识点名称" align="center" min-width="150" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.linked_knowledge_point_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="题量" align="center" min-width="80">
          <template #default="scope">
            {{ scope.row.question_count || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="图文" align="center" min-width="80">
          <template #default="scope">
            <span v-if="scope.row.text_image">有</span>
          </template>
        </el-table-column>
        <el-table-column label="视频" align="center" min-width="80">
          <template #default="scope">
            <span v-if="scope.row.video">有</span>
          </template>
        </el-table-column>
        <el-table-column label="案例" align="center" min-width="80">
          <template #default="scope">
            <span v-if="scope.row.case_study">有</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="280">
          <template #default="scope">
            <el-button v-if="scope.row.level_type !== 'course'" link type="primary" size="small" @click="openEditNode(scope.row)"><el-icon><Edit /></el-icon>编辑</el-button>
            <el-button v-if="scope.row.level_type !== 'node'" link type="primary" size="small" @click="openAddChildNode(scope.row)"><el-icon><CirclePlus /></el-icon>添加子节点</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 节点编辑对话框 -->
    <el-dialog v-model="nodeDialogVisible"
      :title="dialogTitle"
      custom-class="nodeDialogClass"
      draggable
      :close-on-press-escape="false"
      :close-on-click-modal="false">
      <div style="width:100%;text-align:center;">
        <el-form :model="nodeForm" :rules="rules" ref="nodeFormRef" :inline="true" :label-width="formLabelWidth">
          <el-form-item label="课程代码" prop="course_code">
            <el-input v-model="nodeForm.course_code" :disabled="true" style="width:235px;" />
          </el-form-item>
          <el-form-item label="课程名称" prop="course_name">
            <el-input v-model="nodeForm.course_name" :disabled="true" style="width:235px;" />
          </el-form-item>
          
          <!-- 章节相关字段 -->
          <template v-if="nodeForm.level_type === 'chapter' || nodeForm.level_type === 'node'">
            <el-form-item label="章节名称" prop="chapter_name">
              <el-input v-model="nodeForm.chapter_name" style="width:235px;" />
            </el-form-item>
          </template>
          
          <!-- 节点相关字段 -->
          <template v-if="nodeForm.level_type === 'chapter' || nodeForm.level_type === 'node'">
            <el-form-item label="节点名称" prop="node_name">
              <el-input v-model="nodeForm.node_name" style="width:235px;" />
            </el-form-item>
          </template>
          
          <el-form-item label="节点状态" prop="node_status">
            <el-radio-group v-model="nodeForm.node_status" style="width:235px;">
              <el-radio :label="true" @change="checkStatus()">正常</el-radio>
              <el-radio :label="false" @change="checkStatus()">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <!-- 知识点相关字段，仅在节点级别显示 -->
          <template v-if="nodeForm.level_type === 'chapter' || nodeForm.level_type === 'node'">
            <el-form-item label="关联知识点" prop="knowledgePoints">
              <el-select
                v-model="nodeForm.knowledgePoints"
                multiple
                filterable
                allow-create
                default-first-option
                style="width:235px;"
                placeholder="请选择关联知识点">
                <el-option
                  v-for="item in knowledgePointOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="题量" prop="question_count">
              <el-input-number v-model="nodeForm.question_count" :min="0" style="width:235px;" />
            </el-form-item>
            <el-form-item label="图文" prop="text_image">
              <el-input v-model="nodeForm.text_image" style="width:235px;" />
            </el-form-item>
            <el-form-item label="视频" prop="video">
              <el-input v-model="nodeForm.video" style="width:235px;" />
            </el-form-item>
            <el-form-item label="案例" prop="case_study">
              <el-input v-model="nodeForm.case_study" style="width:235px;" />
            </el-form-item>
          </template>
          
          <el-form-item label="备注" prop="remark">
            <el-input v-model="nodeForm.remark" type="textarea" style="width:590px;" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="nodeDialogVisible = false">取消</el-button>
          <el-button type="success" @click="saveNode()">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, reactive, ref, toRefs, nextTick } from "vue";
import { ElMessage, ElMessageBox, FormInstance } from "element-plus";
import { getKcNodeList, saveKcNodeData, updateKcNodeStatus } from '@/api/kcmgr';
import { useRouter } from 'vue-router';
import { Edit, Delete, View, CirclePlus } from '@element-plus/icons-vue';

const router = useRouter();

// 表格数据
const tableData = reactive({
  tableDataData: [],
  loading: false
});

// 表格高度
const tableHeight = ref('calc(100vh - 180px)');

// 表格展开状态
const tableExpansion = ref(false);
const openButtonText = ref('展开全部');
const tablexTree = ref();

// 是否显示全部（包括禁用的节点）
const isShow = ref(false);

// 对话框相关
const nodeDialogVisible = ref(false);
const dialogTitle = ref('添加课程节点');
const formLabelWidth = ref('100px');
const nodeFormRef = ref<FormInstance>();

// 表单数据
const nodeForm = ref({
  id: '',
  course_code: '',
  course_name: '',
  chapter_code: '',
  chapter_name: '',
  node_code: '',
  node_name: '',
  node_status: true,
  level_type: 'node',
  display_order: 0,
  create_date: '',
  create_user: '',
  is_knowledge_point_linked: false,
  knowledgePoints: [],
  linked_knowledge_point_name: '',
  question_count: 0,
  text_image: '',
  video: '',
  case_study: '',
  remark: '',
  parent_id: 0 // 添加父节点ID字段
});

// 表单验证规则
const rules = reactive({
  course_code: [
    { required: true, message: '请输入课程代码', trigger: 'blur' }
  ],
  course_name: [
    { required: true, message: '请输入课程名称', trigger: 'blur' }
  ],
  chapter_name: [
    { required: (form: { level_type: string }) => form.level_type === 'chapter', message: '请输入章节名称', trigger: 'blur' }
  ],
  node_name: [
    { required: (form: { level_type: string }) => form.level_type === 'node', message: '请输入节点名称', trigger: 'blur' }
  ]
});

// 知识点选项
const knowledgePointOptions = ref([
  { value: '会计基础概念', label: '会计基础概念' },
  { value: '会计职能', label: '会计职能' },
  { value: '会计目标', label: '会计目标' },
  { value: '资产', label: '资产' },
  { value: '流动资产', label: '流动资产' },
  { value: '非流动资产', label: '非流动资产' },
  { value: '经济法基础', label: '经济法基础' },
  { value: '法律特征', label: '法律特征' },
  { value: '调整对象', label: '调整对象' },
  { value: '经济活动', label: '经济活动' },
  { value: '统计学基础', label: '统计学基础' },
  { value: '数据分析', label: '数据分析' },
  { value: '统计学应用', label: '统计学应用' },
  { value: '数据挖掘', label: '数据挖掘' },
  { value: '集中趋势', label: '集中趋势' },
  { value: '平均值', label: '平均值' },
  { value: '中位数', label: '中位数' },
  { value: '管理基础', label: '管理基础' },
  { value: '管理理论', label: '管理理论' },
  { value: '管理环境', label: '管理环境' },
  { value: '外部环境', label: '外部环境' },
  { value: '计划职能', label: '计划职能' },
  { value: '战略规划', label: '战略规划' },
  { value: '营销基础', label: '营销基础' },
  { value: '市场分析', label: '市场分析' },
  { value: '营销目标', label: '营销目标' },
  { value: '市场份额', label: '市场份额' },
  { value: '消费者行为', label: '消费者行为' },
  { value: '购买决策', label: '购买决策' }
]);

// 获取数据
const getData = async () => {
  tableData.loading = true;
  try {
    const params = {
      showAll: isShow.value ? 1 : 0
    };
    const res = await getKcNodeList(params);
    if (res && res.data) {
      tableData.tableDataData = res.data;
    } else {
      tableData.tableDataData = [];
    }
  } catch (error) {
    console.error('获取课程节点数据失败', error);
    ElMessage.error('获取课程节点数据失败');
  } finally {
    tableData.loading = false;
  }
};

// 打开添加节点对话框
const openAddNodeDialog = () => {
  dialogTitle.value = '添加课程节点';
  nodeForm.value = {
    id: '',
    course_code: '',
    course_name: '',
    chapter_code: '',
    chapter_name: '',
    node_code: '',
    node_name: '',
    node_status: true,
    level_type: 'node',
    display_order: 0,
    create_date: '',
    create_user: '',
    is_knowledge_point_linked: false,
    knowledgePoints: [],
    linked_knowledge_point_name: '',
    question_count: 0,
    text_image: '',
    video: '',
    case_study: '',
    remark: '',
    parent_id: 0
  };
  nodeDialogVisible.value = true;
};

// 打开编辑节点对话框
const openEditNode = (row: any) => {
  dialogTitle.value = '编辑课程节点';
  nodeForm.value = {
    ...row,
    knowledgePoints: row.linked_knowledge_point_name ? row.linked_knowledge_point_name.split(',').map((item: string) => item.trim()) : []
  };
  nodeDialogVisible.value = true;
};

// 打开添加子节点对话框
const openAddChildNode = (row: any) => {
  let nextLevelType = '';
  let title = '';
  
  // 根据父节点的级别确定下一级别的类型
  if (row.level_type === 'course') {
    nextLevelType = 'chapter';
    title = '添加章节';
  } else if (row.level_type === 'chapter') {
    nextLevelType = 'node';
    title = '添加节点';
  }
  
  dialogTitle.value = title;
  
  nodeForm.value = {
    id: '',
    course_code: row.course_code,
    course_name: row.course_name,
    chapter_code: nextLevelType === 'node' ? row.chapter_code : '',
    chapter_name: nextLevelType === 'node' ? row.chapter_name : '',
    node_code: '',
    node_name: '',
    node_status: true,
    level_type: nextLevelType,
    display_order: 0,
    create_date: '',
    create_user: '',
    is_knowledge_point_linked: false,
    knowledgePoints: [],
    linked_knowledge_point_name: '',
    question_count: 0,
    text_image: '',
    video: '',
    case_study: '',
    remark: '',
    parent_id: row.id
  };
  
  nodeDialogVisible.value = true;
};

// 保存节点
const saveNode = async () => {
  if (!nodeFormRef.value) return;
  
  await nodeFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const formData = { ...nodeForm.value };
        // 处理知识点数据
        formData.linked_knowledge_point_name = formData.knowledgePoints.join(',');
        formData.is_knowledge_point_linked = formData.knowledgePoints.length > 0;
        
        // 根据节点级别处理数据
        if (formData.level_type === 'course') {
          formData.chapter_name = '';
          formData.node_name = '';
        } else if (formData.level_type === 'chapter') {
          formData.node_name = '';
        }
        
        const res = await saveKcNodeData(formData);
        if (res && res.data) {
          ElMessage({
            message: '保存成功！',
            type: 'success',
          });
          nodeDialogVisible.value = false;
          getData();
        }
      } catch (error) {
        console.error('保存课程节点失败', error);
        ElMessage.error('保存课程节点失败');
      }
    }
  });
};

// 检查状态变更
const checkStatus = () => {
  if (nodeForm.value.node_status === false) {
    ElMessage({
      message: '你正在禁用课程节点，禁用后该节点将不可用！',
      type: 'warning',
    });
  }
};

// 更新节点状态
const updateNodeStatus = async (row: any) => {
  try {
    const params = { id: row.id, node_status: row.node_status };
    const res = await updateKcNodeStatus(params);
    if (res && res.data) {
      ElMessage({
        message: '更新成功！',
        type: 'success',
      });
    }
  } catch (error) {
    console.error('更新课程节点状态失败', error);
    ElMessage.error('更新课程节点状态失败');
  }
};

// 表格行样式
const tableRowClassName = (row: any) => {
  if (row.node_status === false) {
    return 'disabled-row';
  }
  return '';
};

// 数据全部展开与关闭
const openRowExpansion = () => {
  if (tableExpansion.value) {
    tableExpansion.value = false;
    openButtonText.value = '展开全部';
    dataTabdata(tableData.tableDataData, false);
  } else {
    tableExpansion.value = true;
    openButtonText.value = '全部折叠';
    dataTabdata(tableData.tableDataData, true);
  }
};

const dataTabdata = (data: any, sta: boolean) => {
  data.forEach((i: any) => {
    tablexTree.value.toggleRowExpansion(i, sta);
    if (i.children) {
      forArr(i.children, sta);
    }
  });
};

const forArr = (arr: any, sta: boolean) => {
  arr.forEach((i: any) => {
    tablexTree.value.toggleRowExpansion(i, sta);
    if (i.children) {
      forArr(i.children, sta);
    }
  });
};

// 跳转到课程设计页面
const goToCourseDesign = (row: any) => {
  // 这里可以根据实际路由配置跳转到课程设计页面
  router.push({
    path: '/course-design',
    query: {
      courseId: row.id,
      courseName: row.course_name
    }
  });
};

onMounted(() => {
  getData();
});
</script>

<style lang="scss" scoped>
.kcnode-list {
  .body {
    background-color: #fff;
  }

  .nodeDialogClass {
    margin-top: 10vh !important;
    width: 800px;
  }
}

:deep(.disabled-row) {
  background-color: #f9f9f9;
  color: #999;
}

:deep(.el-table__row--level-0) {
  font-weight: bold;
  background-color: #f5f7fa;
}

:deep(.el-table__row--level-1) {
  background-color: #f9fafc;
}
</style>
