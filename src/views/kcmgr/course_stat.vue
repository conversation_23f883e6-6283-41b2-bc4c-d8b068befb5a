<template>
  <div class="course_info page">
    <div class="header">
      <el-button plain
                 type="success"
                 size="small"
                 @click="reloadData"
                 icon="refresh">刷新
      </el-button>
      <el-input placeholder="课程代码、名称关键字查询"
                v-model="init_data.key_search"
                size="small"
                @change="searchData" clearable style="width: 260px;">
        <template #prepend>
          查询
        </template>
      </el-input>
      <el-tag type="info">筛选1</el-tag>
      <el-checkbox-group v-model="init_data.learn_type_id" size="small" @change="searchData">
        <el-checkbox v-for="item in init_data.learn_type_ls" :key="item.v" :label="item.t" :value="item.v" border>
          {{ item.t }} {{ item.num }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <div class="body">
      <el-table
          :data="course_ls_local"
          width="100%"
          height="calc(100vh - 135px)"
          v-loading="init_data.pageLoading"
          ref="multipleTable"
          highlight-current-row
          size="small"
          border
          :element-loading-text="init_data.pageLoadingText"
          :element-loading-spinner="init_data.pageLoadingSvg"
          element-loading-svg-view-box="-10, -10, 50, 50"
          element-loading-background="rgba(122, 122, 122, 0.8)"
      >
        <el-table-column label="序号"
                         align="center"
                         width="45"
                         header-align="center"
                         prop="sn">
        </el-table-column>
        <el-table-column label="基础课程Id"
                         align="center"
                         width="100"
                         sortable
                         header-align="center"
                         prop="course_base_id">
        </el-table-column>
        <el-table-column label="课程代码"
                         align="center"
                         sortable
                         width="80"
                         header-align="center"
                         prop="kc_bm">
        </el-table-column>
        <el-table-column label="课程名称"
                         sortable
                         align="left"
                         min-width="150"
                         header-align="center"
                         show-overflow-tooltip
                         prop="kc_mc">
        </el-table-column>
        <el-table-column label="类别"
                         align="center"
                         sortable
                         width="80"
                         header-align="center"
                         prop="learn_type_title">
        </el-table-column>
        <el-table-column label="在线"
                         sortable
                         align="center"
                         width="80"
                         header-align="center"
                         prop="num_online">
          <template #default="scope">
            <span v-if="scope.row.num_online>0">{{ scope.row.num_online }}</span>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column label="发布"
                         sortable
                         align="center"
                         width="80"
                         header-align="center"
                         prop="num_pub">
          <template #default="scope">
            <span v-if="scope.row.num_pub>0">{{ scope.row.num_pub }}</span>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column label="教材"
                         sortable
                         align="center"
                         width="80"
                         header-align="center"
                         prop="num_jc">
          <template #default="scope">
            <span v-if="scope.row.num_jc>0">{{ scope.row.num_jc }}</span>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column label="考试大纲"
                         sortable
                         align="center"
                         width="80"
                         header-align="center"
                         prop="num_ksdg">
          <template #default="scope">
            <span v-if="scope.row.num_ksdg>0">{{ scope.row.num_ksdg }}</span>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column label="电子书"
                         sortable
                         align="center"
                         width="80"
                         header-align="center"
                         prop="num_dzs">
          <template #default="scope">
            <span v-if="scope.row.num_dzs>0">{{ scope.row.num_dzs }}</span>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column label="题库"
                         sortable
                         align="center"
                         width="80"
                         header-align="center"
                         prop="num_job">
          <template #default="scope">
            <span v-if="scope.row.num_job>0">{{ scope.row.num_job }}</span>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column label="视频"
                         sortable
                         align="center"
                         width="80"
                         header-align="center"
                         prop="num_video">
          <template #default="scope">
            <span v-if="scope.row.num_video>0">{{ scope.row.num_video }}</span>
            <span v-else></span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>


</template>

<script setup lang='ts'>
import {nextTick, onMounted, ref, computed} from "vue";
import {I_course_stat} from "@/utils/types";
import {AddCourseInfo, CourseInfo, CourseProgressStat, DelCourseInfo, UpdCourseInfo} from "@/api/course";
import {msgShow} from "@/utils/message";
import {ElMessageBox} from "element-plus";
import {tr} from "element-plus/es/locale";
import {ArrowRight, ArrowLeft, Search, Rank} from '@element-plus/icons-vue'
import {getVideoList, getCourseList} from '@/api/kcmgr';
import draggable from 'vuedraggable'

const course_ls_svr = ref<I_course_stat[]>([])
const course_ls_local = ref<I_course_stat[]>([])
const init_data = ref({
  pageLoading: false,
  pageLoadingText: "获取数据中，请稍后...",
  pageLoadingSvg: `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `,
  key_search: '',
  learn_type_id: ['2'],
  learn_type_ls: [{t: '成人高考', v: '1', num: 0}, {t: '自考', v: '2', num: 0}, {t: '微课', v: '3', num: 0}]
})
onMounted(async () => {
  await nextTick(async () => {
    await reloadData()
  });
});

const searchData = () => {
  course_ls_local.value = course_ls_svr.value.filter(f => {
    const {key_search, learn_type_id} = init_data.value;
    return (
        f.kc_bm.includes(key_search) || f.kc_mc.includes(key_search)
    ) && (
        learn_type_id.includes(f.learn_type_id.toString())
    )
  });
  init_data.value.learn_type_ls.forEach((f: any) => {
    f.num = course_ls_svr.value.filter(f1 => f1.learn_type_id.toString() === f.v).length
  })
}
const reloadData = async () => {
  await getCourseProgressStatData()
}
const getCourseProgressStatData = async () => {
  await CourseProgressStat().then(res => {
    if (res.code === 200) {
      course_ls_svr.value = res.data
      searchData()
    } else {
      msgShow(res.msg, 'warning')
    }
  }).catch(err => {
    msgShow(err.toString(), 'error')
  })
}
</script>

<style lang="scss" scoped>
.course_info {

  :deep .el-table--enable-row-hover .el-table__body tr:hover > td {
    background: transparent;
  }

  :deep .el-table__footer {
    font-weight: bold;
    height: 40px !important;

  }

  :deep .el-table__footer-wrapper tbody td.el-table__cell, .el-table__header-wrapper tbody td.el-table__cell {
    background-color: #bdd6f6;
  }

  :deep .el-table-fixed-column--left {
    //background:none!important;
  }

}
</style>
