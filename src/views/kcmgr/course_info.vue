<template>
  <div class="course_info page">
    <div class="header">
      <el-button plain
                 type="success"
                 size="small"
                 @click="reloadData"
                 icon="refresh">刷新
      </el-button>
      <el-input placeholder="课程代码、名称关键字查询"
                v-model="init_data.key_search"
                size="small"
                @change="searchData" clearable style="width: 260px;">
        <template #prepend>
          查询
        </template>
      </el-input>
      <el-tag type="info">筛选1</el-tag>
      <el-checkbox-group v-model="init_data.is_online" size="small" @change="searchData">
        <el-checkbox v-for="item in init_data.online_ls" :key="item.v" :label="item.t" :value="item.v" border>
          {{ item.t }}
        </el-checkbox>
      </el-checkbox-group>
      <el-tag type="info">筛选2</el-tag>
      <el-checkbox-group v-model="init_data.is_pub" size="small" @change="searchData">
        <el-checkbox v-for="item in init_data.pub_ls" :key="item.v" :label="item.t" :value="item.v" border>
          {{ item.t }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <div class="body">
      <el-table
          :data="course_ls_local"
          width="100%"
          height="calc(100vh - 135px)"
          v-loading="init_data.pageLoading"
          ref="multipleTable"
          highlight-current-row
          size="small"
          border
          :element-loading-text="init_data.pageLoadingText"
          :element-loading-spinner="init_data.pageLoadingSvg"
          element-loading-svg-view-box="-10, -10, 50, 50"
          element-loading-background="rgba(122, 122, 122, 0.8)"
          :row-style="tableRowStyle"
          :cell-style="tableCellStyle"
      >
        <el-table-column label="序号"
                         align="center"
                         width="45"
                         header-align="center"
                         prop="sn">
        </el-table-column>
        <el-table-column label="课程ID"
                         align="center"
                         width="60"
                         header-align="center"
                         prop="id">
        </el-table-column>
        <el-table-column label="课程代码"
                         align="center"
                         sortable
                         width="80"
                         header-align="center"
                         prop="kc_bm">
        </el-table-column>
        <el-table-column label="课程名称"
                         sortable
                         align="left"
                         min-width="150"
                         header-align="center"
                         show-overflow-tooltip
                         prop="kc_mc">
        </el-table-column>
        <el-table-column label="版本号"
                         sortable
                         align="center"
                         width="80"
                         header-align="center"
                         prop="ver">
        </el-table-column>
        <el-table-column label="版本操作"
                         align="center"
                         width="180"
                         header-align="center"
                         prop="ctrl_ver">
          <template #default="scope">
            <el-button size="small" type="primary" icon="plus" @click="addData(scope.row.course_base_id)">新增
            </el-button>
            <el-button size="small" type="danger" icon="delete" :disabled="scope.row.ver===1 || scope.row.is_pub===true"
                       @click="delData(scope.row.id)" :title="scope.row.ver===1 || scope.row.is_pub===true?'初始版本或已发布版本不可删除':''">
              删除
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="在线情况"
                         sortable
                         align="center"
                         width="120"
                         header-align="center"
                         prop="is_online">
          <template #default="scope">
            <el-select v-model="scope.row.is_online" placeholder="请选择" size="small" @change="updData(scope.row.id)">
              <el-option
                  v-for="item in init_data.online_ls"
                  :key="item.v"
                  :label="item.t"
                  :value="item.v"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="发布状态"
                         sortable
                         align="center"
                         width="120"
                         header-align="center"
                         prop="is_pub">
          <template #default="scope">
            <el-switch size="small"
                       v-model="scope.row.is_pub"
                       inline-prompt
                       style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                       active-text="已发布"
                       inactive-text="未发布"
                       width="80px"
                       @change="updData(scope.row.id)"
            />
          </template>
        </el-table-column>
        <el-table-column label="课程视频"
                         align="center"
                         width="120"
                         header-align="center">
          <template #default="scope">
            <el-button v-if="!scope.row.video_ids || scope.row.video_ids.length === 0 || (scope.row.video_ids.length === 1 && scope.row.video_ids[0] == 0)"
                       size="small"
                       type="primary"
                       icon="plus"
                       @click="handleAddVideos(scope.row)">添加
            </el-button>
            <el-button v-else
                       size="small"
                       type="info"
                       icon="video-play"
                       @click="handleViewVideos(scope.row)">查看
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="发布时间"
                         align="center"
                         sortable
                         width="120"
                         header-align="center"
                         prop="pub_date">
        </el-table-column>
        <el-table-column label="发布人员"
                         align="center"
                         width="80"
                         header-align="center"
                         prop="pub_user">
        </el-table-column>
        <el-table-column label="最近操作时间"
                         align="center"
                         sortable
                         width="120"
                         header-align="center"
                         prop="create_date">
        </el-table-column>
        <el-table-column label="最近操作人员"
                         align="center"
                         width="120"
                         header-align="center"
                         prop="create_user">
        </el-table-column>
      </el-table>
    </div>
  </div>

  <!-- 视频关联对话框 -->
  <el-dialog
      v-model="videoDialog.visible"
      :title="videoDialog.title"
      width="1600px"
      :close-on-click-modal="false">
    <div class="video-dialog-content">
      <div class="video-list left">
        <div class="list-header">
          <span>全部视频</span>
          <el-select v-model="videoDialog.selectedCourseId"
                     placeholder="请选择课程"
                     clearable
                     size="small"
                     style="width: 200px; margin-left: 10px;"
                     @change="handleCourseChange">
            <el-option
                v-for="item in courseList"
                :key="item.id"
                :label="item.kc_mc"
                :value="item.id">
            </el-option>
          </el-select>
        </div>
        <el-table
            :data="videoDialogFilteredVideos"
            height="600"
            border
            size="small"
            style="width:100%;"
            @selection-change="handleVideoSelectionChange">
          <el-table-column type="selection" width="48" />
          <el-table-column label="课程名称" prop="course_base_id" min-width="150" show-overflow-tooltip>
            <template #default="scope">
              {{ getCourseName(scope.row.course_base_id) }}
            </template>
          </el-table-column>
          <el-table-column label="视频标题" prop="title" show-overflow-tooltip />
          <el-table-column label="时长" prop="duration_s" width="100" align="center" />
          <el-table-column label="大小" prop="size_s" width="100" align="center" />
        </el-table>
      </div>
      <div class="video-actions">
        <el-button type="primary" 
                   :disabled="!videoDialog.selectedVideos.length"
                   @click="handleAddSelectedVideos"
                   style="width:40px;height:40px;display:flex;align-items:center;justify-content:center;">
          <el-icon><arrow-right /></el-icon>
        </el-button>
        <el-button type="primary" 
                   :disabled="!videoDialog.selectedRelatedVideos.length"
                   @click="handleRemoveSelectedVideos"
                   style="width:40px;height:40px;display:flex;align-items:center;justify-content:center;margin-left:-1px">
          <el-icon><arrow-left /></el-icon>
        </el-button>
      </div>
      <div class="video-list right">
        <div class="list-header">已关联视频</div>
        <div class="el-table" style="width: 100%;">
          <div class="el-table__header">
            <table cellspacing="0" cellpadding="0" border="0" style="width: 100%;">
              <colgroup>
                <col width="48">
                <col width="50">
                <col width="150">
                <col>
                <col width="100">
                <col width="100">
              </colgroup>
              <thead>
                <tr>
                  <th class="el-table__cell"></th>
                  <th class="el-table__cell">排序</th>
                  <th class="el-table__cell">课程名称</th>
                  <th class="el-table__cell">视频标题</th>
                  <th class="el-table__cell">时长</th>
                  <th class="el-table__cell">大小</th>
                </tr>
              </thead>
            </table>
          </div>
          <draggable
              v-model="videoDialog.relatedVideos"
              item-key="id"
              handle=".drag-handle"
              animation="150"
              ghost-class="ghost"
              class="el-table__body"
              :list="videoDialog.relatedVideos"
              @end="onDragEnd">
            <template #item="{ element }">
              <div class="el-table__row">
                <div class="el-table__cell" style="width: 48px; text-align: center;">
                  <el-checkbox v-model="element.selected" @change="handleRelatedVideoSelectionChange" />
                </div>
                <div class="el-table__cell" style="width: 50px; text-align: center;">
                  <el-icon class="drag-handle" style="cursor: move;"><Rank /></el-icon>
                </div>
                <div class="el-table__cell" style="width: 150px; padding: 0 10px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                  {{ getCourseName(element.course_base_id) }}
                </div>
                <div class="el-table__cell" style="flex: 1; padding: 0 10px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                  {{ element.title }}
                </div>
                <div class="el-table__cell" style="width: 100px; text-align: center;">
                  {{ element.duration_s }}
                </div>
                <div class="el-table__cell" style="width: 100px; text-align: center;">
                  {{ element.size_s }}
                </div>
              </div>
            </template>
          </draggable>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="videoDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveVideos">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang='ts'>
import {nextTick, onMounted, ref, computed} from "vue";
import {I_course_info} from "@/utils/types";
import {AddCourseInfo, CourseInfo, DelCourseInfo, UpdCourseInfo} from "@/api/course";
import {msgShow} from "@/utils/message";
import {ElMessageBox} from "element-plus";
import {tr} from "element-plus/es/locale";
import { ArrowRight, ArrowLeft, Search, Rank } from '@element-plus/icons-vue'
import { getVideoList, getCourseList } from '@/api/kcmgr';
import draggable from 'vuedraggable'

const course_ls_svr = ref<I_course_info[]>([])
const course_ls_local = ref<I_course_info[]>([])
const init_data = ref({
  pageLoading: false,
  pageLoadingText: "获取数据中，请稍后...",
  pageLoadingSvg: `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `,
  key_search: '',
  is_online: ['-1', '1', '0'],
  is_pub: ['1', '0'],
  online_ls: [{t: '未指定', v: '-1'}, {t: '线上课程', v: '1'}, {t: '线下课程', v: '0'}],
  pub_ls: [{t: '已发布', v: '1'}, {t: '未发布', v: '0'}]
})

// 视频关联对话框数据
const videoDialog = ref({
  searchKeyword:"",
  visible: false,
  title: '',
  courseId: 0,
  allVideos: [] as any[],
  relatedVideos: [] as any[],
  selectedVideos: [] as any[],
  selectedRelatedVideos: [] as any[],
  selectedCourseId: null as number | null,
  is_online: 0,
  is_pub: false
})

// 课程列表
const courseList = ref<Array<{id: number, kc_mc: string}>>([])

// 获取课程列表
const getCourseListData = async () => {
  try {
    const res = await getCourseList()
    if (res.code === 200) {
      courseList.value = res.data
    } else {
      msgShow(res.msg || '获取课程列表失败', 'warning')
    }
  } catch (error) {
    msgShow('获取课程列表失败', 'error')
  }
}

// 获取课程名称
const getCourseName = (courseId: number | null) => {
  if (!courseId) return '-'
  const course = courseList.value.find(c => Number(c.id) === Number(courseId))
  return course?.kc_mc || '-'
}

// 过滤后的视频列表
const videoDialogFilteredVideos = computed(() => {
  // 过滤掉已在右侧的
  const relatedIds = videoDialog.value.relatedVideos.map(v => Number(v.id))
  let list = videoDialog.value.allVideos.filter(v => !relatedIds.includes(Number(v.id)))
  
  // 按课程ID过滤
  if (videoDialog.value.selectedCourseId) {
    list = list.filter(video => Number(video.course_base_id) === Number(videoDialog.value.selectedCourseId))
  }
  
  return list
})

onMounted(async () => {
  await nextTick(async () => {
    await reloadData()
  });
});

const delData = (id: number) => {
  let t_row = course_ls_local.value.find(f => f.id === id)
  if (t_row) {
    if (t_row.is_pub === true) {
      msgShow('未发布的课程才能删除', 'warning')
    } else {
      let t_msg = `<div class="danger f_b">您确定要删除以下课程吗？</p><p>课程代码：${t_row.kc_bm}</p><p>课程名称：${t_row.kc_mc}</p><p>版本号：${t_row.ver}</p></div>`
      ElMessageBox.confirm(t_msg, '', {
        confirmButtonText: '确定删除',
        cancelButtonText: '再考虑一下',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(() => {
        DelCourseInfo(id).then(res => {
          if (res.code === 200) {
            msgShow(res.msg, 'success')
          } else {
            msgShow(res.msg, 'warning')
          }
        }).catch(e => {
          msgShow(e.toString(), 'error')
        }).finally(() => {
          reloadData()
        })
      }).catch(() => {

      });
    }
  }
}
const addData = (course_base_id: number) => {
  AddCourseInfo(course_base_id).then(res => {
    if (res.code === 200) {
      msgShow(res.msg, 'success')
    } else {
      msgShow(res.msg, 'warning')
    }
  }).catch(e => {
    msgShow(e.toString(), 'error')
  }).finally(() => {
    reloadData()
  })
}
const updData = (id: number) => {
  let t_row = course_ls_local.value.find(f => f.id === id)
  if (t_row) {
    UpdCourseInfo(t_row.id, parseInt(t_row.is_online.toString()), t_row.is_pub ? 1 : 0,t_row.video_ids).then(res => {
      if (res.code === 200) {
        msgShow(res.msg, 'success')
      } else {
        msgShow(res.msg, 'warning')
      }
    }).catch(e => {
      msgShow(e.toString(), 'error')
    }).finally(() => {
      reloadData()
    })
  }
}
const searchData = () => {
  course_ls_local.value = course_ls_svr.value.filter(f => {
    const {key_search, is_online, is_pub} = init_data.value;
    return (
        f.kc_bm.includes(key_search) || f.kc_mc.includes(key_search)
    ) && (
        is_online.includes(f.is_online.toString())
    ) && (
        is_pub.includes(f.is_pub ? '1' : '0')
    );
  });
}

const reloadData = async () => {
  await getCourseInfoData()
}
const getCourseInfoData = async () => {
  await CourseInfo().then(res => {
    if (res.code === 200) {
      course_ls_svr.value = res.data
      searchData()
    } else {
      msgShow(res.msg, 'warning')
    }
  }).catch(err => {
    msgShow(err.toString(), 'error')
  })
}
const tableCellStyle = ({
                          row = {is_online: '0', is_pub: 0},
                          column = {property: ''},
                          rowIndex = 0,
                          columnIndex = 0
                        }) => {

  // if (column.property === 'cj_hg_per') {
  //   return {'background-color': '#b8ebf3'}
  // }
  // if (row.is_online === 1) {
  //   return {'background-color': '#eeea07'}
  // }
}
const tableRowStyle = ({row = {is_online: '0', is_pub:0 }}) => {
  if (row.is_pub && row.is_online==='1') {
    return {'background-color': 'rgba(144, 238, 144, 0.2)'}
  }
}

// 处理拖拽结束
const onDragEnd = () => {
  // 更新选中状态
  handleRelatedVideoSelectionChange()
}

// 处理已关联视频选择变化
const handleRelatedVideoSelectionChange = () => {
  videoDialog.value.selectedRelatedVideos = videoDialog.value.relatedVideos.filter(v => v.selected)
}

// 处理课程选择变化
const handleCourseChange = () => {
  videoDialog.value.selectedVideos = []
}

// 处理添加视频
const handleAddVideos = async (row: I_course_info) => {
  videoDialog.value.courseId = row.id
  videoDialog.value.title = `关联视频 - ${row.kc_mc}`
  videoDialog.value.visible = true
  videoDialog.value.relatedVideos = []
  videoDialog.value.selectedCourseId = null
  videoDialog.value.is_online = parseInt(row.is_online.toString())
  videoDialog.value.is_pub = row.is_pub

  try {
    await getCourseListData()
    const params = {
      course_id: null
    };
    const res = await getVideoList(params)
    if (Number(res.code) === 200) {
      videoDialog.value.allVideos = res.data
    }
  } catch (error) {
    msgShow('获取视频列表失败', 'error')
  }
}

// 处理查看视频
const handleViewVideos = async (row: I_course_info) => {
  videoDialog.value.courseId = row.id
  videoDialog.value.title = `查看视频 - ${row.kc_mc}`
  videoDialog.value.visible = true
  videoDialog.value.searchKeyword = ''
  videoDialog.value.is_online = parseInt(row.is_online.toString())
  videoDialog.value.is_pub = row.is_pub

  try {
    const params = {
      query: ""
    };
    const res = await getVideoList(params)
    if (Number(res.code) === 200) {
      videoDialog.value.allVideos = res.data
      // 根据video_ids的顺序来排序已关联的视频
      if (row.video_ids && row.video_ids.length > 0) {
        const videoMap = new Map(res.data.map((video: any) => [video.id, video]))
        videoDialog.value.relatedVideos = row.video_ids
          .map(id => videoMap.get(Number(id)))
          .filter(Boolean)
      } else {
        videoDialog.value.relatedVideos = []
      }
    }
  } catch (error) {
    msgShow('获取视频列表失败', 'error')
  }
}

// 处理视频选择变化
const handleVideoSelectionChange = (selection: any[]) => {
  videoDialog.value.selectedVideos = selection
}

// 添加选中的视频
const handleAddSelectedVideos = () => {
  const newVideos = videoDialog.value.selectedVideos.filter(
    video => !videoDialog.value.relatedVideos.some(rv => Number(rv.id) === Number(video.id))
  )
  videoDialog.value.relatedVideos = [...videoDialog.value.relatedVideos, ...newVideos]
  videoDialog.value.selectedVideos = []
}

// 移除选中的视频
const handleRemoveSelectedVideos = () => {
  const selectedIds = videoDialog.value.selectedRelatedVideos.map(v => Number(v.id))
  videoDialog.value.relatedVideos = videoDialog.value.relatedVideos.filter(
    video => !selectedIds.includes(Number(video.id))
  )
  videoDialog.value.selectedRelatedVideos = []
}

// 保存视频关联
const handleSaveVideos = async () => {
  try {
    // 保存时使用当前排序后的顺序
    const videoIds = videoDialog.value.relatedVideos.map(v => v.id)
    const res = await UpdCourseInfo(
      videoDialog.value.courseId,
      videoDialog.value.is_online,
      videoDialog.value.is_pub ? 1 : 0, 
      videoIds
    )
    if (res.code === 200) {
      msgShow('保存成功', 'success')
      videoDialog.value.visible = false
      reloadData()
    } else {
      msgShow(res.msg || '保存失败', 'error')
    }
  } catch (error) {
    msgShow('保存失败', 'error')
  }
}

</script>

<style lang="scss" scoped>
.course_info{

:deep .el-table--enable-row-hover .el-table__body tr:hover > td {
  background: transparent;
}

:deep .el-table__footer {
  font-weight: bold;
  height: 40px !important;

}

:deep .el-table__footer-wrapper tbody td.el-table__cell, .el-table__header-wrapper tbody td.el-table__cell {
  background-color: #bdd6f6;
}

:deep .el-table-fixed-column--left {
  //background:none!important;
}

.video-dialog-content {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  .video-list {
    flex: 1;
    min-width: 0;
    .list-header {
      font-weight: bold;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
    }
  }
  .video-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding-top: 180px;
    min-width: 60px;
    align-items: center;
  }
}

.video-list {
  .el-table {
    border: 1px solid #EBEEF5;
    border-radius: 4px;
    
    .el-table__header {
      background-color: #f5f7fa;
      
      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 500;
        text-align: center;
        padding: 12px 0;
        border-bottom: 1px solid #EBEEF5;
      }
    }
    
    .el-table__body {
      .el-table__row {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #EBEEF5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .el-table__cell {
          padding: 8px 0;
          border-right: 1px solid #EBEEF5;
          
          &:last-child {
            border-right: none;
          }
        }
      }
    }
  }
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.drag-handle {
  cursor: move;
  color: #909399;
  &:hover {
    color: #409EFF;
  }
}
}
</style>
