<template>
  <div class="course_info page">
    <div class="header">
      <el-button plain type="success" size="small" @click="reloadData" icon="refresh" style="width: 50px;">
      </el-button>
       <el-select v-model="learn_type_key" style="width: 120px;" class="m-2" clearable placeholder="选择学习类型" size="small" @change="getCourseInfoData">
        <el-option key="1" label="成人高考" value="1" />
        <el-option key="2" label="自考" value="2" />
        <el-option key="3" label="微课" value="3" />
      </el-select>
      <el-input placeholder="课程代码、名称关键字查询" v-model="init_data.key_search" size="small" @change="searchData" clearable
        style="width: 240px;">
       
      </el-input>
     
      <el-button type="primary" size="small" @click="handleAddCourse">添加在线课程</el-button>
    </div>
    <div class="header" style="margin-top: 5px;">
      <el-tag type="info">筛选1</el-tag>
      <el-checkbox-group v-model="init_data.is_online" size="small" @change="searchData">
        <el-checkbox v-for="item in init_data.online_ls" :key="item.v" :label="item.t" :value="item.v" border>
          {{ item.t }}
        </el-checkbox>
      </el-checkbox-group>
      <el-tag type="info">筛选2</el-tag>
      <el-checkbox-group v-model="init_data.is_pub" size="small" @change="searchData">
        <el-checkbox v-for="item in init_data.pub_ls" :key="item.v" :label="item.t" :value="item.v" border>
          {{ item.t }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <div class="body">
      <el-table v-loading="tableloading" :data="course_ls_local" width="100%" height="calc(100vh - 163px)"
        ref="multipleTable" highlight-current-row size="small" border :element-loading-text="init_data.pageLoadingText"
        :element-loading-spinner="init_data.pageLoadingSvg" element-loading-svg-view-box="-10, -10, 50, 50"
        element-loading-background="rgba(122, 122, 122, 0.8)" :row-style="tableRowStyle" :cell-style="tableCellStyle">
        <el-table-column label="序号" align="center" width="45" header-align="center" type="index">
        </el-table-column>
        <el-table-column label="学习类型" align="center" sortable width="80" header-align="center" prop="learn_type_name">
        </el-table-column>
        <el-table-column label="课程代码" align="center" sortable width="80" header-align="center" prop="kc_bm">
        </el-table-column>
        <el-table-column label="课程名称" sortable align="left" min-width="150" header-align="center" show-overflow-tooltip
          prop="kc_mc">
        </el-table-column>
        <el-table-column label="版本号" sortable align="center" width="80" header-align="center" prop="ver">
        </el-table-column>
        <el-table-column label="版本操作" align="center" width="180" header-align="center" prop="ctrl_ver">
          <template #default="scope">
            <el-button size="small" type="primary" icon="plus" @click="addData(scope.row.course_base_id)">新增
            </el-button>
            <el-button size="small" type="danger" icon="delete" :disabled="scope.row.is_pub === true"
              @click="delData(scope.row.id)" :title="scope.row.is_pub === true ? '已发布版本不可删除' : ''">
              删除
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="在线情况" sortable align="center" width="120" header-align="center" prop="is_online">
          <template #default="scope">
            <el-select v-model="scope.row.is_online" placeholder="请选择" size="small" @change="updData(scope.row.id)">
              <el-option v-for="item in init_data.online_ls" :key="item.v" :label="item.t" :value="item.v">
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="发布状态" sortable align="center" width="120" header-align="center" prop="is_pub">
          <template #default="scope">
            <el-switch size="small" v-model="scope.row.is_pub" inline-prompt
              style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949" active-text="已发布"
              inactive-text="未发布" width="80px" @change="updData(scope.row.id)" />
          </template>
        </el-table-column>
        <el-table-column prop="kc_mc" label="板块" align="center" width="100">
          <template #default="scope">
            <el-link @click="openPlateData(scope.row)" type="primary">指定板块</el-link>

          </template>
        </el-table-column>
        <el-table-column prop="kc_mc" label="板块数量" align="center" width="70">
          <template #default="scope">
            <el-badge v-if="scope.row.platenum > 0" type="primary" :value="scope.row.platenum">
            </el-badge>
            <el-badge v-else :value="scope.row.platenum" type="info">
            </el-badge>
          </template>
        </el-table-column>
        <!--
        <el-table-column label="课程视频"
                         align="center"
                         width="120"
                         header-align="center">
          <template #default="scope">
            <el-button v-if="!scope.row.video_ids || scope.row.video_ids.length === 0 || (scope.row.video_ids.length === 1 && scope.row.video_ids[0] == 0)"
                       size="small"
                       type="primary"
                       icon="plus"
                       @click="handleAddVideos(scope.row)">添加
            </el-button>
            <el-button v-else
                       size="small"
                       type="info"
                       icon="video-play"
                       @click="handleViewVideos(scope.row)">查看
            </el-button>
          </template>
        </el-table-column>
      -->
        <el-table-column label="发布时间" align="center" sortable width="120" header-align="center" prop="pub_date">
        </el-table-column>
        <el-table-column label="发布人员" align="center" width="80" header-align="center" prop="pub_user">
        </el-table-column>
        <el-table-column label="最近操作时间" align="center" sortable width="120" header-align="center" prop="create_date">
        </el-table-column>
        <el-table-column label="最近操作人员" align="center" width="120" header-align="center" prop="create_user">
        </el-table-column>
        <el-table-column label="操作" align="center" width="120" header-align="center" fixed="right">
          <template #default="scope">
            <el-button link type="warning" size="small" @click="assignResources(scope.row)">指定资料</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>

  <!-- 视频关联对话框 -->
  <el-dialog v-model="videoDialog.visible" :title="videoDialog.title" width="1600px" :close-on-click-modal="false">
    <div class="video-dialog-content">
      <div class="video-list left">
        <div class="list-header">
          <span>全部视频</span>
          <el-select v-model="videoDialog.selectedCourseId" placeholder="请选择课程" clearable size="small"
            style="width: 200px; margin-left: 10px;" @change="handleCourseChange">
            <el-option v-for="item in courseList" :key="item.id" :label="item.kc_mc" :value="item.id">
            </el-option>
          </el-select>
        </div>
        <el-table :data="videoDialogFilteredVideos" height="600" border size="small" style="width:100%;"
          @selection-change="handleVideoSelectionChange">
          <el-table-column type="selection" width="48" />
          <el-table-column label="课程名称" prop="course_base_id" min-width="150" show-overflow-tooltip>
            <template #default="scope">
              {{ getCourseName(scope.row.course_base_id) }}
            </template>
          </el-table-column>
          <el-table-column label="视频标题" prop="title" show-overflow-tooltip />
          <el-table-column label="时长" prop="duration_s" width="100" align="center" />
          <el-table-column label="大小" prop="size_s" width="100" align="center" />
        </el-table>
      </div>
      <div class="video-actions">
        <el-button type="primary" :disabled="!videoDialog.selectedVideos.length" @click="handleAddSelectedVideos"
          style="width:40px;height:40px;display:flex;align-items:center;justify-content:center;">
          <el-icon><arrow-right /></el-icon>
        </el-button>
        <el-button type="primary" :disabled="!videoDialog.selectedRelatedVideos.length"
          @click="handleRemoveSelectedVideos"
          style="width:40px;height:40px;display:flex;align-items:center;justify-content:center;margin-left:-1px">
          <el-icon><arrow-left /></el-icon>
        </el-button>
      </div>
      <div class="video-list right">
        <div class="list-header">已关联视频</div>
        <div class="el-table" style="width: 100%;">
          <div class="el-table__header">
            <table cellspacing="0" cellpadding="0" border="0" style="width: 100%;">
              <colgroup>
                <col width="48">
                <col width="50">
                <col width="150">
                <col>
                <col width="100">
                <col width="100">
              </colgroup>
              <thead>
                <tr>
                  <th class="el-table__cell"></th>
                  <th class="el-table__cell">排序</th>
                  <th class="el-table__cell">课程名称</th>
                  <th class="el-table__cell">视频标题</th>
                  <th class="el-table__cell">时长</th>
                  <th class="el-table__cell">大小</th>
                </tr>
              </thead>
            </table>
          </div>
          <draggable v-model="videoDialog.relatedVideos" item-key="id" handle=".drag-handle" animation="150"
            ghost-class="ghost" class="el-table__body" :list="videoDialog.relatedVideos" @end="onDragEnd">
            <template #item="{ element }">
              <div class="el-table__row">
                <div class="el-table__cell" style="width: 48px; text-align: center;">
                  <el-checkbox v-model="element.selected" @change="handleRelatedVideoSelectionChange" />
                </div>
                <div class="el-table__cell" style="width: 50px; text-align: center;">
                  <el-icon class="drag-handle" style="cursor: move;">
                    <Rank />
                  </el-icon>
                </div>
                <div class="el-table__cell"
                  style="width: 150px; padding: 0 10px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                  {{ getCourseName(element.course_base_id) }}
                </div>
                <div class="el-table__cell"
                  style="flex: 1; padding: 0 10px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                  {{ element.title }}
                </div>
                <div class="el-table__cell" style="width: 100px; text-align: center;">
                  {{ element.duration_s }}
                </div>
                <div class="el-table__cell" style="width: 100px; text-align: center;">
                  {{ element.size_s }}
                </div>
              </div>
            </template>
          </draggable>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="videoDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveVideos">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <el-dialog draggable :title="selectRow.kc_mc + ' 板块设置'" v-model="PlatedialogVisible" width="700"
    :close-on-click-modal="false" :close-on-press-escape="false" :append-to-body="true" top="6vh">
    <div class="m-2" style="display: flex;justify-content: center;">

      <el-tabs tab-position="top" style="" class="demo-tabs">
        <el-tab-pane label="板块设置">
          <div style="padding: 10px 20px;">
            <div style="padding: 10px 0px;"> 为【<span style="color: #409EFF;font-weight: bold;">{{ selectRow.kc_mc
                }}</span>】课程指定板块
            </div>
            <el-transfer v-model="HavePlateData" :data="plateData" :titles="['未开通板块', '已开通板块']"
              @click="PlageChange()" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="板块详细设置">
          <div style="padding: 0px 20px;height: 380px;width: 610px;overflow-y: auto;">
            <div class="item" v-for="(item, i) in platedSort" :key="item.id" draggable="true"
              @dragstart="dragstart($event, i)" @dragenter="dragenter($event, i)" @dragend="dragend"
              @dragover="dragover">
              <div style="width: 100%;display: flex;align-items: center;">
                <div style="margin-top: 4px;" class="dragCurr">
                  <svg viewBox="0 0 1024 1024" width="30" height="30">
                    <path
                      d="M512 0a512 512 0 0 1 512 512 512 512 0 0 1-512 512A512 512 0 0 1 0 512 512 512 0 0 1 512 0z"
                      fill="#FFFFFF" p-id="7425"></path>
                    <path
                      d="M509.5424 720.6912L593.92 636.5184l35.2256 35.2256-119.1936 118.784-118.784-118.784 35.2256-35.2256zM509.952 245.76l118.784 118.784-34.816 35.2256-83.7632-84.1728-84.1728 84.1728L389.12 364.544l119.808-118.784zM307.2 482.304v-49.7664h409.6v49.7664z m0 112.8448v-49.7664h409.6v49.7664z"
                      fill="#2693FF" p-id="7426"></path>
                  </svg>
                </div>
                <div style="margin-left: 15px;">
                  【{{ i + 1 }}】 {{ item.title }}
                </div>
              </div>
              <div style="margin-left: 40px;margin-top: 1px;" class="cj_ksplan-item">
                <el-select v-model="item.is_list" placeholder="展示方式" size="mini" style="width: 100px">
                  <el-option :key="1" label="有列表" :value="1" />
                  <el-option :key="0" label="无列表" :value="0" />
                </el-select>
                <el-select v-model="item.is_blank" placeholder="打开方式" size="mini"
                  style="width: 150px;margin-left: 15px;">
                  <el-option :key="1" label="新标签页打开" :value="1" />
                  <el-option :key="0" label="当前页打开" :value="0" />
                </el-select>
                <el-select v-model="item.show_progress" placeholder="显示进度条" size="mini"
                  style="width: 150px;margin-left: 15px;">
                  <el-option :key="1" label="显示进度条" :value="1" />
                  <el-option :key="0" label="不显示进度条" :value="0" />
                </el-select>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>



    </div>
    <div class="dialog-footer" style="padding: 10px 20px;text-align: center;border-top: 1px solid #F2F6FC;">
      <el-button @click="PlatedialogVisible = false">取消</el-button>
      <el-button type="primary" @click="savePlate()">保存
      </el-button>
    </div>
  </el-dialog>

  <el-dialog v-model="EditdialogVisible" title="添加在线课程" width="500">
    <div>
      <el-form :label-position="'top'" label-width="auto" style="width: 300px;margin: 0 auto;">
        <el-form-item label="基础课程" label-position="right">
          <el-select v-model="formData.course_base_id" placeholder="选择课程" style="width: 240px" filterable>
            <el-option v-for="item in kcData" :key="item.id" :disabled="item.disabled" :label="item.kc_mcs"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="版本" label-position="right">
          <el-input-number v-model="formData.ver" :min="1" :max="999" />
        </el-form-item>
        <el-form-item label="在线情况" label-position="right">
          <el-select v-model="formData.is_online" placeholder="请选择" style="width: 240px;">
            <el-option v-for="item in init_data.online_ls" :key="item.v" :label="item.t" :value="item.v">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer" style="text-align: center;">
        <el-button @click="EditdialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveCourseInfo">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 指定资料对话框 -->
  <AssignResourceDialog
    v-model="assignResourceDialog.visible"
    :courseInfoId="assignResourceDialog.courseInfoId"
    :courseBaseId="assignResourceDialog.courseBaseId"
    :title="assignResourceDialog.title"
    @save="handleAssignResourceSave"
  />
</template>

<script setup lang='ts'>
import { nextTick, onMounted, ref, computed } from "vue";
import { I_course_info } from "@/utils/types";
import {
  AddCourseInfo, CourseInfo, DelCourseInfo, UpdCourseInfo
  , getPlateDataForOlsCourseById, saveOlsCoursePlateByPlanid, saveOlsCourse
} from "@/api/course";
import { msgShow } from "@/utils/message";
import { ElMessageBox, ElMessage } from "element-plus";
import { tr } from "element-plus/es/locale";
import { ArrowRight, ArrowLeft, Search, Rank } from '@element-plus/icons-vue'
import { getVideoList, getCourseList, getCourseJcList } from '@/api/kcmgr';
import {
  getKcData
} from '@/api/exam'
import draggable from 'vuedraggable'
import AssignResourceDialog from '@/components/course/AssignResourceDialog.vue'

const learn_type_key=ref("")
const course_ls_svr = ref<I_course_info[]>([])
const course_ls_local = ref<I_course_info[]>([])
const tableloading = ref(false)

const PlatedialogVisible = ref(false)
const plateData = ref([])
const HavePlateData: any = ref([])
const selectRow: any = ref({})
const courseData: any = ref([])
const platedSort: any = ref([])
const EditdialogVisible: any = ref(false)
const kcData: any = ref([])
const formData: any = ref({
  course_base_id: "",
  ver: 1,
  is_online: '0',

})
const init_data = ref({
  pageLoading: false,
  pageLoadingText: "获取数据中，请稍后...",
  pageLoadingSvg: `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `,
  key_search: '',
  is_online: ['-1', '1', '0'],
  is_pub: ['1', '0'],
  online_ls: [{ t: '未指定', v: '-1' }, { t: '线上课程', v: '1' }, { t: '线下课程', v: '0' }],
  pub_ls: [{ t: '已发布', v: '1' }, { t: '未发布', v: '0' }]
})

// 视频关联对话框数据
const videoDialog = ref({
  searchKeyword: "",
  visible: false,
  title: '',
  courseId: 0,
  allVideos: [] as any[],
  relatedVideos: [] as any[],
  selectedVideos: [] as any[],
  selectedRelatedVideos: [] as any[],
  selectedCourseId: null as number | null,
  is_online: 0,
  is_pub: false
})
// 课程列表
const courseList = ref<Array<{ id: number, kc_mc: string }>>([])

// 指定资料对话框
const assignResourceDialog = ref({
  visible: false,
  courseInfoId: 0,
  courseBaseId: 0,
  title: ''
})
// 获取课程列表
const getCourseListData = async () => {
  try {
    const res = await getCourseList()
    if (res.code === 200) {
      courseList.value = res.data
    } else {
      msgShow(res.msg || '获取课程列表失败', 'warning')
    }
  } catch (error) {
    msgShow('获取课程列表失败', 'error')
  }
}
// 获取课程名称
const getCourseName = (courseId: number | null) => {
  if (!courseId) return '-'
  const course = courseList.value.find(c => Number(c.id) === Number(courseId))
  return course?.kc_mc || '-'
}
// 过滤后的视频列表
const videoDialogFilteredVideos = computed(() => {
  // 过滤掉已在右侧的
  const relatedIds = videoDialog.value.relatedVideos.map(v => Number(v.id))
  let list = videoDialog.value.allVideos.filter(v => !relatedIds.includes(Number(v.id)))

  // 按课程ID过滤
  if (videoDialog.value.selectedCourseId) {
    list = list.filter(video => Number(video.course_base_id) === Number(videoDialog.value.selectedCourseId))
  }

  return list
})
onMounted(async () => {
  await nextTick(async () => {
    await reloadData()
    getKc()
  });
});
const delData = (id: number) => {
  let t_row = course_ls_local.value.find(f => f.id === id)
  if (t_row) {
    if (t_row.is_pub === true) {
      msgShow('未发布的课程才能删除', 'warning')
    } else {
      let t_msg = `<div class="danger f_b">您确定要删除以下课程吗？</p><p>课程代码：${t_row.kc_bm}</p><p>课程名称：${t_row.kc_mc}</p><p>版本号：${t_row.ver}</p></div>`
      ElMessageBox.confirm(t_msg, '', {
        confirmButtonText: '确定删除',
        cancelButtonText: '再考虑一下',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(() => {
        DelCourseInfo(id).then(res => {
          if (res.code === 200) {
            msgShow(res.msg, 'success')
          } else {
            msgShow(res.msg, 'warning')
          }
        }).catch(e => {
          msgShow(e.toString(), 'error')
        }).finally(() => {
          reloadData()
        })
      }).catch(() => {

      });
    }
  }
}
const addData = (course_base_id: number) => {
  AddCourseInfo(course_base_id).then(res => {
    if (res.code === 200) {
      msgShow(res.msg, 'success')
    } else {
      msgShow(res.msg, 'warning')
    }
  }).catch(e => {
    msgShow(e.toString(), 'error')
  }).finally(() => {
    reloadData()
  })
}
const updData = (id: number) => {
  let t_row = course_ls_local.value.find(f => f.id === id)
  if (t_row) {
    UpdCourseInfo(t_row.id, parseInt(t_row.is_online.toString()), t_row.is_pub ? 1 : 0, t_row.video_ids).then(res => {
      if (res.code === 200) {
        msgShow(res.msg, 'success')
      } else {
        msgShow(res.msg, 'warning')
      }
    }).catch(e => {
      msgShow(e.toString(), 'error')
    }).finally(() => {
      reloadData()
    })
  }
}
const searchData = () => {
  course_ls_local.value = course_ls_svr.value.filter(f => {
    const { key_search, is_online, is_pub } = init_data.value;
    return (
      f.kc_bm.includes(key_search) || f.kc_mc.includes(key_search)
    ) && (
        is_online.includes(f.is_online.toString())
      ) && (
        is_pub.includes(f.is_pub ? '1' : '0')
      );
  });
}
const reloadData = async () => {
  await getCourseInfoData()
}
const getCourseInfoData = async () => {
  tableloading.value = true
  await CourseInfo().then(res => {
    if (res.code === 200) {
      course_ls_svr.value = res.data.filter((item: any) => {
        
        return learn_type_key.value ? item.bm_learn_type_id == learn_type_key.value : true
      })
      searchData()
    } else {
      msgShow(res.msg, 'warning')
    }
  }).catch(err => {
    msgShow(err.toString(), 'error')
  }).finally(() => {
    tableloading.value = false
  })
}
const tableCellStyle = ({
  row = { is_online: '0', is_pub: 0 },
  column = { property: '' },
  rowIndex = 0,
  columnIndex = 0
}) => {

  // if (column.property === 'cj_hg_per') {
  //   return {'background-color': '#b8ebf3'}
  // }
  // if (row.is_online === 1) {
  //   return {'background-color': '#eeea07'}
  // }
}
const tableRowStyle = ({ row = { is_online: '0', is_pub: 0 } }) => {
  if (row.is_pub && row.is_online === '1') {
    return { 'background-color': 'rgba(144, 238, 144, 0.2)' }
  }
}
// 处理拖拽结束
const onDragEnd = () => {
  // 更新选中状态
  handleRelatedVideoSelectionChange()
}
// 处理已关联视频选择变化
const handleRelatedVideoSelectionChange = () => {
  videoDialog.value.selectedRelatedVideos = videoDialog.value.relatedVideos.filter(v => v.selected)
}
// 处理课程选择变化
const handleCourseChange = () => {
  videoDialog.value.selectedVideos = []
}
// 处理添加视频
const handleAddVideos = async (row: I_course_info) => {
  videoDialog.value.courseId = row.id
  videoDialog.value.title = `关联视频 - ${row.kc_mc}`
  videoDialog.value.visible = true
  videoDialog.value.relatedVideos = []
  videoDialog.value.selectedCourseId = null
  videoDialog.value.is_online = parseInt(row.is_online.toString())
  videoDialog.value.is_pub = row.is_pub

  try {
    await getCourseListData()
    const params = {
      course_id: null
    };
    const res = await getVideoList(params)
    if (Number(res.code) === 200) {
      videoDialog.value.allVideos = res.data
    }
  } catch (error) {
    msgShow('获取视频列表失败', 'error')
  }
}
// 处理查看视频
const handleViewVideos = async (row: I_course_info) => {
  videoDialog.value.courseId = row.id
  videoDialog.value.title = `查看视频 - ${row.kc_mc}`
  videoDialog.value.visible = true
  videoDialog.value.searchKeyword = ''
  videoDialog.value.is_online = parseInt(row.is_online.toString())
  videoDialog.value.is_pub = row.is_pub

  try {
    const params = {
      query: ""
    };
    const res = await getVideoList(params)
    if (Number(res.code) === 200) {
      videoDialog.value.allVideos = res.data
      // 根据video_ids的顺序来排序已关联的视频
      if (row.video_ids && row.video_ids.length > 0) {
        const videoMap = new Map(res.data.map((video: any) => [video.id, video]))
        videoDialog.value.relatedVideos = row.video_ids
          .map(id => videoMap.get(Number(id)))
          .filter(Boolean)
      } else {
        videoDialog.value.relatedVideos = []
      }
    }
  } catch (error) {
    msgShow('获取视频列表失败', 'error')
  }
}
// 处理视频选择变化
const handleVideoSelectionChange = (selection: any[]) => {
  videoDialog.value.selectedVideos = selection
}
// 添加选中的视频
const handleAddSelectedVideos = () => {
  const newVideos = videoDialog.value.selectedVideos.filter(
    video => !videoDialog.value.relatedVideos.some(rv => Number(rv.id) === Number(video.id))
  )
  videoDialog.value.relatedVideos = [...videoDialog.value.relatedVideos, ...newVideos]
  videoDialog.value.selectedVideos = []
}
// 移除选中的视频
const handleRemoveSelectedVideos = () => {
  const selectedIds = videoDialog.value.selectedRelatedVideos.map(v => Number(v.id))
  videoDialog.value.relatedVideos = videoDialog.value.relatedVideos.filter(
    video => !selectedIds.includes(Number(video.id))
  )
  videoDialog.value.selectedRelatedVideos = []
}
// 保存视频关联
const handleSaveVideos = async () => {
  try {
    // 保存时使用当前排序后的顺序
    const videoIds = videoDialog.value.relatedVideos.map(v => v.id)
    const res = await UpdCourseInfo(
      videoDialog.value.courseId,
      videoDialog.value.is_online,
      videoDialog.value.is_pub ? 1 : 0,
      videoIds
    )
    if (res.code === 200) {
      msgShow('保存成功', 'success')
      videoDialog.value.visible = false
      reloadData()
    } else {
      msgShow(res.msg || '保存失败', 'error')
    }
  } catch (error) {
    msgShow('保存失败', 'error')
  }
}
let dragIndex = 0
function dragstart(e: any, index: any) {
  e.stopPropagation()
  dragIndex = index
  setTimeout(() => {
    e.target.classList.add('moveing')
  }, 0)
}
function dragenter(e: any, index: any) {
  e.preventDefault()
  // 拖拽到原位置时不触发

  if (dragIndex !== index) {
    const source = platedSort.value[dragIndex];
    platedSort.value.splice(dragIndex, 1);
    platedSort.value.splice(index, 0, source);
    // 更新节点位置
    dragIndex = index
  }
}
function dragover(e: any) {
  e.preventDefault()
  e.dataTransfer.dropEffect = 'move'
}
function dragend(e: any) {
  e.target.classList.remove('moveing')
}
const openPlateData = (row: any) => {
  selectRow.value = row
  getHavePlate(row.id)
  PlatedialogVisible.value = true
  // getPlate()
}
const getHavePlate = (id: any) => {
  getPlateDataForOlsCourseById({ id }).then((res: any) => {
    if (res.code == 200) {
      var have: any = []
      if (res.data) {
        const { allList, haveList } = res.data
        if (haveList) {
          haveList.forEach((e: any) => {
            have.push(e.plate_id)
          });
        }
        HavePlateData.value = have
        plateData.value = allList
        plateData.value.forEach((e: any) => {
          e['label'] = e['title']
          e['key'] = e['id']
        })
        PlageChange()
      } else {
        HavePlateData.value = []
      }

    } else {
      ElMessage.error(res.msg)
    }
  })
}
const PlageChange = () => {
  console.log(HavePlateData)
  platedSort.value = []
  if (HavePlateData.value.length > 0) {
    plateData.value.forEach((e: any) => {
      if (HavePlateData.value.indexOf(e.id) > -1) {
        e['is_list'] = e['is_list'] ? e['is_list'] : 0
        e['is_blank'] = e['is_blank'] ? e['is_blank'] : 0
        e['show_progress'] = e['show_progress'] ? e['show_progress'] : 0

        platedSort.value.push(e)
      }
    });
  }
}
const savePlate = () => {
  var plateIdArray: any = []
  platedSort.value.forEach((e: any, index: number) => {
    var obj = {
      plate_id: e.id,
      is_list: e.is_list,
      is_blank: e.is_blank,
      show_progress: e.show_progress,
      sn: index + 1
    }
    plateIdArray.push(obj)
  });
  if (plateIdArray.length == 0) {
    ElMessage.warning('请至少选择一个板块')
    return
  }
  var params = {
    id: selectRow.value.id,
    jsonstr: JSON.stringify(plateIdArray)
  }
  saveOlsCoursePlateByPlanid(params).then((res: any) => {
    if (res.code == "200") {
      ElMessage.success(res.msg)
      reloadData()
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const getKc = () => {
  getKcData({ page: 1, size: 90000 }).then((res: any) => {
    if (res.code == 200) {
      kcData.value = res.data.list
      kcData.value.forEach((e: any) => {
        e['kc_mcs'] = `(${e['kc_bm']})${e['kc_mc']}`
      });
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const openEditDialog = () => {
  EditdialogVisible.value = true
  getKc()
}
const saveCourseInfo = () => {
  if (formData.value.course_base_id === 0) {
    ElMessage.warning('请选择基础课程')
    return
  }
  if (formData.value.ver < 1 || formData.value.ver > 999) {
    ElMessage.warning('版本号必须在1到999之间')
    return
  }
  var selectKc = kcData.value.filter((item: any) => {
    return item.id === formData.value.course_base_id
  })
  var learn_type = selectKc.length > 0 ? selectKc[0].learn_type : 0
  formData.value['learn_type'] = learn_type
  saveOlsCourse(formData.value).then((res: any) => {
    if (res.code == 200 && res.data > 0) {
      ElMessage.success(res.msg)
      EditdialogVisible.value = false
      reloadData()
    } else {
      ElMessage.error(res.msg)
    }
  }).catch((err: any) => {
    ElMessage.error(err.toString())
  })
  console.log(learn_type)
}
const handleAddCourse = () => {
  formData.value = {
    course_base_id: "",
    ver: 1,
    is_online: '0',
  }
  openEditDialog()
}

// 指定资料
const assignResources = (row: I_course_info) => {
  assignResourceDialog.value.courseInfoId = row.id
  assignResourceDialog.value.courseBaseId = row.course_base_id
  assignResourceDialog.value.title = `指定资料 - ${row.kc_mc}`
  assignResourceDialog.value.visible = true
}

// 指定资料保存回调
const handleAssignResourceSave = () => {
  assignResourceDialog.value.visible = false
  msgShow('资料分配成功', 'success')
}
</script>

<style lang="scss" scoped>
.course_info {

  :deep .el-table--enable-row-hover .el-table__body tr:hover>td {
    background: transparent;
  }

  :deep .el-table__footer {
    font-weight: bold;
    height: 40px !important;

  }

  :deep .el-table__footer-wrapper tbody td.el-table__cell,
  .el-table__header-wrapper tbody td.el-table__cell {
    background-color: #bdd6f6;
  }

  :deep .el-table-fixed-column--left {
    //background:none!important;
  }

  .video-dialog-content {
    display: flex;
    gap: 20px;
    align-items: flex-start;

    .video-list {
      flex: 1;
      min-width: 0;

      .list-header {
        font-weight: bold;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
    }

    .video-actions {
      display: flex;
      flex-direction: column;
      gap: 10px;
      padding-top: 180px;
      min-width: 60px;
      align-items: center;
    }
  }

  .video-list {
    .el-table {
      border: 1px solid #EBEEF5;
      border-radius: 4px;

      .el-table__header {
        background-color: #f5f7fa;

        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: 500;
          text-align: center;
          padding: 12px 0;
          border-bottom: 1px solid #EBEEF5;
        }
      }

      .el-table__body {
        .el-table__row {
          display: flex;
          align-items: center;
          border-bottom: 1px solid #EBEEF5;

          &:last-child {
            border-bottom: none;
          }

          .el-table__cell {
            padding: 8px 0;
            border-right: 1px solid #EBEEF5;

            &:last-child {
              border-right: none;
            }
          }
        }
      }
    }
  }

  .ghost {
    opacity: 0.5;
    background: #c8ebfb;
  }

  .drag-handle {
    cursor: move;
    color: #909399;

    &:hover {
      color: #409EFF;
    }
  }
}

.item {
  width: 96%;
  height: auto;
  background-color: #f5f6f8;
  margin: 10px;
  box-sizing: border-box;
  gap: 10px;
  padding: 5px 15px;
}

.dragCurr:hover {
  cursor: move;
}
</style>
