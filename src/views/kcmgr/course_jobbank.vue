<template>
  <div style="padding: 5px;">
    <div>
      <el-button type="primary" @click="initData" icon="RefreshRight"></el-button>
      <el-button type="primary" @click="add()" plain icon="DocumentAdd">添加</el-button>
      <el-button type="danger" @click="delData()" plain icon="DeleteFilled">删除</el-button>
    </div>
    <el-table :tableHeight="tableHeight" :data="tableData" size="mini" stripe style="width: 100%;margin-top: 10px;"
      highlight-current-row @current-change="handleCurrentChange">
      <el-table-column type="index" width="50" align="center" />
      <el-table-column prop="kc_bm" label="课程编码" width="180" align="center" />
      <el-table-column prop="kc_mc" label="课程名称" width="180" align="center">
        <template #default="scope">
          <el-link @click="update(scope.$index)" type="primary">{{ scope.row.kc_mc }}&nbsp;&nbsp;<el-icon
              style="font-size: 13px;">
              <EditPen />
            </el-icon></el-link>
        </template>
      </el-table-column>
      <el-table-column prop="done_standard" label="完成标准(道题)" width="180" align="center"/>
      <el-table-column prop="ver" label="在线课程版本" align="center" />
      <el-table-column prop="plate_name" label="属于板块" align="center">
        <template #default="scope">
          <el-tag type="success">{{ scope.row.plate_name }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="question_source" label="题库来源" align="center">
        <template #default="scope">
          <el-popover v-if="scope.row.question_source" placement="right" :width="400" trigger="click">
            <template #reference>
              <el-button size="small" type="success">{{ scope.row.question_source_mc.length }}</el-button>
            </template>
            <div>

              <div style="text-align: right;width: 100%;;">
                <el-link @click="update(scope.$index)" type="primary">
                  <el-icon style="font-size: 13px;">
                    <EditPen />
                  </el-icon>编辑
                </el-link>
              </div>
              <div v-for="(item, index) in scope.row.question_source_mc">
                <div :key="index"
                  style="margin: 5px 0px;width: 100%;text-align: left;padding: 10px 5px;border-bottom: 1px solid #F2F6FC;display: flex;align-items: center;">
                  {{ index + 1 }}、 {{ item.title }} <svg t="1747969416047" class="icon" viewBox="0 0 1024 1024"
                    width="25" height="25">
                    <path
                      d="M67.89847538 487.58400192l582.47994502 0L650.37842039 541.5173303 67.89847538 541.5173303l1e-8-53.93332838z"
                      fill="" p-id="3388"></path>
                    <path
                      d="M647.09938542 326.19222268L960.7412301 510.39457617l-313.64184468 189.18079521 0-373.38314871z"
                      fill="" p-id="3389"></path>
                  </svg>【{{ item.ver }}】
                </div>
              </div>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="create_name" label="创建人" align="center"></el-table-column>
      <el-table-column prop="create_date" label="创建时间" align="center"></el-table-column>
    </el-table>
    <el-dialog v-model="dialogVisible" title="添加课程" width="720" top="6vh" :append-to-body="true"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <div style="height: auto;display: flex;justify-content: center;width: 100%;">

        <div style="padding: 10px 10px;">
          <el-form :inline="true" size="large" :model="formKcData" class="demo-form-inline" style="width: 650px;">
            <el-form-item label="选择课程">
              <el-select v-model="formKcData.course_info_id" class="m-2" placeholder="选择课程" style="width: 270px;"
                @change="infoChange">
                <el-option v-for="item in courseData" :key="item.id" :label="item.title" :value="item.id"
                  :disabled="item.disabled">
                </el-option>
              </el-select>
            </el-form-item>
            <br />
            <el-form-item label="选择板块">

              <el-select v-model="formKcData.plate_id" class="m-2" placeholder="选择板块" style="width: 130px;">
                <el-option v-for="item in plateData" :disabled="item.disabled" :key="item.id" :label="item.title"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item><br>
            <el-form-item label="题库来源">
              <div style="width: 550px;border:1px solid #fff;border-radius: 4px;">
                <div style="padding: 10px 0px;height: 300px;overflow-y: auto;">
                  <!--
                  <el-checkbox-group v-model="formKcData.question_source">
                    <div v-for="job in JobbankData">
                      <el-checkbox :key="job.key" :label="job.label" :value="job.key">
                        {{ job.label }}
                      </el-checkbox>

                    </div>
                  </el-checkbox-group>
                  -->
                  <el-cascader-panel v-model="formKcData.question_source" :props="props"
                    style="width: 100%;height: 100%;" @change="selectQuestion" :options="JobbankData" />
                </div>
              </div>
            </el-form-item>
            <el-form-item label="完成标准">
              <el-input v-model="formKcData.done_standard" type="Number" placeholder="请输入完成标准"
                style="width: 150px;"></el-input>
              <el-divider direction="vertical" />
              总题量：{{ selectQuestionNum }}
              <el-alert style="margin-top: 5px;" :closable="false"
                title="根据情况设置学生学习完成标准，如果是练习题可输入最低完成题目数，如果是电子书可以设置最低浏览时长按分钟" type="info" />
            </el-form-item>
          </el-form>
        </div>

      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveData()"
            :disabled="!formKcData.course_info_id || formKcData.question_source.length == 0">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang='ts'>
import { nextTick, onMounted, ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getCourseJobbank, getCoursePlate, getJobbankSourse, SaveCourseJobbank, deleteCourseJobbank } from "@/api/kcmgr";
import { CourseInfo, } from "@/api/course";
const tableData = ref([])
const singleTableRef: any = ref({})
const currentRow = ref()
const tableHeight = ref(window.innerHeight - 200)
const dialogVisible = ref(false)
const courseData: any = ref([])
const plateData: any = ref([])
const JobbankData: any = ref([])
const selectQuestionNum = ref(0)
const props = { multiple: true }
const formKcData: any = ref({
  id: 0,
  course_info_id: "",
  plate_id: "",
  question_source: []
})
onMounted(() => {
  initData()
})
const add = () => {
  dialogVisible.value = true
  formKcData.value = {
    id: 0,
    course_info_id: "",
    plate_id: "",
    question_source: []
  }
  getJobbank()
  getPlate()
  getCourseInfo()
}
const initData = () => {
  getCourseJobbank().then((res) => {
    console.log(res);
    tableData.value = res.data
  })
}
const getCourseInfo = () => {
  CourseInfo().then((res) => {
    console.log(res);
    var courseInfoData: any = []
    res.data.forEach((item: any) => {
      var index = -1
      if (tableData.value) {
        index = tableData.value.findIndex((e: any) => { return e.course_info_id == item['id'] })
      }


      var obj = {
        id: item['id'],
        title: `【${item['kc_bm']}】${item['kc_mc']} ：${item['ver']}`,
        disabled: false
      }
      courseInfoData.push(obj)


    });
    courseData.value = courseInfoData
  })
}
const getPlate = () => {
  getCoursePlate({ id: formKcData.value.course_info_id, type: 3 }).then((res) => {
    console.log(res);
    plateData.value = res.data
    plateData.value.forEach((e: any) => {
      var data = tableData.value.filter((item: any) => {
        return item.course_info_id == formKcData.value.course_info_id && item.plate_id == e.id
      })
      if (data.length > 0) {
        e['disabled'] = true
      }
    });
  })
}
const getJobbank = () => {
  getJobbankSourse({ id: formKcData.value.course_info_id }).then((res: any) => {
    console.log(res);
    var JobbankArray: any = []
    if (res.data) {

      res.data.forEach((e: any) => {
        e['label'] = `${e['title']}【题目数量：${e['question_num']}】`
        e['value'] = e['id']
        e['question_num'] = e['question_num']
        e['disabled'] = false
        var children: any = []
        e['children'].forEach((child: any) => {
          child['label'] = `${child['ver']}【${child['ver_num']}】`
          child['value'] = child['ver']
          child['question_num'] = child['ver_num']
          children.push(child)
        });
        e['children'] = children
        JobbankArray.push(e)
      });
      JobbankData.value = JobbankArray
    }
    setTimeout(() => {
      getJobbankNum()
    }, 0);
  })
}
const saveData = () => {

  console.log(formKcData.value)
  const result = formKcData.value['question_source'].map(([source_id, ver]: any) => ({
    source_id,
    ver
  }));

  formKcData.value['question_source'] = JSON.stringify(formKcData.value['question_source'])
  formKcData.value['question_source_b'] = JSON.stringify(result)
  SaveCourseJobbank(formKcData.value).then((res: any) => {
    console.log(res);
    if (res.code == 200) {
      initData()
      dialogVisible.value = false
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const update = (index: any) => {
  dialogVisible.value = true
  formKcData.value = tableData.value[index]
  formKcData.value['question_source'] = formKcData.value['question_source'] ? JSON.parse(formKcData.value['question_source']) : []
  getCourseInfo()
  getJobbank()
  getPlate()
}
const infoChange = () => {
  getJobbank()
  getPlate()
}
const handleCurrentChange = (val: any | undefined) => {
  currentRow.value = val
  console.log(`output->currentRow`, currentRow)
}
const delData = () => {
  if (currentRow.value) {
    ElMessageBox.confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      debugger
      deleteCourseJobbank({ id: currentRow.value.id }).then((res: any) => {
        if (res.code == 200) {
          ElMessage.success(res.msg)
          initData()
        } else {
          ElMessage.error(res.msg)
        }
      })
    }).catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除'
      });
    });
  } else {
    ElMessage.warning('请先选择要删除的课程')
  }
}
const selectQuestion = (e: any) => {
  console.log(e)
  formKcData.question_source = e
  getJobbankNum()

}
const getJobbankNum = () => {
  var allJonbank = 0
  
  JobbankData.value.forEach((e: any) => {
    e['children'].forEach((child: any) => {
      var data = formKcData.value.question_source.filter((item: any) => {
        if (item['1'] == child.ver) {
          console.log(item)
          return item
        }
      })
      if (data.length > 0) {
        allJonbank = allJonbank + child.question_num
      }
    });
  });

  selectQuestionNum.value = allJonbank
}
</script>

<style>
.el-cascader-menu__wrap.el-scrollbar__wrap {
  height: 100% !important;
}

.el-cascader-menu:last-child {
  flex: 1;
}
</style>