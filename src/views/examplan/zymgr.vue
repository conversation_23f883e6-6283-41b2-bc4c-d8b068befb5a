<template>
  <div class="examplan" style="padding: 5px;">
    <div>
      <el-button type="primary" @click="initData" icon="RefreshRight"></el-button>
      <el-button type="primary" @click="add()" plain icon="DocumentAdd">添加</el-button>
      <el-button type="danger" @click="delData()" plain icon="DeleteFilled">删除</el-button>
    </div>
    <el-table :tableHeight="tableHeight" :data="tableData" size="mini" stripe style="width: 100%;margin-top: 10px;"
      highlight-current-row @current-change="handleCurrentChange">
      <el-table-column type="index" width="50" align="center" />
      <el-table-column prop="learn_type" label="学习类型" align="center">
        <template #default="scope">
          <span v-if="scope.row.learn_type == 5">成人高考</span>
          <span v-else>自考</span>
        </template>
      </el-table-column>
      <el-table-column prop="zy_bm" label="专业编码" width="180" align="center"></el-table-column>
      <el-table-column prop="zy_mc" label="专业名称" width="180" align="center">
        <template #default="scope">
          <el-link @click="update(scope.$index)" type="primary">{{ scope.row.zy_mc }}&nbsp;&nbsp;<el-icon
              style="font-size: 13px;">
              <EditPen />
            </el-icon></el-link>
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.status == 1" type="success">正常</el-tag>
          <el-tag v-else type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="create_name" label="创建人" align="center"></el-table-column>
      <el-table-column prop="create_date" label="创建时间" align="center"></el-table-column>
    </el-table>
    <el-dialog v-model="dialogVisible" title="添加专业信息" width="600" top="6vh" :append-to-body="true"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <div style="height: auto;">
        <el-tabs tab-position="top" style="" class="demo-tabs">
          <el-tab-pane label="基本设置">
            <div style="padding: 10px 20px;">
              <el-form :model="formData" class="demo-form-inline">
                <el-form-item label="学习类型">
                  <el-select v-model="formData.learn_type" class="m-2" style="width: 170px;" placeholder="Select">
                    <el-option key="2" label="自考" value="2" />
                    <el-option key="1" label="成人高考" value="1" />
                  </el-select>
                </el-form-item>
                <el-form-item label="专业编码">
                  <el-input v-model="formData.zy_bm" placeholder="专业编码" clearable />
                </el-form-item>
                <el-form-item label="专业名称">
                  <el-input v-model="formData.zy_mc" placeholder="专业名称" clearable />
                </el-form-item>
                <el-form-item label="教材状态">
                  <el-radio-group v-model="formData.status">
                    <el-radio label="1" border>启用</el-radio>
                    <el-radio label="0" border>禁用</el-radio>
                  </el-radio-group>
                </el-form-item>
               
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveData()"
            :disabled="!formData.zy_bm || !formData.zy_mc">保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
  
<script setup lang='ts'>
  import { defineComponent, onMounted, reactive, ref, toRefs } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getZyData, zySaveData, deleteZyData } from '@/api/exam'
import type { UploadProps } from 'element-plus'
const tableData = ref([])
const singleTableRef: any = ref({})
const currentRow = ref()
const tableHeight = ref(window.innerHeight - 200)
onMounted(() => {
  initData()
});
const formData: any = ref({
  id: "",
  zy_bm: '',
  zy_mc: '',
  learn_type:'',
  status: '0',
})
const value = ref("")
const dialogVisible = ref(false)
const initData = () => {
  getZyData({}).then((res: any) => {
    if (res.code == 200) {

      tableData.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const saveData = () => {
  console.log(`output->formKcData`, formData)
  formData.value['id'] = formData.value['id'] ? formData.value['id'] : 0
  zySaveData(formData.value).then((res: any) => {
    if (res.code == "200") {
      ElMessage.success(res.msg)
      dialogVisible.value = false
      initData()
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const add = () => {
  formData.value = {
    id: "",
    zy_bm: '',
    zy_mc: '',
    status: '0',
  }
  dialogVisible.value = true
}
const update = (index: number) => {
  console.log(`output->index`, index)
  formData.value = tableData.value[index]
  formData.value['status'] = formData.value['status'].toString()
  formData.value['learn_type']= formData.value['learn_type'].toString()
  dialogVisible.value = true
}
const handleCurrentChange = (val: any | undefined) => {
  currentRow.value = val
  console.log(`output->currentRow`, currentRow)
}
const delData = () => {
  debugger
  if (currentRow.value) {
    ElMessageBox.confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deleteZyData(currentRow.value.id).then((res: any) => {
        if (res.code == 200) {
          ElMessage.success(res.msg)
          initData()
        } else {
          ElMessage.error(res.msg)
        }
      })
    }).catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除'
      });
    });
  } else {
    ElMessage.warning('请先选择要删除的课程')
  }
}
const handleAvatarSuccess: UploadProps['onSuccess'] = (
  response,
  uploadFile
) => {
  if(response.code==200){
    ElMessage.success(response.msg)
    formData.value.cover = response.data
  }else{
    ElMessage.error(response.msg)
  }
}
</script>
  
<style>
  .el-dialog__body {
  padding: 0px !important;
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 108px;
  height: 108px;
  text-align: center;
}

.v-note-wrapper .v-note-panel .v-note-edit.divarea-wrapper.single-edit {
  height: 410px;

}

.v-note-wrapper .v-note-panel .v-note-edit.divarea-wrapper.transition {
  height: 410px;
}

.v-note-panel {
  height: 410px;
}
</style>