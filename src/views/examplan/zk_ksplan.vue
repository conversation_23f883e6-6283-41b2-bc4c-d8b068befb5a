<template>
  <div class="examplan" style="padding: 5px;">
    <div>
      <el-button type="primary" @click="initData" icon="RefreshRight"></el-button>
      <el-button type="primary" @click="add()" plain icon="DocumentAdd">添加</el-button>
      <el-button type="danger" @click="delData()" plain icon="DeleteFilled">删除</el-button>

      <el-select v-model="filterZy" class="m-2" placeholder="请选择专业" style="width: 190px;margin-left: 5px;"
        @change="initData()">
        <el-option v-for="item in zyData" :key="item.zy_bm" :label="item.zy_mcs" :value="item.zy_bm">
        </el-option>
      </el-select>
    </div>
    <el-table :tableHeight="tableHeight" :data="tableData" size="mini" stripe style="width: 100%;margin-top: 10px;"
      highlight-current-row @current-change="handleCurrentChange">
      <el-table-column type="index" width="50" align="center" />
      <el-table-column prop="zy_bm" label="专业编码" align="center"></el-table-column>
      <el-table-column prop="zy_mc" label="专业名称" align="center"></el-table-column>
      <el-table-column prop="kc_bm" label="课程编码" align="center"></el-table-column>
      <el-table-column prop="kc_mc" label="课程名称" align="center">
        <template #default="scope">
          <el-link @click="update(scope.$index)" type="primary">{{ scope.row.kc_mc }}&nbsp;&nbsp;<el-icon
              style="font-size: 13px;">
              <EditPen />
            </el-icon></el-link>
        </template>
      </el-table-column>
      <el-table-column prop="ks_lx" label="考试类型" align="center"></el-table-column>
      <el-table-column prop="xf" label="学分" align="center"></el-table-column>
      <el-table-column prop="ks_sj" label="考试时间（月）" align="center"></el-table-column>

      <el-table-column prop="status" label="状态" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.status == 1" type="success">正常</el-tag>
          <el-tag v-else type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="kc_mc" label="板块" align="center">
        <template #default="scope">
          <el-link @click="openPlateData(scope.row)" type="primary">指定板块</el-link>
          <el-badge :value="scope.row.platenum">
            
          </el-badge>

        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" align="center"></el-table-column>
      <el-table-column prop="create_name" label="创建人" align="center"></el-table-column>
      <el-table-column prop="create_date" label="创建时间" align="center"></el-table-column>
    </el-table>
    <el-dialog v-model="dialogVisible" title="添加专业信息" width="750" top="6vh" :append-to-body="true"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <div style="">
        <el-tabs tab-position="top" style="" class="demo-tabs">
          <el-tab-pane label="基本设置">
            <div style="padding: 10px 20px;">
              <el-form :inline="true" :model="formData" class="demo-form-inline" label-position="right"
                label-width="110px">
                <el-divider content-position="left">专业设置</el-divider>
                <el-form-item label="专业编码">
                  <el-select v-model="formData.bm_zy_id" class="m-2" placeholder="专业编码" style="width: 190px;"
                    @change="selectZy()">
                    <el-option v-for="item in zyData" :key="item.id" :label="item.zy_mcs" :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-divider content-position="left">课程设置</el-divider>
                <el-form-item label="选择课程">
                  <el-select v-model="formData.course_base_id" class="m-2" placeholder="选择课程" style="width: 190px;"
                    @change="selectKc">
                    <el-option v-for="item in kcData" :key="item.id" :disabled="item.disabled" :label="item.kc_mcs"
                      :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="在线课程版本">
                  <el-select v-model="formData.course_info_id" class="m-2" placeholder="在线课程" style="width: 190px;">
                    <el-option v-for="item in courseData" :key="item.id" :label="item.mc" :value="item.id">
                      <span>版本：{{ item.ver }}【<span style="color: green;">{{ (item.is_online = 1 ? '在线课程' :
                        item.is_online =
                        0 ? '非在线课程' : '未指定') }}</span>】
                        【<span style="color: green;">{{ item.is_pub == 1 ? '已发布' : '未发布' }}</span>】</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="课程编码">
                  <el-input v-model="formData.kc_bm" :disabled="true" placeholder="请输入课程名称" style="width: 190px;" />
                </el-form-item>
                <el-form-item label="课程名称">
                  <el-input v-model="formData.kc_mc" :disabled="true" placeholder="请输入课程名称" style="width: 190px;" />
                </el-form-item>
                <el-divider content-position="left">课程其他属性</el-divider>
                <el-form-item label="考试类型">
                  <el-select v-model="formData.ks_lx" class="m-2" placeholder="考试类型" style="width: 190px;">
                    <el-option key="省考" label="省考" value="省考"></el-option>
                    <el-option key="统考" label="统考" value="统考"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="学分">
                  <el-input-number v-model="formData.xf" style="width: 190px;" />
                </el-form-item>
                <el-form-item label="考试月份">
                  <el-select v-model="formData.ks_sj" style="width: 190px;" multiple collapse-tags collapse-tags-tooltip
                    placeholder="考试月份">
                    <el-option v-for="item in ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']"
                      :key="item" :label="item + '月'" :value="item" />
                  </el-select>
                </el-form-item>
                <el-form-item label="状态">
                  <el-radio-group v-model="formData.status">
                    <el-radio label="1">启用</el-radio>
                    <el-radio label="0">禁用</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="英语二替考课程">
                  <el-radio-group v-model="formData.is_yy2_tk">
                    <el-radio label="1">启用</el-radio>
                    <el-radio label="0">禁用</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="备注">
                  <el-input v-model="formData.remark" :rows="2" type="textarea" style="width: 500px;" />
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveData()" :disabled="!formData.zy_bm || !formData.kc_bm">保存
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="PlatedialogVisible" width="700" :close-on-click-modal="false" :close-on-press-escape="false"
      :append-to-body="true" top="6vh">
      <div class="m-2" style="display: flex;justify-content: center;">

        <el-tabs tab-position="top" style="" class="demo-tabs">
          <el-tab-pane label="板块设置">
            <div style="padding: 10px 20px;">
              <div style="padding: 10px 0px;"> 为【<span style="color: #409EFF;font-weight: bold;">{{ selectRow.zy_mc
                  }}</span>】专业的【<span style="color: #409EFF;font-weight: bold;">{{ selectRow.kc_mc }}</span>】课程指定板块
              </div>
              <el-transfer v-model="HavePlateData" :data="plateData" :titles="['未开通板块', '已开通板块']"
                @click="PlageChange()" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="板块详细设置">
            <div style="padding: 0px 20px;height: 380px;width: 610px;overflow-y: auto;">
              <div class="item" v-for="(item, i) in platedSort" :key="item.id" draggable="true"
                @dragstart="dragstart($event, i)" @dragenter="dragenter($event, i)" @dragend="dragend"
                @dragover="dragover">
                <div style="width: 100%;display: flex;align-items: center;">
                  <div style="margin-top: 4px;" class="dragCurr">
                    <svg viewBox="0 0 1024 1024" width="30" height="30">
                      <path
                        d="M512 0a512 512 0 0 1 512 512 512 512 0 0 1-512 512A512 512 0 0 1 0 512 512 512 0 0 1 512 0z"
                        fill="#FFFFFF" p-id="7425"></path>
                      <path
                        d="M509.5424 720.6912L593.92 636.5184l35.2256 35.2256-119.1936 118.784-118.784-118.784 35.2256-35.2256zM509.952 245.76l118.784 118.784-34.816 35.2256-83.7632-84.1728-84.1728 84.1728L389.12 364.544l119.808-118.784zM307.2 482.304v-49.7664h409.6v49.7664z m0 112.8448v-49.7664h409.6v49.7664z"
                        fill="#2693FF" p-id="7426"></path>
                    </svg>
                  </div>
                  <div style="margin-left: 15px;">
                    【{{ i + 1 }}】 {{ item.title }}
                  </div>
                </div>
                <div style="margin-left: 40px;margin-top: 1px;" class="cj_ksplan-item">
                  <el-select v-model="item.is_list" placeholder="展示方式" size="mini" style="width: 100px">
                    <el-option :key="1" label="有列表" :value="1" />
                    <el-option :key="0" label="无列表" :value="0" />
                  </el-select>
                  <el-select v-model="item.is_blank" placeholder="打开方式" size="mini"
                    style="width: 150px;margin-left: 15px;">
                    <el-option :key="1" label="新标签页打开" :value="1" />
                    <el-option :key="0" label="当前页打开" :value="0" />
                  </el-select>
                  <el-select v-model="item.show_progress" placeholder="显示进度条" size="mini"
                    style="width: 150px;margin-left: 15px;">
                    <el-option :key="1" label="显示进度条" :value="1" />
                    <el-option :key="0" label="不显示进度条" :value="0" />
                  </el-select>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>



      </div>
      <div class="dialog-footer" style="padding: 10px 20px;text-align: center;border-top: 1px solid #F2F6FC;">
        <el-button @click="PlatedialogVisible = false">取消</el-button>
        <el-button type="primary" @click="savePlate()">保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang='ts'>
import { defineComponent, onMounted, reactive, ref, toRefs } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getPlanData, getZyData, zySaveData, deletePlanData, getKcData, planSaveData
  , getPlateData, getPlateDataByPlanid, savePlateByPlanid, getOnlineCourse
} from '@/api/exam'
import type { UploadProps } from 'element-plus'
import Item from "@/layout/components/Sidebar/Item.vue";
import { id } from "element-plus/es/locale";
const tableData = ref([])
const singleTableRef: any = ref({})
const currentRow = ref()
const tableHeight = ref(window.innerHeight - 200)
onMounted(() => {
  initData()
  getZy()
});
const formData: any = ref({
  id: "",
  bm_zy_id: '',
  zy_bm: '',
  zy_mc: '',
  status: '1',
  course_base_id: "",
  course_info_id: ""
})
const filterZy = ref("")
const value = ref("")
const dialogVisible = ref(false)
const zyData: any = ref([])
const kcData: any = ref([])
const PlatedialogVisible = ref(false)
const plateData = ref([])
const HavePlateData: any = ref([])
const selectRow: any = ref({})
const courseData: any = ref([])
const platedSort: any = ref([])
const initData = () => {
  getPlanData({ filterZy: filterZy.value }).then((res: any) => {
    if (res.code == 200) {
      tableData.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const saveData = () => {
  console.log(`output->formKcData`, formData)
  formData.value['id'] = formData.value['id'] ? formData.value['id'] : 0
  formData.value['zy_mc'] = zyData.value.find((item: any) => item.zy_bm == formData.value.zy_bm).zy_mc
  formData.value['kc_mc'] = kcData.value.find((item: any) => item.kc_bm == formData.value.kc_bm).kc_mc
  formData.value['ks_sj'] = formData.value['ks_sj'].join(',')
  planSaveData(formData.value).then((res: any) => {
    if (res.code == "200") {
      ElMessage.success(res.msg)
      dialogVisible.value = false

      initData()
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const add = () => {
  formData.value = {
    id: "",
    bm_zy_id: "",
    zy_bm: '',
    zy_mc: '',
    ks_lx: '',
    kc_bm: '',
    kc_mc: "",
    ks_sj: "",
    remark: "",
    xf: 0,
    is_yy2_tk: '0',
    status: '1',
    course_base_id: "",
    course_info_id: ""
  }
  getZy()
  getKc()
  dialogVisible.value = true
}
const update = (index: number) => {
  console.log(`output->index`, index)
  formData.value = tableData.value[index]
  formData.value['status'] = formData.value['status'].toString()
  formData.value['is_yy2_tk'] = formData.value['is_yy2_tk'].toString()
  formData.value['ks_sj'] = formData.value['ks_sj'].split(',')
  dialogVisible.value = true
  getZy()
  getKc()
}
const handleCurrentChange = (val: any | undefined) => {
  currentRow.value = val
  console.log(`output->currentRow`, currentRow)
}
const delData = () => {
  if (currentRow.value) {
    ElMessageBox.confirm('此操作将永久删除该计划, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deletePlanData(currentRow.value.id).then((res: any) => {
        if (res.code == 200) {
          ElMessage.success(res.msg)
          initData()
        } else {
          ElMessage.error(res.msg)
        }
      })
    }).catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除'
      });
    });
  } else {
    ElMessage.warning('请先选择要删除的课程')
  }
}
const handleAvatarSuccess: UploadProps['onSuccess'] = (
  response,
  uploadFile
) => {
  if (response.code == 200) {
    ElMessage.success(response.msg)
    formData.value.cover = response.data
  } else {
    ElMessage.error(response.msg)
  }
}
const getZy = () => {
  getZyData({}).then((res: any) => {
    if (res.code == 200) {
      zyData.value = res.data
      zyData.value.forEach((e: any) => {
        e['zy_mcs'] = `(${e['zy_bm']})${e['zy_mc']}`
      });
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const getKc = () => {
  getKcData({}).then((res: any) => {
    if (res.code == 200) {
      kcData.value = res.data.filter((x: any) => (x.learn_type == 2))
      var havaKc = tableData.value.filter((x: any) => {
        if ((x.bm_zy_id == formData.value.bm_zy_id)) {
          return x.course_base_id
        }
      })
      console.log(`output->havaKc`, havaKc)
      kcData.value.forEach((e: any) => {
        e['kc_mcs'] = `(${e['kc_bm']})${e['kc_mc']}`
        if (havaKc.length > 0) {
          havaKc.forEach((item: any) => {
            if (item.course_base_id == e.id) {
              e['disabled'] = true
            }
          });
        } else {
          e['disabled'] = false
        }
      });
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const openPlateData = (row: any) => {
  selectRow.value = row
  getHavePlate(row.id)
  PlatedialogVisible.value = true
  // getPlate()
}
const getPlate = () => {
  getPlateData({}).then((res: any) => {
    if (res.code == 200) {
      var plateArray: any = []
      var obj = res.data.forEach((e: any) => {
        e['label'] = e['title']
        e['key'] = e['id']
        e['disabled'] = false
        plateArray.push(e)
      });
      plateData.value = plateArray
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const getHavePlate = (id: any) => {
  getPlateDataByPlanid(id).then((res: any) => {
    if (res.code == 200) {
      var have: any = []
      if (res.data) {
        const { allList, haveList } = res.data
        if (haveList) {
          haveList.forEach((e: any) => {
            have.push(e.plate_id)
          });
        }
        HavePlateData.value = have
        plateData.value = allList
        plateData.value.forEach((e: any) => {
          e['label'] = e['title']
          e['key'] = e['id']
        })
        PlageChange()
      } else {
        HavePlateData.value = []
      }

    } else {
      ElMessage.error(res.msg)
    }
  })
}
const savePlate = () => {
  var plateIdArray: any = []
  platedSort.value.forEach((e: any, index: number) => {
    var obj = {
      plate_id: e.id,
      is_list: e.is_list,
      is_blank: e.is_blank,
      show_progress: e.show_progress,
      sn: index + 1
    }
    plateIdArray.push(obj)
  });
  if (plateIdArray.length == 0) {
    ElMessage.warning('请至少选择一个板块')
    return
  }
  var params = {
    id: selectRow.value.id,
    jsonstr: JSON.stringify(plateIdArray)
  }
  savePlateByPlanid(params).then((res: any) => {
    if (res.code == "200") {
      ElMessage.success(res.msg)
      initData()
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const getOnlineCourseData = () => {
  getOnlineCourse({ id: formData.value.course_base_id }).then((res: any) => {
    if (res.code == 200) {
      courseData.value = res.data
      courseData.value.forEach((e: any) => {
        e['mc'] = '版本：' + e.ver + '【' + (e.is_online = 1 ? '在线课程' : e.is_online = 0 ? '非在线课程' : '未指定') + '】' + '【' + (e.is_pub == 1 ? '已发布' : '未发布') + '】'
      });
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const selectKc = (val: any) => {
  var kc = kcData.value.find((x: any) => x.id == val)
  formData.value.kc_mc = kc.kc_mc
  formData.value.kc_bm = kc.kc_bm
  getOnlineCourseData()
}
const selectZy = () => {
  var data = zyData.value.find((item: any) => item.id == formData.value.bm_zy_id)
  formData.value.zy_bm = data.zy_bm
  formData.value.zy_mc = data.zy_mc
  getKc()
}

const PlageChange = () => {
  console.log(HavePlateData)
  platedSort.value = []
  if (HavePlateData.value.length > 0) {
    plateData.value.forEach((e: any) => {
      if (HavePlateData.value.indexOf(e.id) > -1) {
        e['is_list'] = e['is_list'] ? e['is_list'] : 0
        e['is_blank'] = e['is_blank'] ? e['is_blank'] : 0
        e['show_progress'] = e['show_progress'] ? e['show_progress'] : 0

        platedSort.value.push(e)
      }
    });
  }
}
let dragIndex = 0
function dragstart(e: any, index: any) {
  e.stopPropagation()
  dragIndex = index
  setTimeout(() => {
    e.target.classList.add('moveing')
  }, 0)
}
function dragenter(e: any, index: any) {
  e.preventDefault()
  // 拖拽到原位置时不触发

  if (dragIndex !== index) {
    const source = platedSort.value[dragIndex];
    platedSort.value.splice(dragIndex, 1);
    platedSort.value.splice(index, 0, source);
    // 更新节点位置
    dragIndex = index
  }
}
function dragover(e: any) {
  e.preventDefault()
  e.dataTransfer.dropEffect = 'move'
}
function dragend(e: any) {
  e.target.classList.remove('moveing')
}
</script>

<style>
.examplan .el-form-item {
  margin-bottom: 10px !important;
}

.el-dialog__body {
  padding: 0px !important;
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 108px;
  height: 108px;
  text-align: center;
}

.v-note-wrapper .v-note-panel .v-note-edit.divarea-wrapper.single-edit {
  height: 410px;

}

.v-note-wrapper .v-note-panel .v-note-edit.divarea-wrapper.transition {
  height: 410px;
}

.v-note-panel {
  height: 410px;
}

.item {
  width: 96%;
  height: auto;
  background-color: #f5f6f8;
  margin: 10px;
  box-sizing: border-box;
  gap: 10px;
  padding: 5px 15px;
}
</style>