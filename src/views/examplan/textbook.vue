<template>
  <div class="kcmgr" style="padding: 5px;">
    <div>
      <el-button type="primary" @click="initData" icon="RefreshRight"></el-button>
      <el-button type="primary" @click="add()" plain icon="DocumentAdd">添加</el-button>
      <el-button type="danger" @click="delData()" plain icon="DeleteFilled">删除</el-button>
    </div>
    <el-table :tableHeight="tableHeight" :data="tableData" size="mini" stripe style="width: 100%;margin-top: 10px;"
      highlight-current-row @current-change="handleCurrentChange">
      <el-table-column type="expand">
        <template #default="props">
          <div style="padding: 5px 50px!important;">
            <H3>课程简介</H3>
          </div>
          <div style="padding: 5px 50px!important;;">
            <mavon-editor :subfield="false" defaultOpen="preview" :editable="false" :toolbarsFlag="false"
              :boxShadow="false" :xssOptions="{}" :html="false" v-model="props.row.introduction" />
          </div>
        </template>
      </el-table-column>
      <el-table-column type="index" width="50" align="center" />
      <el-table-column prop="title" label="教材名称" width="180" align="center">
        <template #default="scope">
          <el-link @click="update(scope.$index)" type="primary">{{ scope.row.title }}&nbsp;&nbsp;<el-icon
              style="font-size: 13px;">
              <EditPen />
            </el-icon></el-link>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="教材类型" width="180" align="center">

      </el-table-column>
      <el-table-column prop="teacher" label="教师" align="center" />
      <el-table-column prop="version" label="版本" align="center" />
      <el-table-column prop="status" label="状态" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.status == 1" type="success">正常</el-tag>
          <el-tag v-else type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="create_name" label="创建人" align="center"></el-table-column>
      <el-table-column prop="create_date" label="创建时间" align="center"></el-table-column>
    </el-table>
    <el-dialog v-model="dialogVisible" title="添加教材信息" width="800" top="6vh" :append-to-body="true"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <div style="height: 500px;">
        <el-tabs tab-position="left" style="height: 500px" class="demo-tabs">
          <el-tab-pane label="基本设置">
            <div style="padding: 10px 20px;">
              <el-form :inline="true" :model="formData" class="demo-form-inline">
                <el-form-item label="教材名称">
                  <el-input v-model="formData.title" placeholder="教材名称" clearable />
                </el-form-item>
                <el-form-item label="类型">
                  <el-select v-model="formData.type" style="width: 150px;" placeholder="Activity zone" clearable>
                    <el-option label="电子教材" value="电子教材" />
                    <el-option label="实体教材" value="实体教材" />
                  </el-select>
                </el-form-item>
                <el-form-item label="编写教师">
                  <el-input v-model="formData.teacher" placeholder="编写教师" clearable />
                </el-form-item>
                <el-form-item label="版本">
                  <el-input v-model="formData.version" placeholder="版本" clearable />
                </el-form-item>
                <el-form-item label="教材状态">
                  <el-radio-group v-model="formData.status">
                    <el-radio label="1" border>启用</el-radio>
                    <el-radio label="0" border>禁用</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="封面" style="align-items: center;font-weight: bold;">
                  <el-upload class="avatar-uploader"
                    action="https://xczx7.swufe.edu.cn/oc/xczk/examplan/upload" :show-file-list="false" :on-success="handleAvatarSuccess">
                    <img style="width: 108px;height: 108px;" v-if="formData.cover" :src="'https://xczx7.swufe.edu.cn/oc/xczk/examplan/getFile?path='+formData.cover" class="avatar" />
                    <el-icon v-else class="avatar-uploader-icon">
                      <Plus />
                    </el-icon>
                  </el-upload>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
          <el-tab-pane label="课程简介">
            <div style="height: 500px;">
              <mavon-editor :boxShadow="false" :xssOptions="{}" :html="false" v-model="formData.introduction" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveData()"
            :disabled="!formData.title || !formData.type || !formData.version">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>

</template>

<script setup lang='ts'>
import { defineComponent, onMounted, reactive, ref, toRefs } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getTextBookData, textBookSaveData, deletetextBookData } from '@/api/exam'
import type { UploadProps } from 'element-plus'
const tableData = ref([])
const singleTableRef: any = ref({})
const currentRow = ref()
const tableHeight = ref(window.innerHeight - 200)
onMounted(() => {
  initData()

});
const formData: any = ref({
  id: "",
  title: '',
  type: '',
  teacher: '',
  status: '0',
  version: '',
  cover: '',
  introduction: ''
})
const value = ref("")
const dialogVisible = ref(false)
const initData = () => {
  getTextBookData({}).then((res: any) => {
    if (res.code == 200) {

      tableData.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const saveData = () => {
  console.log(`output->formKcData`, formData)
  formData.value['id'] = formData.value['id'] ? formData.value['id'] : 0
  textBookSaveData(formData.value).then((res: any) => {
    if (res.code == "200") {
      ElMessage.success(res.msg)
      dialogVisible.value = false
      initData()
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const add = () => {
  formData.value = {
    id: "",
    title: '',
    type: '',
    teacher: '',
    status: '0',
    version: '',
    cover: '',
    introduction: ''
  }
  dialogVisible.value = true
}
const update = (index: number) => {
  console.log(`output->index`, index)
  formData.value = tableData.value[index]
  formData.value['status'] = formData.value['status'].toString()
  dialogVisible.value = true
}
const handleCurrentChange = (val: any | undefined) => {
  currentRow.value = val
  console.log(`output->currentRow`, currentRow)
}
const delData = () => {
  debugger
  if (currentRow.value) {
    ElMessageBox.confirm('此操作将永久删除该教材, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deletetextBookData(currentRow.value.id).then((res: any) => {
        if (res.code == 200) {
          ElMessage.success(res.msg)
          initData()
        } else {
          ElMessage.error(res.msg)
        }
      })
    }).catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除'
      });
    });
  } else {
    ElMessage.warning('请先选择要删除的课程')
  }
}
const handleAvatarSuccess: UploadProps['onSuccess'] = (
  response,
  uploadFile
) => {
  if(response.code==200){
    ElMessage.success(response.msg)
    formData.value.cover = response.data
  }else{
    ElMessage.error(response.msg)
  }
}
</script>

<style>
.el-dialog__body {
  padding: 0px !important;
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 108px;
  height: 108px;
  text-align: center;
}

.v-note-wrapper .v-note-panel .v-note-edit.divarea-wrapper.single-edit {
  height: 410px;

}

.v-note-wrapper .v-note-panel .v-note-edit.divarea-wrapper.transition {
  height: 410px;
}

.v-note-panel {
  height: 410px;
}
</style>