<template>
  <div class="kcmgr" style="padding: 5px;">
    <div>
      <el-button type="primary" @click="initData" icon="RefreshRight"></el-button>
      <el-button type="primary" @click="add()" plain icon="DocumentAdd">添加</el-button>
      <el-button type="danger" @click="delData()" plain icon="DeleteFilled">删除</el-button>
    </div>
    <el-table :tableHeight="tableHeight" :data="tableData" size="mini" stripe style="width: 100%;margin-top: 10px;"
      highlight-current-row @current-change="handleCurrentChange">
      <el-table-column type="index" width="50" label="#" align="center" />
      <el-table-column prop="title" label="板块名称" width="180" align="center">
        <template #default="scope">
          <el-link @click="update(scope.$index)" type="primary">{{ scope.row.title }}&nbsp;&nbsp;<el-icon
              style="font-size: 13px;">
              <EditPen />
            </el-icon></el-link>
        </template>
      </el-table-column>
      <el-table-column prop="name_type" label="类型" width="180" align="center">
      </el-table-column>
      <el-table-column prop="icon" label="图标" align="center">
        <template #default="scope">
          <font-awesome-icon :icon="'fa-solid ' + scope.row.icon" />
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.status == 1" type="success">正常</el-tag>
          <el-tag v-else type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="create_name" label="创建人" align="center"></el-table-column>
      <el-table-column prop="create_date" label="创建时间" align="center"></el-table-column>
    </el-table>
    <el-dialog v-model="dialogVisible" title="添加板块" width="800" top="6vh" :append-to-body="true"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <div style="height: auto;">
        <el-tabs tab-position="left" style="height: auto" class="demo-tabs">
          <el-tab-pane label="基本设置">
            <div style="padding: 10px 20px;">
              <el-form size="large" label-position="right" :model="formKcData" class="demo-form-inline">
                <el-form-item label="板块图标">
                  <el-input size="large" v-model="formKcData.icon" style="width: 200px;" placeholder="图标" clearable>
                  </el-input>

                  <span style="margin-left: 10px;width: 50px;">
                    <font-awesome-icon :icon="'fa-solid ' + formKcData.icon" />
                  </span>
                  <el-alert title="更多图标参详：https://fontawesome.com/v6/search" type="info" :closable="false"
                    style="width: 300px;margin-top: 10px;" />
                </el-form-item>

                <p></p>
                <el-form-item label="板块名称">
                  <el-input size="large" v-model="formKcData.title" style="width: 200px;" placeholder="板块名称" clearable>
                  </el-input>
                </el-form-item>
                <el-form-item label="板块内容">
                  <el-select v-model="formKcData.type" class="m-2" placeholder="Select"  style="width: 200px;">
                    <el-option  key="2" label="视频" value="2" />
                    <el-option  key="1" label="教材" value="1" />
                    <el-option  key="3" label="题库" value="3" />
                    <el-option  key="4" label="笔记" value="4" />
                  </el-select>
                </el-form-item>
                <el-form-item label="板块状态">
                  <el-radio-group v-model="formKcData.status">
                    <el-radio label="1" size="large" border>启用</el-radio>
                    <el-radio label="0" size="large" border>禁用</el-radio>
                  </el-radio-group>
                </el-form-item>

              </el-form>
            </div>
          </el-tab-pane>
          <el-tab-pane label="简介">
            <div style="height: 500px;">
              <mavon-editor :boxShadow="false" :xssOptions="{}" :html="false" v-model="formKcData.remark" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveData()" :disabled="!formKcData.title">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>

</template>

<script setup lang='ts'>
import { defineComponent, onMounted, reactive, ref, toRefs } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getPlateData, plateSaveData, deletePlateData } from '@/api/exam'
import type { UploadProps } from 'element-plus'
const tableData = ref([])
const singleTableRef: any = ref({})
const currentRow = ref()
const tableHeight = ref(window.innerHeight - 200)
onMounted(() => {
  initData()

});
const formKcData: any = ref({
  id: "",
  icon: "",
  title: '',
  type:"",
  remark: "",
  status: '1',
})
const value = ref("")
const dialogVisible = ref(false)
const initData = () => {
  getPlateData({}).then((res: any) => {
    if (res.code == 200) {

      tableData.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const saveData = () => {
  console.log(`output->formKcData`, formKcData)
  formKcData.value['id'] = formKcData.value['id'] ? formKcData.value['id'] : 0
  plateSaveData(formKcData.value).then((res: any) => {
    if (res.code == "200") {
      ElMessage.success(res.msg)
      initData()
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const add = () => {
  formKcData.value = {
    id: "",
    icon: "",
    type:"",
    title: '',
    remark: "",
    status: '1',
  }
  dialogVisible.value = true
}
const update = (index: number) => {
  console.log(`output->index`, index)
  formKcData.value = tableData.value[index]
  formKcData.value['status'] = formKcData.value['status'].toString()
  dialogVisible.value = true
}
const handleCurrentChange = (val: any | undefined) => {
  currentRow.value = val
  console.log(`output->currentRow`, currentRow)
}
const delData = () => {
  debugger
  if (currentRow.value) {
    ElMessageBox.confirm('此操作将永久删除数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deletePlateData(currentRow.value.id).then((res: any) => {
        if (res.code == 200) {
          ElMessage.success(res.msg)
          initData()
        } else {
          ElMessage.error(res.msg)
        }
      })
    }).catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除'
      });
    });
  } else {
    ElMessage.warning('请先选择要删除的课程')
  }
}

const handleAvatarSuccess: UploadProps['onSuccess'] = (
  response,
  uploadFile
) => {
  if (response.code == 200) {
    ElMessage.success(response.msg)
    formKcData.value.cover = response.data
  } else {
    ElMessage.error(response.msg)
  }
}
</script>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 138px;
  height: 138px;
  text-align: center;
}

.v-note-wrapper .v-note-panel .v-note-edit.divarea-wrapper.single-edit {
  height: 410px;

}

.v-note-wrapper .v-note-panel .v-note-edit.divarea-wrapper.transition {
  height: 410px;
}

.v-note-panel {
  height: 410px;
}
</style>