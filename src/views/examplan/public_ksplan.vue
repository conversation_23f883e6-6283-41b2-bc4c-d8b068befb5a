<template>
  <div class="examplan" style="padding: 5px;">
    <div>
      <el-button type="primary" @click="initData" icon="RefreshRight"></el-button>
      <el-button type="primary" @click="add()" plain icon="DocumentAdd">添加</el-button>
      <el-button type="danger" @click="delData()" plain icon="DeleteFilled">删除</el-button>
      <el-select v-model="filterZy" class="m-2" placeholder="请选择专业" style="width: 190px;margin-left: 5px;"
        @change="initData()">
        <el-option v-for="item in zyData" :key="item.zy_bm" :label="item.zy_mcs" :value="item.zy_bm">
        </el-option>
      </el-select>
    </div>
    <el-table :tableHeight="tableHeight" :data="tableData" size="mini" stripe style="width: 100%;margin-top: 10px;"
      highlight-current-row @current-change="handleCurrentChange">
      <el-table-column type="index" width="50" align="center" />
      <el-table-column prop="kc_bm" label="课程编码" align="center"></el-table-column>
      <el-table-column prop="kc_mc" label="课程名称" align="center">
        <template #default="scope">
          <el-link @click="update(scope.$index)" type="primary">{{ scope.row.kc_mc }}&nbsp;&nbsp;<el-icon
              style="font-size: 13px;">
              <EditPen />
            </el-icon></el-link>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.status == 1" type="success">正常</el-tag>
          <el-tag v-else type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" align="center"></el-table-column>
      <el-table-column prop="create_name" label="创建人" align="center"></el-table-column>
      <el-table-column prop="create_date" label="创建时间" align="center"></el-table-column>
    </el-table>
    <el-dialog v-model="dialogVisible" title="添加专业信息" width="750" top="6vh" :append-to-body="true"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <div style="">
        <el-tabs tab-position="top" style="" class="demo-tabs">
          <el-tab-pane label="基本设置">
            <div style="padding: 10px 20px;">
              <el-form :inline="true" :model="formData" class="demo-form-inline" label-position="right"
                label-width="110px">
                <el-form-item label="选择课程">
                  <el-select v-model="formData.course_base_id" class="m-2" placeholder="Select" style="width: 190px;"
                    @change="selectKc">
                    <el-option v-for="item in kcData" :key="item.id" :label="item.kc_mcs" :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
                <p></p>
                <el-form-item label="在线课程版本">
                  <el-select v-model="formData.course_info_id" class="m-2" placeholder="Select" style="width: 190px;">
                    <el-option v-for="item in courseData" :key="item.id" :label="item.mc" :value="item.id">
                      <span>版本：{{ item.ver }}【<span style="color: green;">{{(item.is_online=1?'在线课程':item.is_online=0?'非在线课程':'未指定')}}</span>】
                      【<span style="color: green;">{{item.is_pub==1?'已发布':'未发布'}}</span>】</span>
                    </el-option>
                  </el-select>
                  &nbsp;没有在线课程？<el-link type="primary" @click="gotoOnline()">前往添加</el-link>
                </el-form-item>
                <p></p>
                <el-form-item label="课程编码">
                  <el-input v-model="formData.kc_bm" :disabled="true" placeholder="请输入课程名称" style="width: 190px;" />
                </el-form-item>
                <el-form-item label="课程名称">
                  <el-input v-model="formData.kc_mc" :disabled="true" placeholder="请输入课程名称" style="width: 190px;" />
                </el-form-item>
    
                <el-form-item label="状态">
                  <el-radio-group v-model="formData.status">
                    <el-radio label="1">启用</el-radio>
                    <el-radio label="0">禁用</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="备注">
                  <el-input v-model="formData.remark" :rows="2" type="textarea" style="width: 500px;" />
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveData()" :disabled="!formData.kc_bm">保存
          </el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="PlatedialogVisible" width="700" :close-on-click-modal="false" :close-on-press-escape="false"
      :append-to-body="true" top="6vh">
      <div class="m-2" style="display: flex;justify-content: center;">

        <el-tabs tab-position="top" style="" class="demo-tabs">
          <el-tab-pane label="板块设置">
            <div style="padding: 10px 20px;">
              <div style="padding: 10px 0px;"> 为【<span style="color: #409EFF;font-weight: bold;">{{ selectRow.kc_mc
                  }}</span>】课程指定板块
              </div>
              <el-transfer v-model="HavePlateData" :data="plateData" :titles="['未开通板块', '已开通板块']" />
            </div>
          </el-tab-pane>
        </el-tabs>

      </div>
      <div class="dialog-footer" style="padding: 10px 20px;text-align: center;border-top: 1px solid #F2F6FC;">
        <el-button @click="PlatedialogVisible = false">取消</el-button>
        <el-button type="primary" @click="savePlate()">保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang='ts'>
import { defineComponent, onMounted, reactive, ref, toRefs } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getPublicPlanData, getKcData, deletePublicPlanData, publicPlanSaveData,
  getPlateData, getPlateDataByCjPlanid, savePlateByCjPlanid, getOnlineCourse
} from '@/api/exam'
import type { UploadProps } from 'element-plus'
import Item from "@/layout/components/Sidebar/Item.vue";
const tableData = ref([])
const singleTableRef: any = ref({})
const currentRow = ref()
const tableHeight = ref(window.innerHeight - 200)
onMounted(() => {
  initData()
});
const formData: any = ref({
  id: "",
  zy_bm: '',
  zy_mc: '',
  status: '0',
})
const filterZy = ref("")
const value = ref("")
const dialogVisible = ref(false)
const zyData: any = ref([])
const kcData: any = ref([])
const plateData: any = ref([])
const HavePlateData: any = ref([])
const selectRow: any = ref({})
const PlatedialogVisible = ref(false)
const courseData:any = ref([])
const initData = () => {
  getPublicPlanData({ filterZy: filterZy.value }).then((res: any) => {
    if (res.code == 200) {
      tableData.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const saveData = () => {
  console.log(`output->formKcData`, formData)
  formData.value['id'] = formData.value['id'] ? formData.value['id'] : 0
  publicPlanSaveData(formData.value).then((res: any) => {
    if (res.code == "200") {
      ElMessage.success(res.msg)
      dialogVisible.value = false

      initData()
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const add = () => {
  formData.value = {
    id: "",
    course_base_id: "",
    course_info_id: "",
    ks_lx: '',
    kc_bm: '',
    kc_mc: "",
    ks_sj: "",
    remark: "",
    xf: 0,
    is_yy2_tk: '0',
    status: '0',
  }
  getKc()
  dialogVisible.value = true
}
const update = (index: number) => {
  console.log(`output->index`, index)
  formData.value = tableData.value[index]
  formData.value['status'] = formData.value['status'].toString()
  getOnlineCourseData()
  dialogVisible.value = true
  getKc()
}
const handleCurrentChange = (val: any | undefined) => {
  currentRow.value = val
  console.log(`output->currentRow`, currentRow)
}
const delData = () => {
  if (currentRow.value) {
    ElMessageBox.confirm('此操作将永久删除该计划, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deletePublicPlanData(currentRow.value.id).then((res: any) => {
        if (res.code == 200) {
          ElMessage.success(res.msg)
          initData()
        } else {
          ElMessage.error(res.msg)
        }
      })
    }).catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除'
      });
    });
  } else {
    ElMessage.warning('请先选择要删除的课程')
  }
}
const handleAvatarSuccess: UploadProps['onSuccess'] = (
  response,
  uploadFile
) => {
  if (response.code == 200) {
    ElMessage.success(response.msg)
    formData.value.cover = response.data
  } else {
    ElMessage.error(response.msg)
  }
}
const getKc = () => {
  getKcData({}).then((res: any) => {
    if (res.code == 200) {
      kcData.value = res.data.filter((x: any) => (x.learn_type == 99))
      kcData.value.forEach((e: any) => {
        e['kc_mcs'] = `(${e['kc_bm']})${e['kc_mc']}`
      });
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const openPlateData = (row: any) => {
  selectRow.value = row
  getHavePlate(row.id)
  PlatedialogVisible.value = true
  getPlate()
}
const getPlate = () => {
  getPlateData({}).then((res: any) => {
    if (res.code == 200) {
      var plateArray: any = []
      var obj = res.data.forEach((e: any) => {
        e['label'] = e['title']
        e['key'] = e['id']
        e['disabled'] = false
        plateArray.push(e)
      });
      plateData.value = plateArray
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const getHavePlate = (id: any) => {
  getPlateDataByCjPlanid(id).then((res: any) => {
    if (res.code == 200) {
      var have: any = []
      if (res.data) {
        res.data.forEach((e: any) => {
          have.push(e.plate_id)
        });
        HavePlateData.value = have
      } else {
        HavePlateData.value = []
      }

    } else {
      ElMessage.error(res.msg)
    }
  })
}
const savePlate = () => {
  var plateIdArray: any = []
  HavePlateData.value.forEach((e: any) => {
    plateIdArray.push(e)
  });
  if (plateIdArray.length == 0) {
    ElMessage.warning('请至少选择一个板块')
    return
  }
  var params = {
    id: selectRow.value.id,
    plateids: plateIdArray.join(',')
  }
  savePlateByCjPlanid(params).then((res: any) => {
    if (res.code == "200") {
      ElMessage.success(res.msg)
      initData()
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const selectKc = (val: any) => {
  var kc = kcData.value.find((x: any) => x.id == val)
  formData.value.kc_mc = kc.kc_mc
  formData.value.kc_bm = kc.kc_bm
  getOnlineCourseData()
}
const getOnlineCourseData = () => {
  getOnlineCourse({ id: formData.value.course_base_id}).then((res: any) => {
    if (res.code == 200) {
      courseData.value = res.data
      courseData.value.forEach((e: any) => {
        e['mc']='版本：'+ e.ver+'【'+(e.is_online=1?'在线课程':e.is_online=0?'非在线课程':'未指定')+'】'+'【'+(e.is_pub==1?'已发布':'未发布')+'】'
      });
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const gotoOnline = () => {
  window.open('/examplan/onlinecourse')
}
</script>

<style>
.el-dialog__body {
  padding: 0px !important;
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 108px;
  height: 108px;
  text-align: center;
}

.v-note-wrapper .v-note-panel .v-note-edit.divarea-wrapper.single-edit {
  height: 410px;

}

.v-note-wrapper .v-note-panel .v-note-edit.divarea-wrapper.transition {
  height: 410px;
}

.v-note-panel {
  height: 410px;
}
</style>