<template>
  <div class="kcmgr" style="padding: 5px;">
    <div>
      <el-button type="primary" @click="initData" icon="RefreshRight"></el-button>
      <el-button type="primary" @click="add()" plain icon="DocumentAdd">添加</el-button>
      <el-button type="danger" @click="delData()" plain icon="DeleteFilled">删除</el-button>
    </div>
    <el-table :tableHeight="tableHeight" :data="tableData" size="mini" stripe style="width: 100%;margin-top: 10px;"
      highlight-current-row @current-change="handleCurrentChange">
      <el-table-column type="index" width="50" align="center" />
      <el-table-column type="expand">
        <template #default="props">
          <div style="padding: 5px 50px!important;">
            <H3>课程简介</H3>
          </div>
          <div style="padding: 5px 50px 30px 50px!important;;">
            <mavon-editor :subfield="false" defaultOpen="preview" :editable="false" :toolbarsFlag="false"
              :boxShadow="false" :xssOptions="{}" :html="false" v-model="props.row.kc_introduction" />
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="learn_type" label="学习类型" align="center">
        <template #default="scope">
          <span v-if="scope.row.learn_type == 5">成人高考</span>
          <span v-else-if="scope.row.learn_type == 6">自考</span>
          <span v-else>微课</span>
        </template>
      </el-table-column>
      <el-table-column prop="kc_bm" label="课程编码" width="180" align="center" />
      <el-table-column prop="kc_mc" label="课程名称" width="180" align="center">
        <template #default="scope">
          <el-link @click="update(scope.$index)" type="primary">{{ scope.row.kc_mc }}&nbsp;&nbsp;<el-icon
              style="font-size: 13px;">
              <EditPen />
            </el-icon></el-link>
        </template>
      </el-table-column>
      <el-table-column prop="kc_hour" label="课程时长" align="center" />
      <el-table-column prop="ver_num" label="版本数" align="center" />
      <el-table-column prop="online_num" label="线上课程数" align="center" />
      <el-table-column prop="pub_num" label="已发布" align="center" />
      <el-table-column prop="status" label="状态" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.status == 1" type="success">正常</el-tag>
          <el-tag v-else type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="create_name" label="创建人" align="center"></el-table-column>
      <el-table-column prop="create_date" label="创建时间" align="center"></el-table-column>
    </el-table>
    <el-dialog v-model="dialogVisible" title="添加课程" width="800" top="6vh" :append-to-body="true"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <div style="height: 500px;">
        <el-tabs tab-position="left" style="height: 500px" class="demo-tabs">
          <el-tab-pane label="基本设置">
            <div style="padding: 10px 20px;">
              <el-form :inline="true" size="large" :model="formKcData" class="demo-form-inline">
                <el-form-item label="学习类型">
                  <el-select v-model="formKcData.learn_type" class="m-2" style="width: 170px;" placeholder="Select" size="large">
                    <el-option key="6" label="自考" value="6" />
                    <el-option key="5" label="成人高考" value="5" />
                    <el-option key="99" label="微课" value="99" />
                  </el-select>
                </el-form-item>
                <el-form-item label="课程编码">
                  <el-input style="width: 200px;" v-model="formKcData.kc_bm"  placeholder="课程编码" clearable />
                </el-form-item>
                <el-form-item label="课程名称">
                  <el-input size="large" v-model="formKcData.kc_mc" style="width: 170px;" placeholder="课程名称" clearable>
                  </el-input>
                </el-form-item>
                <el-form-item label="课程时长">
                  <el-input-number v-model="formKcData.kc_hour" :controls="false" style="width: 200px;"
                    placeholder="课程时长" clearable />
                </el-form-item>
                <el-form-item label="课程状态">
                  <el-radio-group v-model="formKcData.status">
                    <el-radio label="1" size="large" border>启用</el-radio>
                    <el-radio label="0" size="large" border>禁用</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="封面" style="align-items: center;font-weight: bold;">
                  <el-upload class="avatar-uploader" action="https://xczx7.swufe.edu.cn/oc/xczk/examplan/upload"
                    :show-file-list="false" :on-success="handleAvatarSuccess">
                    <img style="width: 108px;height: 108px;" v-if="formKcData.cover"
                      :src="'https://xczx7.swufe.edu.cn/oc/xczk/examplan/getFile?path=' + formKcData.cover"
                      class="avatar" />
                    <el-icon v-else class="avatar-uploader-icon">
                      <Plus />
                    </el-icon>
                  </el-upload>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
          <el-tab-pane label="课程简介">
            <div style="height: 500px;">
              <mavon-editor :boxShadow="false" :xssOptions="{}" :html="false" v-model="formKcData.kc_introduction" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveData()"
            :disabled="!formKcData.kc_bm || !formKcData.kc_mc || !formKcData.kc_hour">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>

</template>

<script setup lang='ts'>
import { defineComponent, onMounted, reactive, ref, toRefs } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getKcData, kcSaveData, deleteKcData } from '@/api/exam'
import type { UploadProps } from 'element-plus'
const tableData = ref([])
const singleTableRef: any = ref({})
const currentRow = ref()
const tableHeight = ref(window.innerHeight - 200)
onMounted(() => {
  initData()

});
const formKcData: any = ref({
  id: "",
  kc_bm: '',
  kc_mc: '',
  kc_hour: '',
  status: '0',
  cover: '',
  kc_introduction: ''
})
const value = ref("")
const dialogVisible = ref(false)
const initData = () => {
  getKcData({}).then((res: any) => {
    if (res.code == 200) {

      tableData.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const saveData = () => {
  console.log(`output->formKcData`, formKcData)
  formKcData.value['id'] = formKcData.value['id'] ? formKcData.value['id'] : 0
  kcSaveData(formKcData.value).then((res: any) => {
    if (res.code == "200") {
      ElMessage.success(res.msg)
      initData()
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const add = () => {
  formKcData.value = {
    id: "",
    kc_bm: '',
    kc_mc: '',
    kc_hour: '',
    status: '1',
    cover: '',
    learn_type:'',
    kc_introduction: ''
  }
  dialogVisible.value = true
}
const update = (index: number) => {
  console.log(`output->index`, index)
  formKcData.value = tableData.value[index]
  formKcData.value['status'] = formKcData.value['status'].toString()
  formKcData.value['learn_type']= formKcData.value['learn_type'].toString()
  dialogVisible.value = true
}
const handleCurrentChange = (val: any | undefined) => {
  currentRow.value = val
  console.log(`output->currentRow`, currentRow)
}
const delData = () => {
  debugger
  if (currentRow.value) {
    ElMessageBox.confirm('此操作将永久删除该课程, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deleteKcData(currentRow.value.id).then((res: any) => {
        if (res.code == 200) {
          ElMessage.success(res.msg)
          initData()
        } else {
          ElMessage.error(res.msg)
        }
      })
    }).catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除'
      });
    });
  } else {
    ElMessage.warning('请先选择要删除的课程')
  }
}

const handleAvatarSuccess: UploadProps['onSuccess'] = (
  response,
  uploadFile
) => {
  if (response.code == 200) {
    ElMessage.success(response.msg)
    formKcData.value.cover = response.data
  } else {
    ElMessage.error(response.msg)
  }
}
</script>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 138px;
  height: 138px;
  text-align: center;
}

.v-note-wrapper .v-note-panel .v-note-edit.divarea-wrapper.single-edit {
  height: 410px;

}

.v-note-wrapper .v-note-panel .v-note-edit.divarea-wrapper.transition {
  height: 410px;
}

.v-note-panel {
  height: 410px;
}
</style>