<template>
  <div class="kcmgr" style="padding: 5px;">
    <div>
      <el-button type="primary" @click="initData" icon="RefreshRight"></el-button>
      <el-button type="primary" @click="add()" plain icon="DocumentAdd">添加</el-button>
      <el-button type="danger" @click="delData()" plain icon="DeleteFilled">删除</el-button>
      <el-select v-model="filterType" class="m-2" placeholder="筛选学习类型" style="width: 190px;margin-left: 5px;"
        @change="initData()">
        <el-option key="1" label="成人高考" value="1"></el-option>
        <el-option key="2" label="自考" value="2"></el-option>
        <el-option key="3" label="微课" value="3"></el-option>

      </el-select>
    </div>

    <el-table border v-loading="loading" :height="tableHeight" :data="tableData" size="mini" stripe
      style="width: 100%;margin-top: 10px;" highlight-current-row @current-change="handleCurrentChange">
      <el-table-column type="index" width="50" align="center" />
      <el-table-column type="expand">
        <template #default="props">
          <div style="padding: 5px 50px!important;">
            <H3>课程简介</H3>
          </div>
          <div style="padding: 5px 50px 30px 50px!important;;">
            <mavon-editor :subfield="false" defaultOpen="preview" :editable="false" :toolbarsFlag="false"
              :boxShadow="false" :xssOptions="{}" :html="false" v-model="props.row.kc_introduction" />
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="learn_type_name" width="80" label="学习类型" align="center">
      </el-table-column>
      <el-table-column prop="kc_bm" label="课程编码" width="180" align="center" />
      <el-table-column prop="kc_mc" label="课程名称" width="300">
        <template #default="scope">
          <el-link @click="update(scope.$index)" type="primary">{{ scope.row.kc_mc }}&nbsp;&nbsp;<el-icon
              style="font-size: 13px;">
              <EditPen />
            </el-icon></el-link>
        </template>
      </el-table-column>
      <!--
      <el-table-column prop="kc_mc" label="基础板块" align="center">
        <template #default="scope">
          <el-link @click="openPlateData(scope.row)" type="primary">指定板块</el-link>
        </template>
      </el-table-column>
     
      <el-table-column prop="kc_mc" label="指定板块数量" align="center" width="50">
        <template #default="scope">
          <el-badge v-if="scope.row.platenum > 0" type="primary" :value="scope.row.platenum">
          </el-badge>
          <el-badge v-else :value="scope.row.platenum">
          </el-badge>
        </template>
      </el-table-column>
       -->
      <!--
      <el-table-column prop="kc_hour" label="课程时长" align="center" />
      <el-table-column prop="ver_num" label="版本数" align="center" />
      -->
      <el-table-column prop="online_num" label="线上课程数" align="center">
        <template #default="scope">
          {{ scope.row.online_num }}/{{ scope.row.pub_num }}
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.status == 1" type="success">正常</el-tag>
          <el-tag v-else type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="create_name" label="创建人" align="center"></el-table-column>
      <el-table-column prop="create_date" label="创建时间" align="center"></el-table-column>
    </el-table>
    <div style="margin-top: 15px;text-align: center;width: 100%;display: flex;justify-content: center;">
      <el-pagination @size-change="handleSizeChange" @current-change="handlePageChange" :current-page="page"
        :hide-on-single-page="true" :background="true" :page-sizes="[10, 20, 50, 100]" :page-size="size"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </div>
    <el-dialog v-model="dialogVisible" title="添加课程" width="800" top="6vh" :append-to-body="true"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <div style="height: 500px;">
        <el-tabs tab-position="left" style="height: 500px" class="demo-tabs">
          <el-tab-pane label="基本设置">
            <div style="padding: 10px 20px;">
              <el-form :inline="true" size="large" :model="formKcData" class="demo-form-inline">
                <el-form-item label="学习类型">
                  <el-select v-model="formKcData.learn_type" class="m-2" style="width: 170px;" placeholder="Select"
                    size="large">
                    <el-option key="6" label="自考" value="6" />
                    <el-option key="5" label="成人高考" value="5" />
                    <el-option key="99" label="微课" value="99" />
                  </el-select>
                </el-form-item>
                <el-form-item label="课程编码">
                  <el-input style="width: 200px;" v-model="formKcData.kc_bm" placeholder="课程编码" clearable />
                </el-form-item>
                <el-form-item label="课程名称">
                  <el-input size="large" v-model="formKcData.kc_mc" style="width: 170px;" placeholder="课程名称" clearable>
                  </el-input>
                </el-form-item>
                <el-form-item label="课程时长">
                  <el-input-number v-model="formKcData.kc_hour" :controls="false" style="width: 200px;"
                    placeholder="课程时长" clearable />
                </el-form-item>
                <el-form-item label="课程状态">
                  <el-radio-group v-model="formKcData.status">
                    <el-radio label="1" size="large" border>启用</el-radio>
                    <el-radio label="0" size="large" border>禁用</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="封面" style="align-items: center;font-weight: bold;">
                  <el-upload class="avatar-uploader" action="https://xczx7.swufe.edu.cn/oc/xczk/examplan/upload"
                    :show-file-list="false" :on-success="handleAvatarSuccess">
                    <img style="width: 108px;height: 108px;" v-if="formKcData.cover"
                      :src="'https://xczx7.swufe.edu.cn/oc/xczk/examplan/getFile?path=' + formKcData.cover"
                      class="avatar" />
                    <el-icon v-else class="avatar-uploader-icon">
                      <Plus />
                    </el-icon>
                  </el-upload>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
          <el-tab-pane label="课程简介">
            <div style="height: 500px;">
              <mavon-editor :boxShadow="false" :xssOptions="{}" :html="false" v-model="formKcData.kc_introduction" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveData()"
            :disabled="!formKcData.kc_bm || !formKcData.kc_mc || !formKcData.kc_hour">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>


    <el-dialog v-model="PlatedialogVisible" width="700" :close-on-click-modal="false" :close-on-press-escape="false"
      :append-to-body="true" top="6vh">
      <div class="m-2" style="display: flex;justify-content: center;">

        <el-tabs tab-position="top" style="" class="demo-tabs">
          <el-tab-pane label="板块设置">
            <div style="padding: 10px 20px;">
              <div style="padding: 10px 0px;"> 为【<span style="color: #409EFF;font-weight: bold;">{{ selectRow.kc_mc
              }}</span>】课程指定板块
              </div>
              <el-transfer v-model="HavePlateData" :data="plateData" :titles="['未开通板块', '已开通板块']"
                @click="PlageChange()" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="板块详细设置">
            <div style="padding: 0px 20px;height: 380px;width: 610px;overflow-y: auto;">
              <div class="item" v-for="(item, i) in platedSort" :key="item.id" draggable="true"
                @dragstart="dragstart($event, i)" @dragenter="dragenter($event, i)" @dragend="dragend"
                @dragover="dragover">
                <div style="width: 100%;display: flex;align-items: center;">
                  <div style="margin-top: 4px;" class="dragCurr">
                    <svg viewBox="0 0 1024 1024" width="30" height="30">
                      <path
                        d="M512 0a512 512 0 0 1 512 512 512 512 0 0 1-512 512A512 512 0 0 1 0 512 512 512 0 0 1 512 0z"
                        fill="#FFFFFF" p-id="7425"></path>
                      <path
                        d="M509.5424 720.6912L593.92 636.5184l35.2256 35.2256-119.1936 118.784-118.784-118.784 35.2256-35.2256zM509.952 245.76l118.784 118.784-34.816 35.2256-83.7632-84.1728-84.1728 84.1728L389.12 364.544l119.808-118.784zM307.2 482.304v-49.7664h409.6v49.7664z m0 112.8448v-49.7664h409.6v49.7664z"
                        fill="#2693FF" p-id="7426"></path>
                    </svg>
                  </div>
                  <div style="margin-left: 15px;">
                    【{{ i + 1 }}】 {{ item.title }}
                  </div>
                </div>
                <div style="margin-left: 40px;margin-top: 1px;" class="cj_ksplan-item">
                  <el-select v-model="item.is_list" placeholder="展示方式" size="mini" style="width: 100px">
                    <el-option :key="1" label="有列表" :value="1" />
                    <el-option :key="0" label="无列表" :value="0" />
                  </el-select>
                  <el-select v-model="item.is_blank" placeholder="打开方式" size="mini"
                    style="width: 150px;margin-left: 15px;">
                    <el-option :key="1" label="新标签页打开" :value="1" />
                    <el-option :key="0" label="当前页打开" :value="0" />
                  </el-select>
                  <el-select v-model="item.show_progress" placeholder="显示进度条" size="mini"
                    style="width: 150px;margin-left: 15px;">
                    <el-option :key="1" label="显示进度条" :value="1" />
                    <el-option :key="0" label="不显示进度条" :value="0" />
                  </el-select>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>



      </div>
      <div class="dialog-footer" style="padding: 10px 20px;text-align: center;border-top: 1px solid #F2F6FC;">
        <el-button @click="PlatedialogVisible = false">取消</el-button>
        <el-button type="primary" @click="savePlate()">保存
        </el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script setup lang='ts'>
import { defineComponent, onMounted, reactive, ref, toRefs } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getKcData, kcSaveData, deleteKcData, getPlateDataByCourseBase, savePlateByCourseBase } from '@/api/exam'
import type { UploadProps } from 'element-plus'
const tableData = ref([])
const singleTableRef: any = ref({})
const currentRow = ref()
const tableHeight = ref(window.innerHeight - 200)
const selectRow: any = ref({})
const loading = ref(false)
const total = ref(0)
const filterType = ref("")
onMounted(() => {
  initData()

});
const formKcData: any = ref({
  id: "",
  kc_bm: '',
  kc_mc: '',
  kc_hour: '',
  status: '0',
  cover: '',
  kc_introduction: ''
})
const value = ref("")
const dialogVisible = ref(false)
const initData = () => {
  loading.value = true
  getKcData({ page: page.value, size: size.value }).then((res: any) => {
    if (res.code == 200) {
      loading.value = false
      if (filterType.value) {
        tableData.value = res.data.list.filter((item: any) => item.learn_type == filterType.value)

      } else {
        tableData.value = res.data.list
      }

      total.value = res.data.total
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const saveData = () => {
  console.log(`output->formKcData`, formKcData)
  formKcData.value['id'] = formKcData.value['id'] ? formKcData.value['id'] : 0
  kcSaveData(formKcData.value).then((res: any) => {
    if (res.code == "200") {
      ElMessage.success(res.msg)
      initData()
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const add = () => {
  formKcData.value = {
    id: "",
    kc_bm: '',
    kc_mc: '',
    kc_hour: '',
    status: '1',
    cover: '',
    learn_type: '',
    kc_introduction: ''
  }
  dialogVisible.value = true
}
const update = (index: number) => {
  console.log(`output->index`, index)
  formKcData.value = tableData.value[index]
  formKcData.value['status'] = formKcData.value['status'].toString()
  formKcData.value['learn_type'] = formKcData.value['learn_type'].toString()
  dialogVisible.value = true
}
const handleCurrentChange = (val: any | undefined) => {
  currentRow.value = val
  console.log(`output->currentRow`, currentRow)
}
const delData = () => {
  debugger
  if (currentRow.value) {
    ElMessageBox.confirm('此操作将永久删除该课程, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deleteKcData(currentRow.value.id).then((res: any) => {
        if (res.code == 200) {
          ElMessage.success(res.msg)
          initData()
        } else {
          ElMessage.error(res.msg)
        }
      })
    }).catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除'
      });
    });
  } else {
    ElMessage.warning('请先选择要删除的课程')
  }
}
const handleAvatarSuccess: UploadProps['onSuccess'] = (
  response,
  uploadFile
) => {
  if (response.code == 200) {
    ElMessage.success(response.msg)
    formKcData.value.cover = response.data
  } else {
    ElMessage.error(response.msg)
  }
}
const openPlateData = (row: any) => {
  selectRow.value = row
  getHavePlate(row.id)
  PlatedialogVisible.value = true
  // getPlate()
}
const PlatedialogVisible = ref(false)
const plateData: any = ref([])
const HavePlateData: any = ref([])
const getHavePlate = (id: number) => {
  getPlateDataByCourseBase(id).then((res: any) => {
    if (res.code == 200) {
      var have: any = []
      if (res.data) {
        const { allList, haveList } = res.data
        if (haveList) {
          haveList.forEach((e: any) => {
            have.push(e.plate_id)
          });
        }
        HavePlateData.value = have
        plateData.value = allList
        plateData.value.forEach((e: any) => {
          e['label'] = e['title']
          e['key'] = e['id']
        })
        PlageChange()
      } else {
        HavePlateData.value = []
      }

    } else {
      ElMessage.error(res.msg)
    }
  })
}
const platedSort: any = ref([])
const PlageChange = () => {
  console.log(HavePlateData)
  platedSort.value = []
  if (HavePlateData.value.length > 0) {
    plateData.value.forEach((e: any) => {
      if (HavePlateData.value.indexOf(e.id) > -1) {
        e['is_list'] = e['is_list'] ? e['is_list'] : 0
        e['is_blank'] = e['is_blank'] ? e['is_blank'] : 0
        e['show_progress'] = e['show_progress'] ? e['show_progress'] : 0

        platedSort.value.push(e)
      }
    });
  }
}
const savePlate = () => {
  var plateIdArray: any = []
  platedSort.value.forEach((e: any, index: number) => {
    var obj = {
      plate_id: e.id,
      is_list: e.is_list,
      is_blank: e.is_blank,
      show_progress: e.show_progress,
      sn: index + 1
    }
    plateIdArray.push(obj)
  });
  if (plateIdArray.length == 0) {
    ElMessage.warning('请至少选择一个板块')
    return
  }
  var params = {
    id: selectRow.value.id,
    jsonstr: JSON.stringify(plateIdArray)
  }
  savePlateByCourseBase(params).then((res: any) => {
    if (res.code == "200") {
      ElMessage.success(res.msg)
      initData()
    } else {
      ElMessage.error(res.msg)
    }
  })
}
let dragIndex = 0
function dragstart(e: any, index: any) {
  e.stopPropagation()
  dragIndex = index
  setTimeout(() => {
    e.target.classList.add('moveing')
  }, 0)
}
function dragenter(e: any, index: any) {
  e.preventDefault()
  // 拖拽到原位置时不触发

  if (dragIndex !== index) {
    const source = platedSort.value[dragIndex];
    platedSort.value.splice(dragIndex, 1);
    platedSort.value.splice(index, 0, source);
    // 更新节点位置
    dragIndex = index
  }
}
function dragover(e: any) {
  e.preventDefault()
  e.dataTransfer.dropEffect = 'move'
}
function dragend(e: any) {
  e.target.classList.remove('moveing')
}
const size = ref(30)
const page = ref(1)
const handleSizeChange = (val: any) => {
  size.value = val;
  initData();
}
const handlePageChange = (val: any) => {
  page.value = val;
  initData();
}

</script>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 138px;
  height: 138px;
  text-align: center;
}

.v-note-wrapper .v-note-panel .v-note-edit.divarea-wrapper.single-edit {
  height: 410px;

}

.v-note-wrapper .v-note-panel .v-note-edit.divarea-wrapper.transition {
  height: 410px;
}

.v-note-panel {
  height: 410px;
}

.item {
  width: 96%;
  height: auto;
  background-color: #f5f6f8;
  margin: 10px;
  box-sizing: border-box;
  gap: 10px;
  padding: 5px 15px;
}

.dragCurr:hover {
  cursor: move;
}
</style>