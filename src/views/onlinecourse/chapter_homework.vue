<template>
  <div class="chapter-homework">
    <el-button type="primary" @click="initData">刷新</el-button>
    <el-table :data="tableData" :Height="tableHeight" stripe style="width: 100%;margin-top: 5px;" v-loading="tableloading">
      <el-table-column type="index" label="#" align="center" width="60" />
      <el-table-column prop="kc_bm" label="课程编码" width="180" align="center" />
      <el-table-column prop="kc_mc" label="课程名称" width="180" align="center" />
      <el-table-column prop="chapter_num" width="100" label="章节数" align="center" />
      <el-table-column prop="jobbank_count" width="100" label="章节作业数" align="center" />
      <el-table-column prop="chapter_num" label="" align="center">
        <template #default="scope">
          <el-button type="primary" @click="openDialog(scope.row.course_info_id)">添加章节作业</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog v-model="dialogVisible" title="指定章节作业" :fullscreen="true"  :before-close="handleClose">
      <div style="width: 100%;height: calc(100vh - 80px);display: flex;gap: 10px;">
        <div style="width: 300px;height: 100%;overflow-y: auto;" v-loading="leftLoading">
          <div style="color: #909399;font-weight: bold;font-size: 17px;">章节信息</div>
          <div style="margin-top: 15px;">
            <div v-for="(item, index) in chapterData"
              :class="'chapter-item ' + (chapter_id == item.id ? 'chapter-item-active' : '')" :key="index"
              @click="chageChapter(item.id,item)">
              <span>
                第 {{ index + 1 }} 章.{{ item.chapter_name }}
                <el-badge v-if="item.jobbank_count" :value="item.jobbank_count" type="primary"></el-badge>
              </span>
              <span v-if="chapter_id == item.id" style="font-size: 18px;margin-top: 5px;">
                <el-icon>
                  <CircleCheckFilled />
                </el-icon>
              </span>
            </div>
          </div>
        </div>
        <div style="width: calc(100% - 300px);height: 100%;overflow-y: auto;" v-loading="rightLoading">
          <div style="padding: 5px 0px;display: flex;justify-content:space-between;align-items: center;">
            <div style="display: flex;align-items: center;gap: 10px;">
              <el-button type="primary" @click="getQuestion()"><el-icon>
                  <Refresh />
                </el-icon></el-button>
              题库来源：
              <el-radio-group v-model="questionSource" @change="getQuestion">
                <el-radio label="ols" border>在线章节</el-radio>
                <el-radio label="jobbank" border>题库</el-radio>
              </el-radio-group>
            </div>
            <div>
              <el-button type="primary" @click="saveData()">保存《{{checkChapter.chapter_name}}》作业配置</el-button>
            </div>
          </div>
          <el-tabs v-model="activeName" v-if="jobbankData[0].chapter_name" type="border-card" class="demo-tabs">
            <el-tab-pane v-for="(item, index) in jobbankData" :label="'第' + (index + 1) + '章'" :name="index">
              <template #label>
                <div style="display: flex;">
                  第{{ (index + 1) }}章&nbsp;&nbsp;
                  <el-badge v-if="checkboxGroup1['index' + index].length > 0"
                    :value="checkboxGroup1['index' + index].length" style="margin-top: 1px;">
                  </el-badge>
                </div>
              </template>
              <div style="height: 80vh;overflow-y: auto;">
                <div
                  style="width: 100%;text-align: center;display: flex;align-items: center;justify-content: space-between;">
                  <span>{{ item.chapter_name }}</span>
                  <span>
                    <el-checkbox v-model="checkAll['index' + index]" :indeterminate="isIndeterminate"
                      @change="(checked: any) => handleCheckAllChange(checked, index)">选择全部</el-checkbox>
                  </span>
                </div>
                <div v-for="(questiontype, bindex) in item.children" style="margin-top: 15px;">
                  <div class="questiontype_item">{{ bindex + 1 }}、{{ questiontype.questionType }}</div>
                  <div v-for="(question, qindex) in questiontype.children" :key="qindex"
                    style="padding: 10px 5px;display: flex;align-items: center;gap: 15px;">
                    <div>
                      <el-checkbox-group v-model="checkboxGroup1['index' + index]" @change="handleCheckedChange">
                        <el-checkbox label="选择" :value="question.jobbank_id" border />
                      </el-checkbox-group>
                    </div>
                    <div style="max-height: 100px;overflow-y: auto;border-bottom: 1px solid #E4E7ED;flex: 1;">
                      <div style="margin-bottom: 5px;">{{ qindex + 1 }}.{{ question.title.title }}</div>
                      <div style="padding: 5px 0px;">
                        <span v-if="question.title.options" v-for="(option, oindex) in question.title.options"
                          :key="oindex" style="padding: 0px 15px;">
                          <span :style="'color:' + (option.answer ? 'green' : '#333')">{{ String.fromCharCode(65 +
                            oindex)
                          }}.{{ option.option }}</span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang='ts'>
import { defineComponent, onMounted, reactive, ref, toRefs } from "vue";
import { getOlsCourseChapterJob, getOlsCourseChapter, getOlsChapterQuestion, saveOlsChapterQuestion, getOlsChapterJob } from '@/api/course'
import { ElMessage, ElMessageBox } from 'element-plus';
onMounted(() => {
  initData()
})
const checkboxGroup1: any = ref({

})
const tableHeight = window.innerHeight - 200
const tableData = ref([])
const chapterData: any = ref([])
const dialogVisible = ref(false)
const chapter_id = ref("")
const now_course_info_id = ref("")
const questionSource = ref("ols") // 题库来源，ols: 在线章节，jobbank: 题库
const activeName = ref(0) // 当前选中的标签页
const checkAll: any = ref({
})
const rightLoading = ref(false)
const leftLoading = ref(false)
const isIndeterminate = ref(true)
const jobbankData: any = ref([
  {
    children: [
      {
        children: [
          { jobbank_id: 0 as Number }
        ] as any
      }
    ] as any,
  }
])
const Originally: any = ref({})
const checkChapter:any=ref({})
const tableloading=ref(false)
const initData = () => {
  tableloading.value = true
  getOlsCourseChapterJob({}).then((res) => {
    console.log(res)
    tableData.value = res.data
  }).catch((err) => {
    console.error(err)
  }).finally(() => {
    tableloading.value = false
  })
}
const openDialog = (id: any) => {
  getChapter(id)
  dialogVisible.value = true
}
const getChapter = (course_info_id: string) => {
  leftLoading.value = true
  now_course_info_id.value = course_info_id
  getOlsCourseChapter({ course_info_id }).then((res) => {
    var data = res.data.filter((item: any) => {
      return item.pid == 0
    })
    chapterData.value = data.sort((a: any, b: any) => {
      return a.sn - b.sn
    })
    chageChapter(chapter_id.value ? chapter_id.value : chapterData.value[0].id, chapterData.value[0])
    console.log(`output->chapterData.value`, chapterData.value)
  }).catch((err) => {
    console.error(err)
  }).finally(() => {
    leftLoading.value = false
  })
}
const chageChapter = (id:any,obj: any) => {
  chapter_id.value = id
  checkChapter.value = obj
  getQuestion()
  console.log(`output->chapter_id`, chapter_id)
}
const getQuestion = () => {
  rightLoading.value = true
  getOlsChapterQuestion({ course_info_id: now_course_info_id.value, chapter_id: chapter_id.value, source: questionSource.value }).then((res) => {
    console.log(`output->getQuestion`, res)
    jobbankData.value = res.data.listData
    var checkJobbank = res.data.data ? res.data.data[0].jobbank_ids : []
    jobbankData.value.forEach((item: any, index: Number) => {
      checkAll.value['index' + index] = -1
      //checkboxGroup1.value['index' + index] = checkboxGroup1.value['index' + index] ? checkboxGroup1.value['index' + index] : []
      checkboxGroup1.value['index' + index]=[]
      var obb:any=[]
      checkJobbank.forEach((element: any) => {
        if (item.answerData.indexOf(element.id) > -1) {
          obb.push(element.id)
          checkboxGroup1.value['index' + index].push(element.id)
        }
      });
      //checkboxGroup1.value['index' + index] =[...new Set(checkboxGroup1.value['index' + index].concat(obb))];
    });
    Originally.value = JSON.parse(JSON.stringify(checkboxGroup1.value))
    console.log(checkboxGroup1.value)
  }).catch((err) => {
    console.error(err)
  }).finally(() => {
    rightLoading.value = false
  })
}
const handleCheckedChange = (val: any) => {
  console.log(`output->handleCheckedChange`, val)
  console.log(checkboxGroup1.value)
}
const handleCheckAllChange = (val: any, index: any) => {
  checkboxGroup1.value['index' + index] = val ? jobbankData.value[index].answerData : []
  console.log(checkboxGroup1.value)
  isIndeterminate.value = false
}
const saveData = () => {
  var jobbankids: any = []
  jobbankData.value.forEach((item: any, index: Number) => {
    if (checkboxGroup1.value['index' + index].length > 0) {
      jobbankids.push(...checkboxGroup1.value['index' + index])
    }

  });
  console.log(`output->saveData`, chapter_id.value)
  console.log(`output->saveData`, jobbankids)
  var data = {
    chapter_id: chapter_id.value,
    jobbankids: JSON.stringify(jobbankids)
  }
  saveOlsChapterQuestion(data).then((res: any) => {
    console.log(`output->saveOlsChapterQuestion`, res)
    if (res.data > 0) {
      ElMessage.success("保存成功")
      getQuestion()
      getChapter(now_course_info_id.value)
    } else {
      ElMessage.error("保存失败")
    }
  }).catch((err: any) => {
    console.error(err)
    ElMessage.error("保存失败")
  })
}
const handleClose = (done: () => void) => {
  var isChange = shallowEqual(Originally.value, checkboxGroup1.value)
  if (!isChange) {
    ElMessageBox.confirm(
      '数据已修改，还未保存!是否离开？',
      'Warning',
      {
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
        type: 'warning',
      }
    )
      .then(() => {
        done()
      })
      .catch(() => {
       
      })
  }else{
    done()
  }
}
const shallowEqual = (objA: any, objB: any) => {
  // 检查是否为同一个对象引用
  if (objA === objB) return true;

  // 检查是否都是对象
  if (typeof objA !== 'object' || objA === null ||
    typeof objB !== 'object' || objB === null) {
    return false;
  }

  // 获取所有键名
  const keysA = Object.keys(objA);
  const keysB = Object.keys(objB);

  // 检查键的数量是否相同
  if (keysA.length !== keysB.length) return false;
  
  var unEqual=0
  keysA.forEach((e:any)=>{
      if(JSON.stringify(objA[e])!=JSON.stringify(objB[e])){
        unEqual++
      }
  })
  // 检查每个键的值是否相同
  //return keysA.every(key => objA[key] === objB[key]);
  return unEqual === 0
}

</script>

<style>
.chapter-homework {
  padding: 5px;
}

.chapter-homework .el-dialog__body {
  padding: 0px ! important;
}

.chapter-item {
  padding: 13px 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  border-radius: 5px;
  ;
  justify-content: space-between;
  margin: 5px 0px;
}

.chapter-item:hover {

  background-color: rgb(235.9, 245.3, 255);
  cursor: pointer;
}

.chapter-item-active {

  background-color: rgb(216.8, 235.6, 255);
  color: #409EFF;
  cursor: pointer;
}

.questiontype_item {
  width: 100%;
  background-color: #fafafa;
  border-radius: 5px;
  font-weight: bold;
  letter-spacing: 1px;
  padding: 10px 5px;
}
</style>