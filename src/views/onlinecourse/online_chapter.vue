<template>
  <div class="quesbank page">
    <div class="header"> 
      <el-button type="success" plain size="default" icon="refresh" round @click="getData"></el-button>
      <el-input v-model="userData.keySearch" placeholder="知识点" size="default" style="width: 260px;margin:0 10px;" clearable @change="getData">
        <template #append>
          <el-button icon="Search" @click="getData"/>
        </template>
      </el-input>
      <el-tag class="el_tag_5">课程教材</el-tag>
      <el-select v-model="userData.course_jc_id"
                 placeholder="请选择教材课程"
                 clearable
                 style="width: 200px;"
                 @change="getData">
        <el-option
            v-for="item in courseJcList"
            :key="item.id"
            :label="item.course_name"
            :value="item.id">
              <span style="float: left">{{ item.course_name }}</span>
              <span style="float: right; color: #67C23A; font-size: 13px;">{{ item.course_code }}</span>
        </el-option>
      </el-select> 
    </div>
    <div class="body">
      <el-table
        :data="userData.dataTable"
        border
        class="modTable"
        :height="userData.tableHeight"
        style="width: 100%;"
        v-loading="userData.loading"
        @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column type="selection" width="50" align="center" />  -->
        <!-- <el-table-column prop="learn_type"  label="课程类型" min-width="100" align="center" header-align="center" show-overflow-tooltip>
        <template #default="scope">
          <span v-if="scope.row.learn_type === 5">成人高考</span>
          <span v-else>自考</span>
        </template>
      </el-table-column> -->
        <el-table-column prop="" type="index" label="序号"  align="center" width="60" header-align="center" />
        
        <el-table-column prop="linked_knowledge_point_code" label="知识点编码" width="80" align="center" header-align="center" show-overflow-tooltip/>
        <el-table-column prop="linked_knowledge_point_name" label="知识点" min-width="120" align="left" header-align="center" show-overflow-tooltip/>
        <el-table-column prop="node_code" label="节点编码" width="80" align="center" header-align="center"/>
        <el-table-column prop="node_name" label="节点名称" min-width="120" align="left" header-align="center"/>
        <el-table-column prop="chapter_code" label="章节编码" width="80" align="center" header-align="center"/>
        <el-table-column prop="chapter_name" label="章节名称" min-width="120" align="left" header-align="center"/>
        <el-table-column prop="course_code" label="课程编码" width="80" align="center" show-overflow-tooltip/>
        <el-table-column prop="course_name" label="课程名称" min-width="120" align="left" header-align="center">
          <template #default="scope">
              {{ getCourseName(scope.row.course_jc_id) }}
            </template>
        </el-table-column>
        <el-table-column prop="create_date" label="创建时间" min-width="120" align="center" header-align="center"/>
        <el-table-column prop="create_user" label="创建人" min-width="120" align="center" header-align="center"/>
      </el-table>
      <div style="margin-top: 16px; text-align: right;">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="userData.total"
          :page-size="userData.pageSize"
          :current-page="userData.currentPage"
          :page-sizes="[20, 50, 100, 500, userData.total]"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { GetQuestionBankPage} from '@/api/question'
import { getSelectCourseJcList ,getCourseKnowledgeNodePage} from '@/api/dzs';
import { msgShow } from '@/utils/message'; 

const userData = reactive({
  loading: false,
  tableHeight: window.innerHeight - 200,
  keySearch: '',
  course_jc_id:5,
  dataTable: [] as any,
  total: 0,
  pageSize: 30,
  currentPage: 1
}) 
const handlecurrentRow = ref([]) as any

// 课程列表
const courseJcList = ref([]) as any

// 获取课程列表
const getCourseJcListData = async () => {
  try {
    const res = await getSelectCourseJcList()
    if (res.code === 200) {
      courseJcList.value = res.data
      getData()
    } else {
      msgShow(res.msg || '获取课程列表失败', 'warning')
    }
  } catch (error) {
    msgShow('获取课程列表失败', 'error')
  }
}

const getData = () => {
  userData.loading = true
  const pars = {
    keySearch: userData.keySearch,
    course_jc_id: userData.course_jc_id,
    currentPage: userData.currentPage,
    pageSize: userData.pageSize,
  }
  getCourseKnowledgeNodePage(pars).then((msg: any) => {
    userData.dataTable = msg.data || []
    userData.total = msg.total || userData.dataTable.length
    userData.loading = false
  }).catch((err: any) => {
    console.log(err)
    userData.loading = false
  })
}
const getCourseName = (courseId: number | null) => {
  if (!courseId) return '-'
  const course = courseJcList.value.find((c:any) => Number(c.id) === Number(courseId))
  return course?.course_name || '-'
}

const handleSelectionChange = (rows: any) => {
  handlecurrentRow.value = rows
}
const handleSizeChange = (size: number) => {
  userData.pageSize = size
  getData()
}

const handlePageChange = (page: number) => {
  userData.currentPage = page
  getData()
}


const calcTableHeight = () => {
  // 这里的220可根据实际页面头部、分页等高度调整
  userData.tableHeight = window.innerHeight - 200;
}

onMounted(() => {
  getCourseJcListData()
  calcTableHeight();
  window.addEventListener('resize', calcTableHeight);
});

onUnmounted(() => {
  window.removeEventListener('resize', calcTableHeight);
});


</script>

<style lang="scss"> 


</style>
