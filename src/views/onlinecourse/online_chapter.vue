<template>
 <div class="online_chapter page" >
  <div class="header"> 
      <el-button type="success" plain size="default" icon="refresh" round @click="getData"></el-button>
      <el-button type="primary" plain @click="openRowExpansion()" size="default" round icon="View" aria-label="">{{openButtonText}}</el-button>
      <el-button type="success" plain @click="openAddNodeDialog()"  icon="CirclePlus" round size="default">添加“章”</el-button>
      <el-checkbox style="margin-left: 10px;" v-model="isShow" size="default" @change="getData()">显示全部</el-checkbox> 
      <el-tag class="el_tag_5">在线课程</el-tag>
      <el-select v-model="tableData.course_info_id"
                 placeholder="请选择在线课程"
                 clearable
                 filterable
                 style="width: 200px;"
                 @change="getData">
        <el-option
            v-for="item in OlsCourseList"
            :key="item.id"
            :label="item.kc_mc"
            :value="item.id">
              <span style="float: left">{{ '('+item.id+')'+item.kc_mc }}</span>
              <span style="float: right; color: #67C23A; font-size: 13px;">{{ item.kc_bm }}</span>
        </el-option>
      </el-select> 
  </div>
  <div class="body"> 
   <el-table
      border
      :height="tableData.tableHeight"
      :data="tableData.tableDataData" 
      :row-class-name="tableRowClassName"
      row-key="id"
      class="moduleTable"
      v-loading="tableData.loading" 
      :default-expand-all="false"
      highlight-current-row
      ref="tablexTree"
    >
      <el-table-column prop=""  label="#" align="center" width="60" />
      <el-table-column prop="id" label="编号(id)" align="center"  width="80" />
      <el-table-column prop="pid" label="上级编号(pid)" align="center" width="100" />
      <el-table-column prop="chapter_code" label="章节编码" align="center" width="100" />
      <el-table-column prop="chapter_name" label="章节名称" align="left" header-align="center" min-width="160" />
      <el-table-column prop="sn" label="排序" header-align="center" align="center" width="80" />
      <el-table-column prop="status" label="状态" align="center" width="80" show-overflow-tooltip>
        <template #default="scope">
          <span v-if="scope.row.status===1" style="color:blue">启用</span>
          <span v-else style="color:Red">禁用</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="操作" align="center" min-width="200" >
        <template #default ="scope">
          <el-button v-if="scope.row.pid===0" link type="primary" size="small" @click="openEditNode(scope.row)"><el-icon><Edit /></el-icon>编辑</el-button>
          <el-button v-if="scope.row.pid!==0" link type="primary" size="small" @click="openEditNode(scope.row)"><el-icon><Edit /></el-icon>编辑</el-button>
          <el-button v-if="((scope.row.pid===0 && scope.row.children.length===0) || scope.row.pid!==0) && scope.row.status!==1" link type="danger" size="small" @click="DetailClick(scope.row)"><el-icon><Delete /></el-icon>删除</el-button>
          <el-button v-if="scope.row.pid===0" link type="primary" size="small" @click="openAddChildNode(scope.row)"><el-icon><CirclePlusFilled /></el-icon>添加“节”</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
     <!--父节点编辑-->
    <el-dialog v-model="parentNodeVisible" 
      :title="node_title" 
      custom-class="parentNodeclass"
      draggable
      width="800"
      :close-on-press-escape="false"
      :close-on-click-modal="false">
      <div style="width:100%;text-align:center;">
        <el-form :model="modulefrom" :inline="true" :label-width="formLabelWidth">
          
           <el-form-item label="章名称"  v-if="modulefrom.type==='menu'">
            <div style="width: 510px;">
              <span style="color: red;">
                {{modulefrom.old_chaptername}}
              </span>
            </div>
          </el-form-item>
          <el-form-item label="类型" >
            <el-radio-group v-model="modulefrom.type" :disabled="modulefrom_nodeType" style="width:200px;">
              <el-radio label="menu">节</el-radio>
              <el-radio label="menu_dir">章</el-radio>
            </el-radio-group>
          </el-form-item>
           <el-form-item label="是否启用">
            <el-radio-group v-model="modulefrom.status" style="width:200px;">
              <el-radio :label="1"  @change="checkStatus()">启用</el-radio>
              <el-radio :label="0"  @change="checkStatus()">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="modulefrom.type==='menu'?'节号':'章号'" >
            <el-input v-model="modulefrom.chapter_code" placeholder="序号"  style="width:200px;"/>
          </el-form-item>
          <el-form-item label="排序" >
             <el-input-number v-model="modulefrom.sn" :min="0" placeholder="排序" style="width:200px"  />
          </el-form-item>
          <el-form-item :label="modulefrom.type==='menu'?'节名称':'章名称'" >
            <el-input v-model="modulefrom.chapter_name" placeholder="名称"  style="width:510px;"/>
          </el-form-item>
          <el-form-item label="备注" >
            <el-input v-model="modulefrom.reamrk" type="textarea" style="width:510px;" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="parentNodeVisible = false">关闭</el-button>
          <el-button type="success" @click="SaveParent()"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>

 </div>
</template>

<script lang="ts" setup>
    import { defineComponent, onMounted, reactive, ref, toRefs } from "vue";
    import { getOlsCourseChapterData,SaveOlsCourseChapterData,DelOlsCourseChapterData,getSelectOlsCourseList } from '@/api/course'
    import { ElMessage,ElMessageBox } from "element-plus";
    import { msgShow } from '@/utils/message'; 

    const isShow = ref(false);
    const parentNodeVisible = ref(false);  
    const modulefrom_nodeType = ref(false);
    const node_title = ref('');
    const formLabelWidth = '80px'
    const tablexTree = ref();
    const tableExpansion = ref(false);

    const modulefrom = ref({}) as any;
    const tableData = reactive({
      tableDataData:[],
      tableHeight:window.innerHeight - 155,
      loading:false,
      course_info_id:''
    });
    const openButtonText = ref('展开全部');
    // 课程列表
    const OlsCourseList = ref([]) as any
        // 获取课程列表
    const getOlsCourseListData = async () => {
      try {
         getSelectOlsCourseList().then((res:any) => {
            if (res.code === 200) {
              OlsCourseList.value = res.data
              getData()
            } else {
              msgShow(res.msg || '获取课程列表失败', 'warning')
            }
          })
      } catch (error) {
        msgShow('获取课程列表失败', 'error')
      }
    }
    const getData=() => {
      tableData.loading=true
      var par = {
        course_info_id: tableData.course_info_id,
        status : isShow.value ? '' : '1'
      };
      getOlsCourseChapterData(par).then((res:any) => {
        tableData.tableDataData = res.data
        tableData.loading=false
      })
    }

    //添加父节点
    const openAddNodeDialog=() => {
      modulefrom.value = {
        id: 0,
        pid:0,
        sn: 0,
        reamrk: '',
        status: 1,
        type:'menu_dir'
      }
      node_title.value="添加“章”信息"
      parentNodeVisible.value = true
      modulefrom_nodeType.value = true

    }
    //添加子节点
    const openAddChildNode=(row:any) => {
      modulefrom.value = {
        id: 0,
        pid: row.id,
        sn: 0,
        reamrk:  '',
        status: 1,
        type: 'menu',
        old_chaptername:row.chapter_name
      }
      node_title.value="添加“节”信息" 
      modulefrom_nodeType.value = true;
      parentNodeVisible.value = true
    }
    //修改节点
    const openEditNode=(row:any) => {
      modulefrom.value = {
        id: row.id,
        pid: row.pid,
        chapter_code: row.chapter_code,
        chapter_name:  row.chapter_name,
        sn: row.sn,
        reamrk:  row.reamrk,
        status: row.status,
        type: row.pid===0? 'menu_dir':'menu'
      }
      node_title.value=row.pid===0? "编辑“章”信息":"编辑“节”信息"
      modulefrom_nodeType.value = row.pid === 0 ? true : false
      parentNodeVisible.value = true
    }
   
    //保存节点数据
    const SaveParent=() => {
      if(modulefrom.value.chapter_code==='' || modulefrom.value.chapter_code===null || modulefrom.value.chapter_code === undefined){
        ElMessage({
              message: '章节号不能为空',
              type: 'info',
            })
            return
      }
      if(modulefrom.value.chapter_name==='' || modulefrom.value.chapter_name===null || modulefrom.value.chapter_name === undefined){
        ElMessage({
              message: '章节名称不能为空',
              type: 'info',
            })
            return
      }
      SaveOlsCourseChapterData(modulefrom.value).then((res:any) => {
        if (res.data > 0 ) {
          if (modulefrom.value.type === 'menu_dir') {
            if (modulefrom.value.status === 0) {
              // DisablePerentNode({ pid: modulefrom.value.id, status: modulefrom.value.status }).then((res:any) => {
              //   if (res.data < 0) {
              //     return
              //   }
              // })
            }
          }
          parentNodeVisible.value = false
          ElMessage({
            message: '保存成功！',
            type: 'success',
          })
          getData()
        }
      })
    }
    //禁用父节点时同步子节点状态
    const checkStatus=()=> {
      if (modulefrom.value.status === 0) {
        if (modulefrom.value.type === 'menu_dir') {
          ElMessage({
            message: '你正在禁用父节点，如果禁用其下所有子节点将会同时被禁用！！',
            type: 'warning',
          })
        }
      }
    }
    const tableRowClassName=( row:any )=> {
      if (row.status === 0) {
        return 'success-row'
      }
      return ''
    }
    //数据全部展开与关闭
    const openRowExpansion=()=> {
      if(tableExpansion.value){
        tableExpansion.value=false
        openButtonText.value='展开全部'
        dataTabdata(tableData.tableDataData,false)
      }else{
        tableExpansion.value=true
        openButtonText.value='全部折叠'
        dataTabdata(tableData.tableDataData,true)
      }
    }
    const dataTabdata=(data:any,sta:boolean)=>{
      data.forEach((i:any) => {
        tablexTree.value.toggleRowExpansion(i,sta)
        if(i.children){
          forArr(i.children,sta)
        }
      });
    }
    const forArr=(arr:any,sta:boolean)=>{
      arr.forEach((i:any) => {
        tablexTree.value.toggleRowExpansion(i,sta)
        if(i.children){
          forArr(i.children,sta)
        }
      });
    }
    const DetailClick=(row:any) => {
        ElMessageBox.confirm(
          '此操作将永久删除【' + row.title + '】节点功能, 是否继续?',
          '提示',
          {
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        .then(() => {
          if (row.children.length>0 && row.pid===0) {
             ElMessage({
                type: 'info',
                message: '存在节点信息!不能删除本章信息！！！请确认'
              })
          }
          const pars = { id: row.id }
          DelOlsCourseChapterData(pars).then((msg:any) => {
            if (msg.data > 0) {
              ElMessage({
                type: 'success',
                message: '成功删除!' + msg.data + '条'
              })
              getData()
            } else {
              ElMessage({
                type: 'info',
                message: '删除失败!' + msg.msg
              })
            }
          }).catch((err:any) => {
            ElMessage({
              type: 'info',
              message: '删除失败!' + err
            })
          })
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '取消删除!',
          })
        })
    }
    
    


  onMounted(() => {
      getOlsCourseListData();
  });
  





</script>

<style  lang="scss">
.online_chapter{ 
  
  .parentNodeclass{
  margin-top: 10vh !important;
  width: 800px;
  }
  .icondialogclass{
  margin-top: 15vh !important;
  width: 700px;
  }
  .permsdialogclass{
  margin-top: 15vh !important;
  width: 700px;
  }
  .dialog_icon_div{
  height: 40vh;
  overflow: auto;
  }
  .elrow_4{
  margin-bottom: 10px;

  }
}

</style>
