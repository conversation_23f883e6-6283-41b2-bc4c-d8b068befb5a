<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 搜索和过滤区域 -->
      <div class="search-container">
        <el-form :inline="true" :model="queryParams" class="demo-form-inline">
          <el-form-item label="搜索关键词">
            <el-input
              v-model="queryParams.search_key"
              placeholder="课程代码/名称/教材名称/主编"
              clearable
              @clear="handleQuery"
              @keyup.enter="handleQuery"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">刷新</el-button>
          </el-form-item>
        </el-form>
        <div class="action-buttons">
          <el-button type="success" @click="handleAdd">添加教材</el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="course_code" label="课程代码" sortable="custom" width="120" />
        <el-table-column prop="course_name" label="课程名称" sortable="custom" min-width="180" />
        <el-table-column prop="course_credits" label="学分" sortable="custom" width="80" align="center" />
        <el-table-column prop="course_type" label="课程性质" width="120" />
        <el-table-column prop="textbook_name" label="教材名称" min-width="200" />
        <el-table-column prop="textbook_editor" label="主编" width="120" />
        <el-table-column prop="publication_info" label="出版信息" min-width="200" />
        <el-table-column label="教材封面" width="100" align="center">
          <template #default="{ row }">
            <el-link v-if="row.textbook_url" type="primary" @click="previewImage(row.textbook_url)">
              查看封面
            </el-link>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="教材文件" width="100" align="center">
          <template #default="{ row }">
            <el-link v-if="row.url" type="primary" @click="previewPdf(row.url)">
              查看文件
            </el-link>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button link type="success" size="small" @click="viewMaterials(scope.row)">查看资料</el-button>
            <el-button link type="warning" size="small" @click="assignResources(scope.row)">指定资料</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 图片预览对话框 -->
      <el-dialog v-model="imagePreview.visible" title="教材封面预览" width="50%">
        <el-image :src="imagePreview.url" fit="contain" style="width: 100%; height: 60vh;">
          <template #error>
            <div class="image-error">
              <el-icon><Picture /></el-icon>
              <span>图片加载失败</span>
            </div>
          </template>
        </el-image>
      </el-dialog>

      <!-- PDF预览对话框 -->
      <el-dialog v-model="pdfPreview.visible" title="教材文件预览" width="80%" top="5vh">
        <iframe 
          v-if="pdfPreview.url" 
          :src="`https://xczx7.swufe.edu.cn/vue/pdfjs-5.3.31-dist/web/viewer.html?file=${encodeURIComponent(pdfPreview.url)}`" 
          style="width: 100%; height: 80vh; border: none;"
        ></iframe>
      </el-dialog>

      <!-- 添加/编辑对话框 -->
      <el-dialog 
        v-model="dialog.visible" 
        :title="dialog.title" 
        width="50%"
        :close-on-click-modal="false"
        @closed="resetForm"
      >
        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="选择课程" prop="course_base_id" :rules="[{ required: true, message: '请选择课程', trigger: 'change' }]">
            <el-select 
              v-model="form.course_base_id" 
              placeholder="请选择课程" 
              style="width: 100%"
              @change="handleCourseChange"
              filterable
              clearable
            >
              <el-option
                v-for="item in courseList"
                :key="item.id"
                :label="`${item.kc_bm} ${item.kc_mc}`"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="学分" prop="course_credits">
            <el-input-number v-model="form.course_credits" :min="0" :step="0.5" :precision="1" />
          </el-form-item>
          <el-form-item label="课程性质" prop="course_type">
            <el-input v-model="form.course_type" placeholder="请输入课程性质" />
          </el-form-item>
          <el-form-item label="教材名称" prop="textbook_name">
            <el-input v-model="form.textbook_name" placeholder="请输入教材名称" />
          </el-form-item>
          <el-form-item label="主编" prop="textbook_editor">
            <el-input v-model="form.textbook_editor" placeholder="请输入主编姓名" />
          </el-form-item>
          <el-form-item label="出版信息" prop="publication_info">
            <el-input 
              v-model="form.publication_info" 
              type="textarea" 
              :rows="2" 
              placeholder="请输入出版信息，如：出版社, 出版年份" 
            />
          </el-form-item>
          <el-form-item label="教材封面" prop="textbook_url">
            <div class="upload-area" @click="uploadFile('image')">
              <el-button type="primary" :loading="uploading">点击上传</el-button>
              <div v-if="form.textbook_url" class="el-upload__tip">
                <el-link type="success" @click.stop="previewImage(form.textbook_url)">
                  已上传，点击查看
                </el-link>
              </div>
              <div class="el-upload__tip">
                支持 JPG/PNG 格式，建议尺寸 3:4
              </div>
            </div>
            <input 
              ref="imageInput" 
              type="file" 
              accept="image/jpeg,image/png" 
              style="display: none"
              @change="handleFileChange($event, 'image')"
            >
          </el-form-item>
          <el-form-item label="教材文件" prop="url">
            <div class="upload-area" @click="uploadFile('pdf')">
              <el-button type="primary" :loading="uploading">点击上传</el-button>
              <div v-if="form.url" class="el-upload__tip">
                <el-link type="success" @click.stop="previewPdf(form.url)">
                  已上传，点击查看
                </el-link>
              </div>
              <div class="el-upload__tip">
                支持 PDF 格式，建议文件小于 50MB
              </div>
            </div>
            <input 
              ref="pdfInput" 
              type="file" 
              accept=".pdf" 
              style="display: none"
              @change="handleFileChange($event, 'pdf')"
            >
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialog.visible = false">取消</el-button>
            <el-button type="primary" @click="submitForm" :loading="dialog.loading">
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 资料对话框 -->
      <el-dialog 
        v-model="materialsDialog.visible" 
        :title="materialsDialog.title" 
        width="40%"
        :close-on-click-modal="false"
      >
        <el-table 
          v-loading="materialsDialog.loading"
          :data="materialsDialog.materials" 
          border 
          style="width: 100%"
        >
          <el-table-column prop="type" label="类型" width="120" />
          <el-table-column prop="title" label="名称" />
          <el-table-column label="操作" width="100" align="center">
            <template #default="{ row }">
              <el-button 
                type="primary" 
                link 
                @click="previewFile(row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>

      <!-- 文件预览对话框 -->
      <el-dialog
        v-model="filePreviewDialog.visible"
        :title="filePreviewDialog.title"
        width="80%"
        top="5vh"
        :close-on-click-modal="false"
        destroy-on-close
      >
        <div v-loading="filePreviewDialog.loading" class="preview-container">
          <!-- Office 文档预览 -->
          <iframe
            v-if="filePreviewDialog.type === 'office'"
            :src="filePreviewDialog.url"
            frameborder="0"
            style="width: 100%; height: 70vh;"
            allowfullscreen
          ></iframe>

          <!-- PDF 预览 -->
          <iframe
            v-else-if="filePreviewDialog.type === 'pdf'"
            :src="filePreviewDialog.url"
            frameborder="0"
            style="width: 100%; height: 70vh;"
          ></iframe>

          <!-- 视频预览 -->
          <div v-else-if="filePreviewDialog.type === 'video'" class="video-preview">
            <video
              controls
              style="width: 100%; max-height: 70vh;"
              :src="filePreviewDialog.url"
            >
              您的浏览器不支持 HTML5 视频标签。
            </video>
          </div>
        </div>
      </el-dialog>

      <!-- 指定资料对话框 -->
      <AssignResourceDialog
        v-model="assignResourceDialog.visible"
        :course-base-id="assignResourceDialog.courseBaseId"
        :course-info-id="assignResourceDialog.courseInfoId"
        :title="assignResourceDialog.title"
        @close="handleAssignResourceClose"
        @save="handleAssignResourceSave"
      />

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.page_size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import { GetJcList, EditJc, addJc, uploadDzs,getCourseList,getJcDatas } from '@/api/dzs'
import AssignResourceDialog from '@/components/course/AssignResourceDialog.vue'

// 加载状态
const loading = ref(false)
// 表格数据
const tableData = ref([])
// 总条数
const total = ref(0)
// 查询参数
const queryParams = reactive({
  page: 1,
  page_size: 10,
  search_key: '',
  order_by: 'id',
  order_type: 'asc'
})

// 上传相关
const uploading = ref(false)

// 资料对话框类型定义
interface MaterialItem {
  type: string;
  title: string;
  url: string;
}

// 图片预览
const imagePreview = reactive({
  visible: false,
  url: ''
})

// PDF预览
const pdfPreview = reactive({
  visible: false,
  url: ''
})

// 对话框
const dialog = reactive({
  visible: false,
  title: '添加教材',
  loading: false
})

// 文件预览对话框
const filePreviewDialog = reactive({
  visible: false,
  title: '文件预览',
  loading: false,
  type: '', // 'office', 'pdf', 'video'
  url: ''
})

// 资料对话框
const materialsDialog = reactive({
  visible: false,
  title: '课程资料',
  loading: false,
  courseId: null as number | null,
  materials: [] as MaterialItem[]
})

// 指定资料对话框
const assignResourceDialog = reactive({
  visible: false,
  title: '指定资料',
  courseBaseId: null as number | null,
  courseInfoId: null as number | null
})

// 表单
const formRef = ref()
interface FormData {
  id?: number | undefined;
  course_base_id?: number | undefined;
  course_code: string;
  course_name: string;
  course_credits: number;
  course_type: string;
  textbook_name: string;
  textbook_editor: string;
  publication_info: string;
  textbook_url: string;
  url: string;
  [key: string]: any;  // This allows string indexing
}

const form = reactive<FormData>({
  id: undefined,
  course_base_id: undefined,
  course_code: '',
  course_name: '',
  course_credits: 2.0,
  course_type: '',
  textbook_name: '',
  textbook_editor: '',
  publication_info: '',
  textbook_url: '',
  url: ''
})

// 表单验证规则
const rules = {
  course_code: [{ required: true, message: '请输入课程代码', trigger: 'blur' }],
  course_name: [{ required: true, message: '请输入课程名称', trigger: 'blur' }],
  textbook_name: [{ required: true, message: '请输入教材名称', trigger: 'blur' }]
}

// 课程列表
const courseList = ref<Array<{id: number; kc_bm: string; kc_mc: string}>>([]);

// 处理课程资料数据
const processCourseMaterials = (data: any): MaterialItem[] => {
  const materials: MaterialItem[] = []
  
  // 添加电子书
  if (data.dzs) {
    for (const dz of data.dzs) {
      materials.push({
        type: '电子书',
        title: dz.title || '电子书',
        url: dz.url
      })
    }
  }
  
  // 添加视频
  if (data.video) {
    for (const video of data.video) {
      materials.push({
        type: '视频',
        title: video.title || '视频资料',
        url: video.url
      })
    }
  }
  
  // 添加试题
  if (data.job) {
    for (const job of data.job) {
      materials.push({
        type: '试题',
        title: job.title || '试题资料',
        url: job.url
      })
    }
  }
  
  // 添加考试大纲
  if (data.ksdg) {
    for (const ksdg of data.ksdg) {
      materials.push({
        type: '考试大纲',
        title: ksdg.title || '考试大纲',
        url: ksdg.url
      })
    }
  }
  
  return materials
}

// 查看资料
const viewMaterials = async (row: any) => {
  if (!row.course_base_id) {
    ElMessage.warning('该课程没有关联的资料')
    return
  }
  
  try {
    materialsDialog.loading = true
    materialsDialog.courseId = row.course_base_id
    materialsDialog.title = `${row.course_name} - 课程资料`
    materialsDialog.visible = true
    
    const res = await getJcDatas({ course_base_id: row.course_base_id,course_jc_id : row.id })
    if (res.code === 200) {
      materialsDialog.materials = processCourseMaterials(res)
    } else {
      ElMessage.error(res.msg || '获取资料失败')
    }
  } catch (error) {
    console.error('获取资料失败:', error)
    ElMessage.error('获取资料失败')
  } finally {
    materialsDialog.loading = false
  }
}

// 获取课程列表
const fetchCourseList = async () => {
  try {
    const res = await getCourseList();
    if (res.code === 200) {
      courseList.value = res.data || [];
    } else {
      ElMessage.error(res.msg || '获取课程列表失败');
    }
  } catch (error) {
    console.error('获取课程列表失败:', error);
    ElMessage.error('获取课程列表失败');
  }
};

// 处理课程选择变化
const handleCourseChange = (courseId: number) => {
  const selectedCourse = courseList.value.find(course => course.id === courseId);
  if (selectedCourse) {
    form.course_code = selectedCourse.kc_bm;
    form.course_name = selectedCourse.kc_mc;
  }
};



// 获取列表数据
const getList = async () => {
  try {
    loading.value = true
    const res = await GetJcList({
      ...queryParams,
      page: queryParams.page,
      page_size: queryParams.page_size
    })
    if (res.code === 200) {
      tableData.value = res.data.list || []
      total.value = res.data.total || 0
    } else {
      ElMessage.error(res.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 处理查询
const handleQuery = () => {
  queryParams.page = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  queryParams.search_key = ''
  queryParams.order_by = 'id'
  queryParams.order_type = 'asc'
  handleQuery()
}

// 处理排序
const handleSortChange = ({ prop, order }: { prop: string, order: string }) => {
  if (prop) {
    queryParams.order_by = prop
    queryParams.order_type = order === 'ascending' ? 'asc' : 'desc'
    getList()
  }
}

// 处理分页大小变化
const handleSizeChange = (val: number) => {
  queryParams.page_size = val
  getList()
}

// 处理当前页变化
const handleCurrentChange = (val: number) => {
  queryParams.page = val
  getList()
}

// 预览图片
const previewImage = (url: string) => {
  imagePreview.url = url
  imagePreview.visible = true
}

// 预览PDF
const previewPdf = (url: string) => {
  pdfPreview.url = url
  pdfPreview.visible = true
}

// 添加
const handleAdd = () => {
  resetForm()
  dialog.title = '添加教材'
  dialog.visible = true
}

// 编辑
const handleEdit = async (row: any) => {
  // Reset form first
  resetForm()
  
  // Ensure course list is loaded
  if (courseList.value.length === 0) {
    await fetchCourseList()
  }
  
  // Copy all properties from row to form
  Object.keys(row).forEach(key => {
    if (row[key] !== undefined) {
      form[key as keyof FormData] = row[key]
    }
  })
  
  // Set the course_base_id to ensure the dropdown shows the correct selection
  if (row.course_base_id) {
    const courseId = Number(row.course_base_id)
    form.course_base_id = courseId
    
    // Find and set the course code and name
    const selectedCourse = courseList.value.find(c => Number(c.id) === courseId)
    if (selectedCourse) {
      form.course_code = selectedCourse.kc_bm
      form.course_name = selectedCourse.kc_mc
    }
  }
  
  dialog.title = '编辑教材'
  dialog.visible = true
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  form.id = undefined
  form.course_base_id = undefined
  form.course_code = ''
  form.course_name = ''
  form.course_credits = 2.0
  form.course_type = ''
  form.textbook_name = ''
  form.textbook_editor = ''
  form.publication_info = ''
  form.textbook_url = ''
  form.url = ''
}

// 触发文件选择
const uploadFile = (type: 'image' | 'pdf') => {
  if (uploading.value) return
  
  const input = type === 'image' ? imageInput.value : pdfInput.value
  if (input) {
    input.value = '' // 清除之前的文件选择
    input.click()
  }
}

// 处理文件选择
const handleFileChange = async (event: Event, type: 'image' | 'pdf') => {
  const input = event.target as HTMLInputElement
  const file = input.files?.[0]
  if (!file) return

  // 验证文件类型和大小
  if (type === 'image') {
    const isImage = file.type === 'image/jpeg' || file.type === 'image/png'
    const isLt5M = file.size / 1024 / 1024 < 5
    
    if (!isImage) {
      ElMessage.error('只能上传 JPG/PNG 格式的图片!')
      return
    }
    if (!isLt5M) {
      ElMessage.error('图片大小不能超过 5MB!')
      return
    }
  } else {
    const isPdf = file.type === 'application/pdf' || file.name.endsWith('.pdf')
    const isLt50M = file.size / 1024 / 1024 < 50
    
    if (!isPdf) {
      ElMessage.error('只能上传 PDF 格式的文件!')
      return
    }
    if (!isLt50M) {
      ElMessage.error('文件大小不能超过 50MB!')
      return
    }
  }

  // 上传文件
  try {
    uploading.value = true
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', type)
    
    const res = await uploadDzs(formData)
    if (res.success === true) {
      const url = res.url
      if (type === 'image') {
        form.textbook_url = url
      } else {
        form.url = url
      }
      ElMessage.success('上传成功')
    } else {
      ElMessage.error(res.msg || '上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败，请重试')
  } finally {
    uploading.value = false
  }
}

// 模板引用
const imageInput = ref<HTMLInputElement | null>(null)
const pdfInput = ref<HTMLInputElement | null>(null)

// 提交表单
const submitForm = async () => {
  try {
    await formRef.value.validate()
    
    dialog.loading = true
    const api = form.id ? EditJc : addJc
    const res = await api(form)
    
    if (res.code === 200) {
      ElMessage.success(form.id ? '更新成功' : '添加成功')
      dialog.visible = false
      getList()
    } else {
      ElMessage.error(res.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    dialog.loading = false
  }
}

// 预览文件
const previewFile = (file: { type: string; url: string; title: string }) => {
  const url = file.url
  const ext = url.split('.').pop()?.toLowerCase() || ''
  
  // 设置预览标题
  filePreviewDialog.title = file.title || '文件预览'
  filePreviewDialog.url = url
  // 判断文件类型
  if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(ext)) {
    filePreviewDialog.type = 'office'
    filePreviewDialog.url = `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(url)}&wdOrigin=BROWSELINK`
  } else if (ext === 'pdf') {
    filePreviewDialog.type = 'pdf'
    filePreviewDialog.url = `https://xczx7.swufe.edu.cn/vue/pdfjs-5.3.31-dist/web/viewer.html?file=${encodeURIComponent(url)}`
  } else if (['mp4', 'webm', 'ogg'].includes(ext)) {
    filePreviewDialog.type = 'video'
  } else {
    // 其他类型直接下载
    window.open(url, '_blank', 'noopener,noreferrer')
    return
  }
  
  filePreviewDialog.visible = true
}

// 移除未使用的函数

// 指定资料
const assignResources = (row: any) => {
  if (!row.course_base_id) {
    ElMessage.warning('该课程没有关联的课程基础信息')
    return
  }

  assignResourceDialog.courseBaseId = row.course_base_id
  assignResourceDialog.courseInfoId = row.id // 这里使用教材记录的ID作为course_info_id
  assignResourceDialog.title = `${row.course_name} - 指定资料`
  assignResourceDialog.visible = true
}

// 处理指定资料对话框关闭
const handleAssignResourceClose = () => {
  assignResourceDialog.visible = false
}

// 处理指定资料保存
const handleAssignResourceSave = () => {
  ElMessage.success('资料指定成功')
  assignResourceDialog.visible = false
}



// 组件挂载时获取数据
onMounted(() => {
  getList();
  fetchCourseList();
})

</script>

<style scoped>
.app-container {
  padding: 20px;
}

.search-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.demo-form-inline {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.upload-area {
  width: 100%;
  cursor: pointer;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
}

.image-error .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.el-upload__tip {
  margin-top: 8px;
  color: #999;
  font-size: 12px;
}

.el-dialog__body {
  padding: 20px;
}

.el-form-item {
  margin-bottom: 18px;
}
</style>