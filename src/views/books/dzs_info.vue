<!--成高电子书内容页-->
<template>
  <Img2Paragraph v-if="is_loading" :is_loading="is_loading"></Img2Paragraph>
  <div v-else>
    <div ref="ref_page_cg_kc_dzs_content"
         :class="show_ctrl.type==='menu'?'ref_page_cg_kc_dzs_content_chapter':'ref_page_cg_kc_dzs_content_point'">
    <TextSpeech>
      <v-md-preview :text="course_dzs_info.content" class="doc_v_md_content"></v-md-preview>
    </TextSpeech>
    </div>
  </div>
  <el-backtop v-if="!is_loading && show_ctrl.type==='menu'"
              target=".ref_page_cg_kc_dzs_content_chapter"
              :right="show_ctrl.ext ? 425 : 5"
              :bottom="5"
              :visibility-height="200">
  </el-backtop>
  <el-backtop v-if="!is_loading && show_ctrl.type==='point'"
              target=".ref_page_cg_kc_dzs_content_point"
              :right="show_ctrl.ext ? 425 : 5"
              :bottom="5"
              :visibility-height="200">
  </el-backtop>
  <div v-if="!is_loading" class="page_cg_kc_dzs_header">
    <div class="">
     <span style="letter-spacing: 5px;
    font-size: 22px;
    color: #00008b;
    font-weight: bold;
    text-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);">
       {{ course_dzs_info.kc_mc }}
     </span>
      <el-icon class="m_r_5"><ArrowRight /></el-icon>
      <span class="info" style="font-size: 14px;">{{ course_dzs_info.plate_title }}</span>
      <el-icon class="m_r_5"><ArrowRight /></el-icon>
      <span class="info" style="font-size: 14px;">{{ course_dzs_info.course_dzs_title }}</span>
    </div>
    <div class="d_flex_e">
      <el-radio-group v-model="show_ctrl.type" size="small">
        <el-radio-button label="menu">按目录结构<span v-if="pars.num_chapter>0">（{{ pars.num_chapter }}章）</span>
        </el-radio-button>
        <el-radio-button label="point">按知识点<span v-if="pars.num_point>0">（{{ pars.num_point }}个）</span>
        </el-radio-button>
      </el-radio-group>
      <el-tooltip :content="show_ctrl.ext?'点击可关闭列表':'点击可开启列表'" placement="top">
        <el-tag class="hand" @click="clickShowExt" size="large">
          <el-icon>
            <component :is="show_ctrl.ext ? 'ArrowRight' : 'ArrowLeft'" />
          </el-icon>
        </el-tag>
      </el-tooltip>
    </div>
  </div>
  <div v-if="!is_loading && show_ctrl.ext && show_ctrl.type==='menu'" class="menu_ls">
    <el-input style="width: 410px;position: fixed;z-index: 2;right: 10px;top:60px;" size="default"
             v-model="pars.key_menu" clearable placeholder="可输入章名称关键字后,回车查询"
             @keyup.enter="changeMenuLsFilter"
             @clear="changeMenuLsFilter">
      <template #prefix>
        <el-icon><Search /></el-icon>
      </template>
    </el-input>

    <div class="w100 d_flex_s flex_w gap_25">
      <div v-for="lv1 in menu_ls_filter" :key="lv1.title" class="w100">
        <div class="w100 d_flex_s flex_w_un gap_10">
          <div class="c_page_menu" style="display: flex;align-items: center;">
            <el-icon class="hand" @click="lv1.is_ext=!lv1.is_ext" style="font-size: 18px;font-weight: bold;">
            <component :is="lv1.is_ext ? 'ArrowDown' : 'ArrowRight'" />
          </el-icon>
          <span :title="lv1.title"
                :class="menu_item_curr && menu_item_curr===lv1.lineIndex?'warning hand flex_ellipsis':'hand flex_ellipsis'"
                style="font-size: 18px;font-weight: bold;"
                @click="scrollToLine(lv1.lineIndex)">
              <v-md-preview :text="lv1.title"></v-md-preview>
          </span>
          </div>
        </div>
        <div v-if="lv1.children && lv1.children.length>0 && lv1.is_ext" style="margin-top: 15px;padding-left: 15px;"
             class="w100 d_flex_s flex_w gap_15">
          <div v-for="lv2 in lv1.children" :key="lv2.title" class="w100">
            <div class="w100 d_flex_s flex_w_un gap_10">
              <div class="c_page_menu" style="display: flex;align-items: center;">
                <el-icon class="m_r_5 hand" @click="lv2.is_ext=!lv2.is_ext" style="font-size: 16px;font-weight: bold;">
                <component :is="lv2.is_ext ? 'ArrowDown' : 'ArrowRight'" />
              </el-icon>
              <span :title="lv2.title"
                    :class="menu_item_curr && menu_item_curr===lv2.lineIndex?'warning hand flex_ellipsis':'hand flex_ellipsis'"
                    style="font-size: 16px;font-weight: bold;"
                    @click="scrollToLine(lv2.lineIndex)">
                  <v-md-preview :text="lv2.title"></v-md-preview>
              </span>
              </div>
            </div>
            <div v-if="lv2.children && lv2.children.length>0 && lv2.is_ext" style="margin-top: 15px;padding-left: 15px;"
                 class="w100 d_flex_s flex_w gap_10">
              <div v-for="lv3 in lv2.children" :key="lv3.title" class="w100 d_flex_s flex_w gap_5">
                <div class="w100 d_flex_s flex_w_un gap_5 info">
                  <div class="c_page_menu" style="display: flex;align-items: center;">
                    <el-icon style="font-size: 14px;" class="primary">
                    <Star />
                  </el-icon>
                  <span :title="lv3.title"
                        :class="menu_item_curr && menu_item_curr===lv3.lineIndex?'warning hand flex_ellipsis':'hand flex_ellipsis'"
                        style="font-size: 14px;"
                        @click="scrollToLine(lv3.lineIndex)">
                      <v-md-preview :text="lv3.title"></v-md-preview>
                  </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-if="!is_loading && show_ctrl.ext && show_ctrl.type==='point'" class="point_ls">
    <el-input style="width: 410px;position: fixed;z-index: 2;right: 10px;top:60px;" size="default"
             v-model="pars.key_point" clearable placeholder="可输入知识点关键字后,回车查询" @keyup.enter="changePointLsFilter"
             @clear="changePointLsFilter">
      <template #prefix>
        <el-icon><Search /></el-icon>
      </template>
    </el-input>
    <el-tag v-for="item in point_ls_filter" :key="item" @click="scrollToFirstMatch(item)" class="hand"
           :type="point_item_curr===item?'warning':'info'">
      {{ item }}
    </el-tag>
  </div>
</template>
<script setup lang="ts">
import {getCurrentInstance, onMounted, ref, onUnmounted, nextTick} from "vue";
import {useRoute, useRouter} from "vue-router";
import {msgShow} from "@/utils/message";
import { ArrowRight, ArrowLeft, ArrowDown, Search, Star } from '@element-plus/icons-vue'
import {toolsUtils} from "@/utils/tools";
import {
  I_course_dzs_info, I_course_dzs_menu, I_markdown_menu
} from "@/utils/types";
import Img2Paragraph from '@/components/loading/img_2_paragraph.vue';
import {watchThrottled} from "@vueuse/core";
import { CourseDzsInfo,CourseDzsInfoByChapter } from '@/api/dzs';
import TextSpeech from '@/components/TextSpeech.vue'

const pars = ref({
  id: '',
  key_menu: '',
  key_point: '',
  kc_mc: '课程名称xxx',
  kc_bm: '',
  num_chapter: 0,
  num_point: 0
})
const course_dzs_info = ref({} as I_course_dzs_info)
const menu_ls = ref([] as I_course_dzs_menu[])
const point_ls = ref([] as string[])
const menu_ls_filter = ref([] as I_course_dzs_menu[])
const point_ls_filter = ref([] as string[])
const is_loading = ref(true)
const show_ctrl = ref({ext: true, type: 'menu'})
const ref_page_cg_kc_dzs_content = ref<HTMLElement | null>(null)
const ref_v_md = ref()
const menu_item_curr = ref('')
const point_item_curr = ref('')

watchThrottled(
    show_ctrl,
    () => {
      console.log('show_ctrl', show_ctrl.value.ext, show_ctrl.value.type)
      if (ref_page_cg_kc_dzs_content.value) {
        if (show_ctrl.value.ext) {
          ref_page_cg_kc_dzs_content.value.style.width = show_ctrl.value.type === 'menu' ? `calc(100% - 420px)` : `calc(100% - 420px)`
        } else {
          ref_page_cg_kc_dzs_content.value.style.width = `100%`
        }
        console.log('ref_page_cg_kc_dzs_content.value', ref_page_cg_kc_dzs_content.value)
      }
    },
    {throttle: 500, deep: true}
)
onMounted(() => {
  // const route = useRoute()
  // if (route.query.course_plate_info_id) {
  //   if (route.query.course_plate_info_id.length > 10) {
  //     pars.value.course_plate_info_id = toolsUtils.replaceSpace(route.query.course_plate_info_id.toString())
  //     reloadData()
  //   } else {
  //     msgShow("仅支持加密后的数据传递", "error")
  //   }
  // } else {
  //   msgShow("未获取到课程参数", "error")
  // }
  const route = useRoute()
  const id:any = route.query.id
  pars.value.id = id
  reloadData()
})

onUnmounted(() => {
})

const hideLoading = () => {
  setTimeout(() => {
    is_loading.value = false
    setTimeout(() => {
      getMenuLs()
    }, 500)
  }, 360)
}
const reloadData = () => {
  getCourseDzsInfo()
}
const getCourseDzsInfo = () => {
  is_loading.value = true
  course_dzs_info.value = {} as I_course_dzs_info
  var param = {
    id: pars.value.id
  }
  CourseDzsInfo(param).then(res => {
    const {code, msg, data} = res
    if (code.toString() === '200') {
      course_dzs_info.value = data
      pars.value.kc_bm = course_dzs_info.value.kc_bm
      pars.value.kc_mc = course_dzs_info.value.kc_mc
      point_ls.value = course_dzs_info.value.zsd
      point_ls_filter.value = point_ls.value
      menu_ls.value = course_dzs_info.value.menu
      menu_ls_filter.value = menu_ls.value
      hideLoading()
    } else {
      hideLoading()
      msgShow(msg, "warning")
    }
  }).catch(err => {
    hideLoading()
    msgShow("获取电子书信息出错啦。", "error")
  })
}
const scrollToFirstMatch = (text: string) => {
  point_item_curr.value = text
  toolsUtils.scrollToFirstMatch(text)
}

const clickShowExt = () => {
  show_ctrl.value.ext = !show_ctrl.value.ext
}
const getMenuLs = async () => {
  await nextTick()
  let doc_v_md_content = document.querySelector('.doc_v_md_content') as HTMLElement | null
  if (doc_v_md_content) {
    menu_ls.value= toolsUtils.generateMarkdownToc(doc_v_md_content)
    menu_ls_filter.value = menu_ls.value
  }
}
const scrollToLine = (lineIndex: string) => {
  if (lineIndex) {
    menu_item_curr.value = lineIndex
    let doc_v_md_content = document.querySelector('.doc_v_md_content') as HTMLElement | null
    if (doc_v_md_content) {
      let nav_2_dom = doc_v_md_content.querySelector(`[data-v-md-line="${lineIndex}"]`)
      if (nav_2_dom) {
        nav_2_dom.scrollIntoView({
          behavior: 'smooth',
        })
        toolsUtils.highlightMatch(nav_2_dom)
      }
    }
  } else {
    menu_item_curr.value = ''
    msgShow('抱歉，未找到定位节点', 'warning')
  }
}

const filterMenuByTitle = (menuItems: I_course_dzs_menu[], key: string): I_course_dzs_menu[] => {
  return menuItems.reduce((result: I_course_dzs_menu[], item: I_course_dzs_menu) => {
    // 检查当前节点是否包含关键词
    const hasKeyword = item.title.includes(key);

    // 递归过滤子节点
    const filteredChildren = item.children
        ? filterMenuByTitle(item.children, key)
        : [];

    // 如果当前节点包含关键词或有子节点包含关键词，则保留该节点
    if (hasKeyword || filteredChildren.length > 0) {
      // 创建节点的浅拷贝，避免修改原始数据
      const filteredItem:any = {...item};

      // 如果有子节点包含关键词，更新子节点列表
      if (filteredChildren.length > 0) {
        filteredItem.children = filteredChildren;
      } else {
        // 如果没有子节点包含关键词且当前节点不包含关键词，则不保留子节点
        delete filteredItem.children;
      }

      result.push(filteredItem);
    }

    return result;
  }, []);
};

const changeMenuLsFilter = () => {
  menu_ls_filter.value = filterMenuByTitle(menu_ls.value, pars.value.key_menu);
}
const changePointLsFilter = () => {
  point_ls_filter.value = point_ls.value.filter(f => f.includes(pars.value.key_point))
}

</script>
<style scoped lang="scss">
.page_cg_kc_dzs_header {
  z-index: 2;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 60px;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
  padding: 5px;
  background: linear-gradient(180.00deg, rgb(200, 222, 251) 26.148%, rgb(182, 205, 255) 100%);

  .kc_mc {

  }
}

.ref_page_cg_kc_dzs_content_chapter {
  width: calc(100% - 420px);
  margin-top: 60px;
  height: calc(100vh - 60px);
  overflow-y: auto;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  align-content: flex-start;
  gap: 25px;
  //padding: 25px;
  padding-right: 25px;
  /* 隐藏 WebKit 浏览器的滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;
  transition: all 0.3s ease;
}

.ref_page_cg_kc_dzs_content_point {
  width: calc(100% - 420px);
  margin-top: 60px;
  height: calc(100vh - 60px);
  overflow-y: auto;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  align-content: flex-start;
  gap: 25px;
  //padding: 25px;
  padding-right: 25px;
  /* 隐藏 WebKit 浏览器的滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;
  transition: all 0.3s ease;
}

.menu_ls {
  z-index: 2;
  position: fixed;
  right: 0;
  top: 60px;
  width: 420px;
  height: calc(100% - 60px);
  overflow-x: hidden;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  align-content: flex-start;
  gap: 15px;
  padding: 15px;
  padding-top: 50px;
  flex-direction: row;
  box-shadow: 2px 1px 10px rgba(255, 255, 255, 0.1), -2px 1px 10px rgba(111, 255, 255, 0.1), 0 3px 10px rgba(0, 0, 0, 0.1);
  background: linear-gradient(180.00deg, rgb(200, 222, 251) 26.148%, rgb(182, 205, 255) 100%);

  .menu_item {
    width: 100%;
    height: auto;
    background-color: #fff;
    border-radius: 10px;
    padding: 15px;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
    cursor: pointer;
    gap: 5px;

  }

}

.point_ls {
  z-index: 2;
  position: fixed;
  right: 0;
  top: 60px;
  width: 420px;
  height: calc(100% - 60px);
  overflow-x: hidden;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  align-content: flex-start;
  gap: 10px;
  padding: 50px 15px 15px;
  flex-direction: row;
  box-shadow: 2px 1px 10px rgba(255, 255, 255, 0.1), -2px 1px 10px rgba(111, 255, 255, 0.1), 0 3px 10px rgba(0, 0, 0, 0.1);
  background: linear-gradient(180.00deg, rgb(200, 222, 251) 26.148%, rgb(182, 205, 255) 100%);

  .point_item {
    width: auto;
    height: auto;
    background-color: #fff;
    border-radius: 5px;
    padding: 5px 10px;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
    cursor: pointer;
    gap: 5px;

  }

}

.item_curr {
  box-shadow: 2px 1px 10px #1B23FF;
  color: #409EFF;
}

:deep(.c_page_menu) {
  .github-markdown-body {
    padding: 0 !important;
    font-family: "JetBrains Mono", Inter, system-ui, Avenir, Helvetica, Arial, sans-serif !important;
    //font-size: 14px!important;
    p {
      padding: 0 !important;
      margin: 0 !important;
    }
  }

}

:deep(.t-layout) {
  background: none;
}

:deep(.t-layout__content) {
  background: none;
}

:deep(.t-row) {
  background: none;
}

:deep(.t-tabs) {
  background: none;
}

:deep(.katex-html) {
  display: none;
}
</style>
