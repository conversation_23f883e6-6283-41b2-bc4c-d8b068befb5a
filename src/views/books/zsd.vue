<template>
  <div class="books_zsd page">
    <div class="header"> 
      <el-button type="success" plain size="default" icon="refresh" round @click="getData"></el-button>
      <el-input v-model="userData.keySearch" placeholder="知识点" size="default" style="width: 260px;margin:0 10px;" clearable @change="getData">
        <template #append>
          <el-button icon="Search" @click="getData"/>
        </template>
      </el-input>
      <el-tag class="el_tag_5">课程教材</el-tag>
      <el-select v-model="userData.course_jc_id"
                 placeholder="请选择教材课程"
                 clearable
                 filterable
                 style="width: 200px;"
                 @change="getData">
        <el-option
            v-for="item in courseJcList"
            :key="item.id"
            :label="item.course_name"
            :value="item.id">
              <span style="float: left">{{ item.course_name }}</span>
              <span style="float: right; color: #67C23A; font-size: 13px;">{{ item.course_code }}</span>
        </el-option>
      </el-select> 
      <el-button type="primary" 
                 size="small" 
                 @click="handleUpload"
                 :disabled="userData.dataZsdTable.length>0"
                 icon="upload">
                 {{userData.dataZsdTable.length>0?'当前课程已有文档不能新增上传只能重传':'上传知识点关系表'}}
      </el-button>
    </div>
    <div class="body">
      <el-table
        :data="userData.dataZsdTable"
        border
        class="modzsdTable"
        height="150"
        style="width: 100%;  margin-bottom: 20px;"
        v-loading="userData.loading"
      >
        <el-table-column prop="" type="index" label="序号"  align="center" width="60" header-align="center" />
        <el-table-column prop="title" label="标题" min-width="200" align="left" header-align="center" show-overflow-tooltip/>
        <el-table-column prop="url" label="文档URL" min-width="200" align="left" header-align="center" show-overflow-tooltip/>
        <el-table-column prop="course_code" label="课程编码" width="80" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{ getCoursecode(scope.row.course_jc_id) }}
            </template>
        </el-table-column>
        <el-table-column prop="course_name" label="课程名称" min-width="120" align="left" header-align="center">
          <template #default="scope">
              {{ getCourseName(scope.row.course_jc_id) }}
            </template>
        </el-table-column>
        <el-table-column prop="create_date" label="创建时间" min-width="120" align="center" header-align="center" />
        <el-table-column prop="create_user" label="创建人" min-width="120" align="center" header-align="center"/>
        <el-table-column label="执行状态" align="center" width="100" header-align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.execute_status)">
              {{ getStatusText(scope.row.execute_status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" header-align="center">
          <template #default="scope">
            <el-button size="small" 
                       type="primary" 
                       icon="magic-stick"
                       v-if="scope.row.execute_status !== 'Success'"
                       :disabled="!!(scope.row.execute_id && scope.row.execute_status === 'Running')"
                       @click="handleGetAI(scope.row)">文档读取
            </el-button>
            <el-button size="small" 
                       type="warning" 
                       icon="edit"
                       @click="handleEdit(scope.row)">重传
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-table
        :data="userData.dataTable"
        border
        class="modTable"
        :height="userData.tableHeight"
        style="width: 100%;"
        v-loading="userData.loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column prop="" type="index" label="序号"  align="center" width="60" header-align="center" />
        <el-table-column prop="linked_knowledge_point_code" label="知识点编码" width="80" align="center" header-align="center" show-overflow-tooltip/>
        <el-table-column prop="linked_knowledge_point_name" label="知识点" min-width="120" align="left" header-align="center" show-overflow-tooltip/>
        <el-table-column prop="node_code" label="节点编码" width="80" align="center" header-align="center"/>
        <el-table-column prop="node_name" label="节点名称" min-width="120" align="left" header-align="center"/>
        <el-table-column prop="chapter_code" label="章节编码" width="80" align="center" header-align="center"/>
        <el-table-column prop="chapter_name" label="章节名称" min-width="120" align="left" header-align="center"/>
        <el-table-column prop="course_code" label="课程编码" width="80" align="center" show-overflow-tooltip/>
        <el-table-column prop="course_name" label="课程名称" min-width="120" align="left" header-align="center">
          <template #default="scope">
              {{ getCourseName(scope.row.course_jc_id) }}
            </template>
        </el-table-column>
        <el-table-column prop="create_date" label="创建时间" min-width="120" align="center" header-align="center"/>
        <el-table-column prop="create_user" label="创建人" min-width="120" align="center" header-align="center"/>
      </el-table>
      <div style="margin-top: 16px; text-align: right;" class="div_pagination">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="userData.total"
          :page-size="userData.pageSize"
          :current-page="userData.currentPage"
          :page-sizes="[20, 50, 100, 500, userData.total]"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    
    <!-- 上传知识点关系表对话框 -->
    <el-dialog
        v-model="uploadDialog.visible"
        title="上传知识点关系表"
        width="600px"
        :close-on-click-modal="false"
        :close-on-press-escape="!uploadDialog.uploading">
      <el-form :model="uploadDialog.form" label-width="140px">
        <el-form-item label="所属课程教材" required>
          <el-select v-model="uploadDialog.form.course_id" 
                    placeholder="请选择课程教材" 
                    filterable
                    style="width: 300px">
            <el-option
                v-for="item in courseJcList"
                :key="item.id"
                :label="item.course_name"
                :value="item.id">
                  <span style="float: left">{{ item.course_name }}</span>
                  <span style="float: right; color: #67C23A; font-size: 13px;">{{ item.course_code }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Excel导入模板">
            <el-link type="primary" href="https://cdn8a.swufe-online.com/oc/mfile/public-comfyui/62383b90-b3fc-4c31-9e87-7704b1d8ace4.xlsx">下载知识点关系表模板</el-link>
        </el-form-item>
        <el-form-item label="知识点关系表文件" required>
          <el-upload
              class="upload-demo"
              drag
              ref="upload"
              :show-file-list="false"
              action="#"
              :auto-upload="false"
              :on-change="beforeUpload"
              :limit="1"
              :multiple="false"
              :disabled="uploadDialog.uploading"
              :on-exceed="handleExceed"
              accept=".xlsx">
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip" style="color: red;">
                仅支持 xlsx 格式！请严格按照模板上传
              </div>
            </template>
          </el-upload>
          <div class="upload-file-info" v-if="uploadDialog.form.file">
            已选择: {{ uploadDialog.form.file.name }} ({{ (uploadDialog.form.file.size / (1024 * 1024)).toFixed(2) }}MB)
          </div>
        </el-form-item>
        <el-form-item v-if="uploadDialog.uploading">
          <el-progress :percentage="uploadDialog.progress" :format="formatProgress" />
          <div class="upload-status">正在上传中，请勿关闭窗口...</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialog.visible = false" :disabled="uploadDialog.uploading">取消</el-button>
          <el-button type="primary" @click="submitUpload" :loading="uploadDialog.uploading" :disabled="uploadDialog.uploading">{{ uploadDialog.uploading ? '上传中...' : '确定' }}</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { GetQuestionBankPage} from '@/api/question'
import { uploadKnowledgeZsd,getSelectCourseJcList ,getCourseKnowledgeNodePage,setKnowledgeNodeData,importKnowledgeNodeFromExcel } from '@/api/dzs';
import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'
import { genFileId } from 'element-plus'
import { msgShow } from '@/utils/message'; 

const userData = reactive({
  loading: false,
  tableHeight: window.innerHeight - 370,
  keySearch: '',
  course_jc_id:5,
  dataTable: [] as any,
  dataZsdTable: [] as any,
  total: 0,
  pageSize: 30,
  currentPage: 1
}) 
const handlecurrentRow = ref([]) as any
const uploadDialog = reactive({
  visible: false,
  progress: 0,
  uploading: false,
  form: {} as any
})

// 课程列表
const courseJcList = ref([]) as any

// 获取课程列表
const getCourseJcListData = async () => {
  try {
    const res = await getSelectCourseJcList()
    if (res.code === 200) {
      courseJcList.value = res.data
      getData()
    } else {
      msgShow(res.msg || '获取课程列表失败', 'warning')
    }
  } catch (error) {
    msgShow('获取课程列表失败', 'error')
  }
}

const getData = () => {
  userData.loading = true
  const pars = {
    keySearch: userData.keySearch,
    course_jc_id: userData.course_jc_id,
    currentPage: userData.currentPage,
    pageSize: userData.pageSize,
  }
  getCourseKnowledgeNodePage(pars).then((msg: any) => {
    userData.dataTable = msg.data || []
    userData.dataZsdTable = msg.data_zsd || []
    
    userData.total = msg.total.count || userData.dataTable.length
    userData.loading = false
  }).catch((err: any) => {
    console.log(err)
    userData.loading = false
  })
}
const getCourseName = (courseId: number | null) => {
  if (!courseId) return '-'
  const course = courseJcList.value.find((c:any) => Number(c.id) === Number(courseId))
  return course?.course_name || '-'
}
const getCoursecode = (courseId: number | null) => {
  if (!courseId) return '-'
  const course = courseJcList.value.find((c:any) => Number(c.id) === Number(courseId))
  return course?.course_code || '-'
}

// 处理上传按钮点击
const handleUpload = async () => {
  uploadDialog.visible = true
  uploadDialog.form = {
    id:0,
    title: '',
    file: null,
    course_id: userData.course_jc_id,
    url_change: 0
  }
}
const upload = ref<UploadInstance>()
const formatProgress = (p: number) => p + '%'
const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}

const beforeUpload = (event: any) => {
  const file = event.raw || event.file
  const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

  if (!isXLSX) {
    msgShow('只能上传xlsx 格式的文件！', 'warning')
    return false
  }
  uploadDialog.form.file = file
  // 将文件名（不含扩展名）设置为标题
  uploadDialog.form.title = file.name.split('.')[0]
  return false // 阻止自动上传
}

// 提交上传
const submitUpload = async () => {
  if (!uploadDialog.form.title) {
    msgShow('请输入标题', 'warning')
    return
  }
  if (!uploadDialog.form.file) {
    msgShow('请选择要上传的文件', 'warning')
    return
  }
  if (!uploadDialog.form.course_id) {
    msgShow('请选择所属教材课程', 'warning')
    return
  } 
  uploadDialog.uploading = true
  uploadDialog.progress = 0
  
  try {
    const formData = new FormData()
    formData.append('file', uploadDialog.form.file)
    // 上传文件
    // const uploadRes = await uploadDzs(formData)

    uploadKnowledgeZsd(formData).then((res: any) => {
      if (res.success) {
          setKnowledgeData(res)
      } else {
        msgShow('文件上传失败', 'error')
      }
      userData.loading = false
    }).catch((err: any) => {
      console.log(err) 
    }) 
  } catch (error) {
    msgShow('上传失败', 'error')
  } finally {
    uploadDialog.uploading = false
    uploadDialog.progress = 0
  }
}
//保存数据
const setKnowledgeData=(uploadRes:any)=>{ 
  const prem = {
      id:uploadDialog.form.id,
      course_jc_id: uploadDialog.form.course_id,
      title: uploadDialog.form.title,
      url: uploadRes.url,
      url_change:uploadDialog.form.url_change
  }
  setKnowledgeNodeData(prem).then((saveRes: any) => {
       if (saveRes.code === 200) {
          msgShow('上传成功', 'success')
          uploadDialog.visible = false
          getData()
        } else {
          msgShow(saveRes.msg || '保存数据失败', 'error')
        }
      userData.loading = false
    }).catch((err: any) => {
      console.log(err) 
    })
}

// 处理AI获取
const handleGetAI = async (row: any) => {
  try {
    userData.loading = true
    const data = {
      url: row.url,
      course_jc_id: row.course_jc_id,
      id: row.id
    }
    importKnowledgeNodeFromExcel(data).then((res: any) => {
      if (res.code === 200) {
        msgShow('导入数据库！'+res.data+'条', 'success')
        getData()
      } else {
        msgShow(res.msg || 'AI内容获取失败', 'warning')
      }
      userData.loading = false
    }).catch((err: any) => {
      console.log(err) 
    })
  } catch (error) {
    msgShow('AI内容获取失败', 'error')
  }
}
// 处理编辑按钮点击
const handleEdit = (row: any) => {
  uploadDialog.visible = true
  uploadDialog.form = {
    id:row.id,
    title: '',
    file: null,
    course_id: row.course_jc_id,
    url_change:1
  }
}

// 获取状态类型
const getStatusType = (status: string | undefined) => {
  switch (status) {
    case 'Success':
      return 'success'
    case 'Fail':
      return 'danger'
    case 'Running':
      return 'warning'
    default:
      return 'info'
  }
}
// 获取状态文本
const getStatusText = (status: string | undefined) => {
  switch (status) {
    case 'Success':
      return '成功'
    case 'Fail':
      return '失败'
    case 'Running':
      return '执行中'
    default:
      return '未执行'
  }
}
const handleSelectionChange = (rows: any) => {
  handlecurrentRow.value = rows
}
const handleSizeChange = (size: number) => {
  userData.pageSize = size
  getData()
}

const handlePageChange = (page: number) => {
  userData.currentPage = page
  getData()
}


const calcTableHeight = () => {
  // 这里的220可根据实际页面头部、分页等高度调整
  userData.tableHeight = window.innerHeight - 370;
}

onMounted(() => {
  getCourseJcListData()
  calcTableHeight();
  window.addEventListener('resize', calcTableHeight);
});

onUnmounted(() => {
  window.removeEventListener('resize', calcTableHeight);
});


</script>

<style lang="scss"> 

</style>
