<template>
  <div class="page">
    <div class="header">
      <el-button plain
                 type="success"
                 size="small"
                 @click="reloadData"
                 icon="refresh">刷新
      </el-button>
      <el-select v-model="init_data.selected_course_id"
                 placeholder="请选择课程"
                 clearable
                 filterable
                 size="small"
                 style="width: 260px;"
                 @change="handleCourseChange">
        <el-option
            v-for="item in courseList"
            :key="item.id"
            :label="`${item.kc_bm} ${item.kc_mc}`"
            :value="item.id">
        </el-option>
      </el-select>

    </div>
    <div class="body">
      <el-table
          :data="dzs_ls_local"
          width="100%"
          height="calc(100vh - 135px)"
          v-loading="init_data.pageLoading"
          ref="multipleTable"
          highlight-current-row
          size="small"
          border
          :element-loading-text="init_data.pageLoadingText"
          :element-loading-spinner="init_data.pageLoadingSvg"
          element-loading-svg-view-box="-10, -10, 50, 50"
          element-loading-background="rgba(122, 122, 122, 0.8)"
          :row-style="tableRowStyle"
          :cell-style="tableCellStyle"
      >
        <el-table-column label="ID"
                         align="center"
                         width="45"
                         header-align="center"
                         prop="id">
        </el-table-column>
        <el-table-column label="课程名称"
                         align="left"
                         min-width="100"
                         header-align="center"
                         show-overflow-tooltip>
          <template #default="scope">
            {{ getCourseName(scope.row.course_base_id) }}
          </template>
        </el-table-column>
        <el-table-column label="标题"
                         align="left"
                         min-width="200"
                         header-align="center"
                         show-overflow-tooltip
                         prop="title">
        </el-table-column>
        <el-table-column label="知识点标记"
                         align="left"
                         min-width="100"
                         header-align="center"
                         show-overflow-tooltip
                         prop="zsd_key">
        </el-table-column>
        <el-table-column label="内容"
                         align="center"
                         width="80"
                         header-align="center">
          <template #default="scope">
            <el-tooltip content="关闭时自动保存" placement="top">
              <el-button v-if="scope.row.content" 
                         size="small" 
                         type="primary" 
                         @click="viewContent('内容', scope.row.content, scope.row)">
                编辑
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="目录"
                         align="center"
                         width="80"
                         header-align="center">
          <template #default="scope">
            <el-tooltip content="关闭时自动保存" placement="top">
              <el-button v-if="scope.row.menu" 
                         size="small" 
                         type="primary" 
                         @click="viewContent('目录', scope.row.menu, scope.row)">
                编辑
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="知识点"
                         align="center"
                         width="80"
                         header-align="center">
          <template #default="scope">
            <el-tooltip content="关闭时自动保存" placement="top">
              <el-button v-if="scope.row.zsd" 
                         size="small" 
                         type="primary" 
                         @click="viewContent('知识点', scope.row.zsd, scope.row)">
                编辑
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
                         label="对比"
                         align="center"
                         width="100"
                         header-align="center">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              :disabled="!scope.row.url"
              @click="openCompareDialog(scope.row)"
            >
              原文件对比
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="创建时间"
                         align="center"
                         width="200"
                         header-align="center"
                         prop="create_date">
        </el-table-column>
        <el-table-column label="创建人"
                         align="center"
                         width="100"
                         header-align="center"
                         prop="create_user">
        </el-table-column>
        <el-table-column label="执行状态"
                         align="center"
                         width="100"
                         header-align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.execute_status)">
              {{ getStatusText(scope.row.execute_status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>


  </div>
  
  <!-- 内容预览对话框 -->
  <PreviewDzs 
    v-if="previewType === 'content'"
    v-model="previewVisible"
    :id="currentItemId || 0"
    :title="previewTitle"
    :content="previewContent"
    @close="previewVisible = false"
    @save="handleContentSave"
  />
  
  <!-- 菜单预览对话框 -->
  <PreviewDzsMenu
    v-else-if="previewType === 'menu'"
    v-model="previewVisible"
    :id="currentItemId || 0"
    :title="previewTitle"
    :content="previewContent"
    @close="previewVisible = false"
    @save="handleMenuSave"
  />
  
  <!-- 知识点预览对话框 -->
  <PreviewDzsZsd
    v-else-if="previewType === 'zsd'"
    v-model="previewVisible"
    :id="currentItemId || 0"
    :title="previewTitle"
    :content="previewContent"
    @close="previewVisible = false"
    @save="handleZsdSave"
  />
  
  <!-- 文档对比对话框 -->
  <CompareDocDialog
    v-model="compareDialogVisible"
    :content="compareContent"
    :doc-url="compareDocUrl"
    @close="handleCompareClose"
  />
</template>

<script setup lang='ts'>
// 注册组件
const PreviewDzs = defineAsyncComponent(() => import('@/components/dzs/preview_dzs.vue'))
const PreviewDzsMenu = defineAsyncComponent(() => import('@/components/dzs/preview_dzs_menu.vue'))
const PreviewDzsZsd = defineAsyncComponent(() => import('@/components/dzs/preview_dzs_zsd.vue'));
import { UpdateDzsContent, UpdateDzsMenu, UpdateDzsZsd } from '@/api/dzs';
import { ref, onMounted, defineAsyncComponent } from 'vue';
const CompareDocDialog = defineAsyncComponent(() => import('@/components/dzs/CompareDocDialog.vue'));
import { ElMessage } from 'element-plus'
import { getVideoList, getCourseList, getContentFromCoze, checkContentFromCoze } from '@/api/dzs';
import { msgShow } from '@/utils/message';
import axios from 'axios'
import { genFileId } from 'element-plus'
import { useRouter } from 'vue-router';

interface DzsItem {
  id: number
  course_base_id: number
  title: string
  url: string
  zsd_key: string
  create_date: string
  create_user: string
  content?: string
  menu?: string
  zsd?: string
  execute_id?: string
  execute_status?: string
}

// 状态检查队列
interface StatusCheckItem {
  executeId: string
  row: DzsItem
}

const dzs_ls_local = ref<DzsItem[]>([])
const init_data = ref({
  pageLoading: false,
  pageLoadingText: "获取数据中，请稍后...",
  pageLoadingSvg: `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `,
  selected_course_id: null as number | null
})
const router = useRouter();



// 课程列表
const courseList = ref<Array<{id: number, kc_bm: string, kc_mc: string}>>([])

// 获取课程列表
const getCourseListData = async () => {
  try {
    const res = await getCourseList()
    if (res.code === 200) {
      courseList.value = res.data
    } else {
      msgShow(res.msg || '获取课程列表失败', 'warning')
    }
  } catch (error) {
    msgShow('获取课程列表失败', 'error')
  }
}

// 表格样式
const tableRowStyle = () => {
  return {
    height: '40px'
  }
}

const tableCellStyle = () => {
  return {
    padding: '4px 0'
  }
}

// 获取课程名称
const getCourseName = (courseId: number | null) => {
  if (!courseId) return '-'
  const course = courseList.value.find(c => Number(c.id) === Number(courseId))
  return course?.kc_mc || '-'
}

// 处理内容保存
const handleContentSave = (content: string) => {
  // 更新本地数据
  if (currentItemId.value) {
    const item = dzs_ls_local.value.find(item => item.id === currentItemId.value);
    if (item) {
      item.content = content;
    }
  }
  ElMessage.success('保存成功');
};

// 处理菜单保存
const handleMenuSave = (content: string) => {
  // 更新本地数据
  if (currentItemId.value) {
    const item = dzs_ls_local.value.find(item => item.id === currentItemId.value);
    if (item) {
      item.menu = content;
    }
  }
  ElMessage.success('保存成功');
};

// 处理知识点保存
const handleZsdSave = (content: string) => {
  // 更新本地数据
  if (currentItemId.value) {
    const item = dzs_ls_local.value.find(item => item.id === currentItemId.value);
    if (item) {
      item.zsd = content;
    }
  }
  ElMessage.success('保存成功');
};

// 加载数据
const reloadData = async () => {
  init_data.value.pageLoading = true
  try {
    // 先获取课程列表
    await getCourseListData()
    // 再获取电子书列表
    const params = {
      course_id: init_data.value.selected_course_id
    };
    const res = await getVideoList(params)
    if (res.code === 200) {
      dzs_ls_local.value = res.data
    } else {
      msgShow(res.msg || '获取电子书列表失败', 'warning')
    }
  } catch (error) {
    msgShow('获取数据失败', 'error')
  } finally {
    init_data.value.pageLoading = false
  }
}

// 处理课程选择变化
const handleCourseChange = () => {
  reloadData()
}





// 获取状态类型
const getStatusType = (status: string | undefined) => {
  switch (status) {
    case 'Success':
      return 'success'
    case 'Fail':
      return 'danger'
    case 'Running':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string | undefined) => {
  switch (status) {
    case 'Success':
      return '成功'
    case 'Fail':
      return '失败'
    case 'Running':
      return '执行中'
    default:
      return '未执行'
  }
}

// 查看内容
const previewVisible = ref(false);
const previewTitle = ref('');
const previewContent = ref('');
const previewType = ref<'content' | 'menu' | 'zsd'>('content');
const currentItemId = ref<number | null>(null);

// 文档对比相关状态
const compareDialogVisible = ref(false);
const compareContent = ref('');
const compareDocUrl = ref('');

const viewContent = (title: string, content: any, row: any) => {
  previewTitle.value = title;
  currentItemId.value = row.id;
  
  // 根据标题设置预览类型和内容
  if (title === '目录') {
    previewType.value = 'menu';
    previewContent.value = typeof content === 'string' ? content : JSON.stringify(content, null, 2);
  } else if (title === '知识点') {
    previewType.value = 'zsd';
    // 如果是数组，转换为逗号分隔的字符串
    if (Array.isArray(content)) {
      previewContent.value = content.join(',');
    } else if (typeof content === 'string') {
      previewContent.value = content;
    } else {
      previewContent.value = '';
    }
  } else {
    previewType.value = 'content';
    previewContent.value = content || '';
  }
  
  previewVisible.value = true;
};

// 打开文档对比对话框
const openCompareDialog = (row: any) => {
  if (!row.url) {
    ElMessage.warning('没有可对比的文档URL');
    return;
  }
  
  compareContent.value = row.content || '';
  compareDocUrl.value = row.url;
  compareDialogVisible.value = true;
};

// 处理对比对话框关闭
const handleCompareClose = () => {
  compareDialogVisible.value = false;
  compareContent.value = '';
  compareDocUrl.value = '';
};

onMounted(() => {
  reloadData()
})
</script>

<style scoped>
.page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  padding: 10px;
  display: flex;
  gap: 10px;
  align-items: center;
}

.body {
  flex: 1;
  padding: 0 10px 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.upload-file-info {
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
}

.upload-status {
  margin-top: 10px;
  color: #409eff;
  font-size: 14px;
  text-align: center;
  margin-bottom: 12px;
}
</style> 