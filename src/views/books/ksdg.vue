<template>
  <div class="page">
    <div class="header">
      <el-button plain
                 type="success"
                 size="small"
                 @click="reloadData"
                 icon="refresh">刷新
      </el-button>
      <el-select v-model="init_data.selected_course_id"
                 placeholder="请选择教材课程"
                 clearable
                 size="small"
                 style="width: 260px;"
                 @change="handleCourseChange">
        <el-option
            v-for="item in courseJcList"
            :key="item.id"
            :label="item.course_name"
            :value="item.id">
              <span style="float: left">{{ item.course_name }}</span>
              <span style="float: right; color: #67C23A; font-size: 13px;">{{ item.course_code }}</span>
        </el-option>
      </el-select>

     
      <el-button type="primary" 
                 size="small" 
                 @click="handleUpload"
                 icon="upload">上传考试大纲
      </el-button>
    </div>
    <div class="body">
      <el-table
          :data="dzs_ls_local"
          width="100%"
          height="calc(100vh - 135px)"
          v-loading="init_data.pageLoading"
          ref="multipleTable"
          highlight-current-row
          size="small"
          border
          :element-loading-text="init_data.pageLoadingText"
          :element-loading-spinner="init_data.pageLoadingSvg"
          element-loading-svg-view-box="-10, -10, 50, 50"
          element-loading-background="rgba(122, 122, 122, 0.8)"
          :row-style="tableRowStyle"
          :cell-style="tableCellStyle"
      >
        <el-table-column label="ID"
                         align="center"
                         width="45"
                         header-align="center"
                         prop="id">
        </el-table-column>
        <el-table-column label="课程名称"
                         align="left"
                         min-width="100"
                         header-align="center"
                         show-overflow-tooltip>
          <template #default="scope">
            {{ getCourseName(scope.row.course_jc_id) }}
          </template>
        </el-table-column>
        <el-table-column label="标题"
                         align="left"
                         min-width="200"
                         header-align="center"
                         show-overflow-tooltip
                         prop="title">
        </el-table-column>
          <el-table-column label="内容"
                         align="center"
                         width="120"
                         header-align="center">
          <template #default="scope">
            <el-tooltip content="关闭时自动保存" placement="top">
              <el-button v-if="scope.row.content" 
                         size="small" 
                         type="primary" 
                         @click="viewContent(scope.row)">
                编辑
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="文档URL"
                         min-width="100"
                         align="center"
                         header-align="center"
                         show-overflow-tooltip>
          <template #default="scope">
            <el-button link type="primary" size="small" @click="showUrlPreview(scope.row.url)">查看</el-button>
          </template>
        </el-table-column>
        <el-table-column label="创建时间"
                         align="center"
                         width="200"
                         header-align="center"
                         prop="create_date">
        </el-table-column>
        <el-table-column label="创建人"
                         align="center"
                         width="100"
                         header-align="center"
                         prop="create_user">
        </el-table-column>
        <el-table-column label="执行状态"
                         align="center"
                         width="100"
                         header-align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.execute_status)">
              {{ getStatusText(scope.row.execute_status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作"
                         align="center"
                         width="200"
                         header-align="center">
          <template #default="scope">
            <el-button size="small" 
                       type="primary" 
                       icon="magic-stick"
                       v-if="scope.row.execute_status !== 'Success'"
                       :disabled="!!(scope.row.execute_id && scope.row.execute_status === 'Running')"
                       @click="handleGetAI(scope.row)">文档转换
            </el-button>
            <el-button size="small" 
                       type="info" 
                       icon="view"
                       v-if="scope.row.execute_status === 'Success'"
                       @click="handlePreview(scope.row)">预览
            </el-button>
            <el-button size="small" 
                       type="warning" 
                       icon="edit"
                       @click="handleEdit(scope.row)">重传
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 上传考试大纲对话框 -->
    <el-dialog
        v-model="uploadDialog.visible"
        title="上传考试大纲"
        width="500px"
        :close-on-click-modal="false"
        :close-on-press-escape="!uploadDialog.uploading">
      <el-form :model="uploadDialog.form" label-width="120px">
        <el-form-item label="所属课程教材" required>
          <el-select v-model="uploadDialog.form.course_id" 
                    placeholder="请选择课程教材" 
                    style="width: 100%">
            <el-option
                v-for="item in courseJcList"
                :key="item.id"
                :label="item.course_name"
                :value="item.id">
                  <span style="float: left">{{ item.course_name }}</span>
                  <span style="float: right; color: #67C23A; font-size: 13px;">{{ item.course_code }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="考试大纲文件" required>
          <el-upload
              class="upload-demo"
              drag
              ref="upload"
              :show-file-list="false"
              action="#"
              :auto-upload="false"
              :on-change="beforeUpload"
              :limit="1"
              :multiple="false"
              :disabled="uploadDialog.uploading"
              :on-exceed="handleExceed"
              accept="application/msword, .doc, .docx">
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                仅支持 docx\doc 格式
              </div>
            </template>
          </el-upload>
          <div class="upload-file-info" v-if="uploadDialog.form.file">
            已选择: {{ uploadDialog.form.file.name }} ({{ (uploadDialog.form.file.size / (1024 * 1024)).toFixed(2) }}MB)
          </div>
        </el-form-item>
        <el-form-item v-if="uploadDialog.uploading">
          <el-progress :percentage="uploadDialog.progress" :format="formatProgress" />
          <div class="upload-status">正在上传中，请勿关闭窗口...</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialog.visible = false" :disabled="uploadDialog.uploading">取消</el-button>
          <el-button type="primary" @click="submitUpload" :loading="uploadDialog.uploading" :disabled="uploadDialog.uploading">{{ uploadDialog.uploading ? '上传中...' : '确定' }}</el-button>
        </span>
      </template>
    </el-dialog>
 
    <!-- New URL Preview Dialog -->
    <preview-url-dialog v-model="previewUrlDialogVisible" :doc-url="currentPreviewUrl" />

    <!-- 编辑对话框 -->
    <el-dialog
        v-model="editDialog.visible"
        title="编辑考试大纲"
        width="500px"
        :close-on-click-modal="false"
        :close-on-press-escape="!editDialog.uploading">
      <el-form :model="editDialog.form" label-width="100px">
        <el-form-item label="所属课程" required>
          <el-select v-model="editDialog.form.course_id" 
                    placeholder="请选择课程" 
                    style="width: 100%">
            <el-option
                v-for="item in courseJcList"
                :key="item.id"
                :label="item.course_name"
                :value="item.id">
                  <span style="float: left">{{ item.course_name }}</span>
                  <span style="float: right; color: #67C23A; font-size: 13px;">{{ item.course_code }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="考试大纲文件">
          <el-upload
              class="upload-demo"
              drag
              ref="editUpload"
              :show-file-list="false"
              action="#"
              :auto-upload="false"
              :on-change="beforeEditUpload"
              :limit="1"
              :multiple="false"
              :disabled="editDialog.uploading"
              :on-exceed="handleEditExceed"
              accept="application/msword, .doc, .docx">
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                仅支持 docx 格式，不选择文件则保持原文件不变
              </div>
            </template>
          </el-upload>
          <div class="upload-file-info" v-if="editDialog.form.file">
            已选择: {{ editDialog.form.file.name }} ({{ (editDialog.form.file.size / (1024 * 1024)).toFixed(2) }}MB)
          </div>
        </el-form-item>
        <el-form-item v-if="editDialog.uploading">
          <el-progress :percentage="editDialog.progress" :format="formatProgress" />
          <div class="upload-status">正在上传中，请勿关闭窗口...</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialog.visible = false" :disabled="editDialog.uploading">取消</el-button>
          <el-button type="primary" @click="submitEdit" :loading="editDialog.uploading" :disabled="editDialog.uploading">{{ editDialog.uploading ? '保存中...' : '确定' }}</el-button>
        </span>
      </template>
    </el-dialog>


    <!-- 内容编辑 -->
    <el-dialog v-model="init_dialog_adup.dialogShow"
              align-center draggable
              v-if="init_dialog_adup.dialogShow"
              :show-close="true"
              :fullscreen="true"
              :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="dialog_content" v-loading="init_dialog_adup.loading"
          :element-loading-text="init_dialog_adup.loadingText"
          :element-loading-spinner="init_dialog_adup.loadingSvg"
          element-loading-svg-view-box="-10, -10, 50, 50"
          element-loading-background="rgba(122, 122, 122, 0.8)">
          <div style="width:100%;">
              <div class="editor_div"> 
                  <v-md-editor v-model="init_dialog_adup.content" height="calc(100vh - 150px)"/> 
              </div>
            </div> 
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="init_dialog_adup.dialogShow = false" icon="Close">关闭</el-button>
          <el-button type="success" @click="SaveContentFrom()" icon="CircleCheck"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="previewDialog.visible" ref="kdroomDialog" title="预览内容" draggable
              :show-close="true"
              :fullscreen="true"
              :close-on-click-modal="false" :close-on-press-escape="false">
      <div style="width:100%;">
        <div class="editor_div">
          <v-md-preview :text="previewDialog.content" style="height: calc(100vh - 150px); overflow: auto;" ></v-md-preview>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="previewDialog.visible = false" icon="Close">关闭</el-button> 
        </span>
      </template>
    </el-dialog>



  </div>
</template>

<script setup lang='ts'>
import { ref, reactive,onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { getSelectCourseJcList,getCourseKsdgList, uploadDzs,setCourseKsdgList,getContentKsdgFromCoze,updateCourseKsdg } from '@/api/dzs';
import { msgShow } from '@/utils/message'; 
import { genFileId } from 'element-plus'
import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'
import { useRouter } from 'vue-router';
import PreviewUrlDialog from '@/components/dzs/preview_url.vue';

//新增编辑
const init_dialog_adup = reactive({
  dialogShow: false,
  title: '',
  loading: false,
  loadingText: "获取数据中，请稍后...",
  loadingSvg: `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `,
  content: '',
  id: 0
})

interface DzsItem {
  id: number
  course_base_id: number
  title: string
  url: string
  zsd_key: string
  create_date: string
  create_user: string
  content?: string
  menu?: string
  zsd?: string
  execute_id?: string
  execute_status?: string
}
 
const dzs_ls_local = ref<DzsItem[]>([])
const init_data = ref({
  pageLoading: false,
  pageLoadingText: "获取数据中，请稍后...",
  pageLoadingSvg: `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `,
  selected_course_id: null as number | null
})
const router = useRouter();

// State for the new URL preview dialog
const previewUrlDialogVisible = ref(false);
const currentPreviewUrl = ref<string | null>(null);

const uploadDialog = ref({
  visible: false,
  progress: 0,
  uploading: false,
  form: {
    title: '',
    file: null as File | null,
    course_id: null as number | null
  }
})

const previewDialog = reactive({
  visible: false,
  content: ''
})

const editDialog = ref({
  visible: false,
  progress: 0,
  uploading: false,
  form: {
    id: null as number | null,
    title: '',
    file: null as File | null,
    course_id: null as number | null,
    url: ''
  }
})

// 课程列表
const courseJcList = ref([]) as any

// 获取课程列表
const getCourseJcListData = async () => {
  try {
    const res = await getSelectCourseJcList()
    if (res.code === 200) {
      courseJcList.value = res.data
    } else {
      msgShow(res.msg || '获取课程列表失败', 'warning')
    }
  } catch (error) {
    msgShow('获取课程列表失败', 'error')
  }
}


const viewContent = (row: any) => {
    init_dialog_adup.title = '内容编辑'
    init_dialog_adup.dialogShow = true
    init_dialog_adup.content = row.content
    init_dialog_adup.id = row.id
  }



// 表格样式
const tableRowStyle = () => {
  return {
    height: '40px'
  }
}

const tableCellStyle = () => {
  return {
    padding: '4px 0'
  }
}

// 获取课程名称
const getCourseName = (courseId: number | null) => {
  if (!courseId) return '-'
  const course = courseJcList.value.find((c:any) => Number(c.id) === Number(courseId))
  return course?.course_name || '-'
}

//保存数据
const SaveContentFrom=() => {
  var prem={
    content:init_dialog_adup.content,
    id:init_dialog_adup.id
  }
  updateCourseKsdg(prem).then((res:any) => {
    if (res.data > 0) {
      init_dialog_adup.dialogShow = false
      ElMessage({
        message: '保存成功！',
        type: 'success',
      })
      reloadData()
    }else{
      ElMessage({
        message: '保存失败！'+res.msg,
        type: 'error',
      })
    }
  })
}
  
// 处理AI获取
const handleGetAI = async (row: DzsItem) => {
  try {
    const data = {
      url: row.url,
      id: row.id
    }
    const res = await getContentKsdgFromCoze(data)
    if (res.code === 200) {
      // 更新本地数据
      const index = dzs_ls_local.value.findIndex(item => item.id === row.id)
      if (index !== -1) {
        dzs_ls_local.value[index].execute_status = res.data.execute_status
      }
    } else {
      msgShow(res.msg || 'AI内容获取失败', 'warning')
    }
  } catch (error) {
    msgShow('AI内容获取失败', 'error')
  }
}

// 加载数据
const reloadData = async () => {
  init_data.value.pageLoading = true
  try {
    // 先获取课程列表
    await getCourseJcListData()
    // 再获取考试大纲列表
    const params = {
      course_id: init_data.value.selected_course_id
    };
    const res = await getCourseKsdgList(params) 
    if (res.code === 200) {
      dzs_ls_local.value = res.data || []
    } else {
      msgShow(res.msg || '获取考试大纲列表失败', 'warning')
    }
  } catch (error) {
    msgShow('获取数据失败', 'error')
  } finally {
    init_data.value.pageLoading = false
  }
}

// 处理课程选择变化
const handleCourseChange = () => {
  reloadData()
}

// 处理上传按钮点击
const handleUpload = async () => {
  await getCourseJcListData() // 获取课程列表
  uploadDialog.value.visible = true
  uploadDialog.value.form = {
    title: '',
    file: null,
    course_id: null
  }
}

// 上传前检查
const beforeUpload = (event: any) => {
  const file = event.raw || event.file
  const isDOCX = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'

  if (!isDOCX) {
    msgShow('只能上传DOCX 格式的文件！', 'warning')
    return false
  }
  uploadDialog.value.form.file = file
  // 将文件名（不含扩展名）设置为标题
  uploadDialog.value.form.title = file.name.split('.')[0]
  return false // 阻止自动上传
}

const upload = ref<UploadInstance>()

const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}

const formatProgress = (p: number) => p + '%'

// 提交上传
const submitUpload = async () => {
  if (!uploadDialog.value.form.title) {
    msgShow('请输入标题', 'warning')
    return
  }
  if (!uploadDialog.value.form.file) {
    msgShow('请选择要上传的文件', 'warning')
    return
  }
  if (!uploadDialog.value.form.course_id) {
    msgShow('请选择所属教材课程', 'warning')
    return
  } 
  uploadDialog.value.uploading = true
  uploadDialog.value.progress = 0
  
  try {
    const formData = new FormData()
    formData.append('file', uploadDialog.value.form.file)
    // 上传文件
    const uploadRes = await uploadDzs(formData)
    if (uploadRes.success) {
      // 保存数据
      const saveRes = await setCourseKsdgList({
        id:0,
        course_jc_id: uploadDialog.value.form.course_id,
        title: uploadDialog.value.form.title,
        url: uploadRes.url
      })
      
      if (saveRes.code === 200) {
        msgShow('上传成功', 'success')
        uploadDialog.value.visible = false
        reloadData()
      } else {
        msgShow(saveRes.msg || '保存数据失败', 'error')
      }
    } else {
      msgShow('文件上传失败', 'error')
    }
  } catch (error) {
    msgShow('上传失败', 'error')
  } finally {
    uploadDialog.value.uploading = false
    uploadDialog.value.progress = 0
  }
}

// 处理预览
const handlePreview = (row: any) => {
  previewDialog.content = row.content
  previewDialog.visible = true
}

// 获取状态类型
const getStatusType = (status: string | undefined) => {
  switch (status) {
    case 'Success':
      return 'success'
    case 'Fail':
      return 'danger'
    case 'Running':
      return 'warning'
    default:
      return 'info'
  }
}

// Method to show the URL preview dialog
const showUrlPreview = (url: string) => {
  currentPreviewUrl.value = url;
  previewUrlDialogVisible.value = true;
};

// 获取状态文本
const getStatusText = (status: string | undefined) => {
  switch (status) {
    case 'Success':
      return '成功'
    case 'Fail':
      return '失败'
    case 'Running':
      return '执行中'
    default:
      return '未执行'
  }
}

const editUpload = ref<UploadInstance>()

// 处理编辑按钮点击
const handleEdit = (row: DzsItem) => {
  editDialog.value.visible = true
  editDialog.value.form = {
    id: row.id,
    title: row.title,
    file: null,
    course_id: row.course_base_id,
    url: row.url
  }
}

// 编辑上传前检查
const beforeEditUpload = (event: any) => {
  const file = event.raw || event.file
  const isDOCX = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  
  if (!isDOCX) {
    msgShow('只能上传 DOCX 格式的文件！', 'warning')
    return false
  }
  editDialog.value.form.file = file
  return false // 阻止自动上传
}

const handleEditExceed: UploadProps['onExceed'] = (files) => {
  editUpload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  editUpload.value!.handleStart(file)
}

// 提交编辑
const submitEdit = async () => {
  if (!editDialog.value.form.title) {
    msgShow('请输入标题', 'warning')
    return
  }
  if (!editDialog.value.form.course_id) {
    msgShow('请选择所属课程', 'warning')
    return
  }
  
  editDialog.value.uploading = true
  editDialog.value.progress = 0
  
  try {
    let url = editDialog.value.form.url
    let url_change = 0
    
    // 如果有新文件，先上传
    if (editDialog.value.form.file) {
      const formData = new FormData()
      formData.append('file', editDialog.value.form.file)

      const uploadRes = await uploadDzs(formData) 
      if (uploadRes.success) {
        url = uploadRes.url
        url_change = 1
      } else {
        msgShow('文件上传失败', 'error')
        return
      }
    }
    
    // 更新数据
    const updateRes = await setCourseKsdgList({
      id: editDialog.value.form.id,
      course_base_id: editDialog.value.form.course_id,
      title: editDialog.value.form.title,
      url: url,
      url_change: url_change
    })
    
    if (updateRes.code === 200) {
      msgShow('更新成功', 'success')
      editDialog.value.visible = false
      reloadData()
    } else {
      msgShow(updateRes.msg || '更新失败', 'error')
    }
  } catch (error) {
    msgShow('更新失败', 'error')
  } finally {
    editDialog.value.uploading = false
    editDialog.value.progress = 0
  }
}

onMounted(() => {
  reloadData()
})
</script>

<style scoped >
.page {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.editor_div{
  width: 100%;
  height: -100px+100vh;
  overflow: auto;
}
.header {
  padding: 10px;
  display: flex;
  gap: 10px;
  align-items: center;
}

.body {
  flex: 1;
  padding: 0 10px 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.upload-file-info {
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
}

.upload-status {
  margin-top: 10px;
  color: #409eff;
  font-size: 14px;
  text-align: center;
  margin-bottom: 12px;
}

 
</style> 