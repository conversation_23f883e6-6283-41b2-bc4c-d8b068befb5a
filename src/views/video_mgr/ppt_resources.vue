<template>
  <div class="page" >
    <div class="header"> 
          <el-button type="success" plain size="default" icon="refresh" round @click="getData"></el-button>
          <el-input v-model="searchForm.keySearch" placeholder="名称" size="default" style="width: 260px;margin:0 10px;" clearable @change="getData">
            <template #append>
              <el-button icon="Search" @click="getData"/>
            </template>
          </el-input>
          <el-tag class="el_tag_5">课程编码</el-tag>
          <el-select v-model="searchForm.course_base_id" clearable filterable style="width: 160px;margin:0 10px;" placeholder="课程编码" @change="getData">
            <el-option v-for="item in course_base_list" :key="item.id" :label="item.kc_mc" :value="item.id" >
                <span style="float: left">{{ item.kc_mc }} <span> {{ '('+item.id+')' }} </span></span>
                <span style="float: right; color: #67C23A; font-size: 13px;">{{ item.kc_bm }}</span>
            </el-option> 
          </el-select>
          <el-button type="primary"  plain size="default" round icon="CirclePlus" @click="AddData()">新增</el-button>
          <el-button type="success" plain size="default" round icon="edit" @click="handleEdit()">编辑</el-button> 
          <el-button type="primary" size="default"  @click="handleUpload" icon="upload">上传PPT资源</el-button>
    </div>
    <div class="body">
      <el-table
        ref="multipleRolesTable"
        class="tb-edit"
        :data="searchForm.dataTable"
        border
        v-loading="searchForm.loading" 
        :height="searchForm.tableHeight"
        size="mini"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="60" align="center" />
        <el-table-column type="expand">
          <template #default="scope">
            <div style="padding: 5px 50px 30px 50px!important;;">
                <el-table
                    ref="propsmultipleRolesTable"
                    class="tb-edit"
                    :data="scope.row.file_all"
                    border
                    lazy
                    height="550px"
                    size="mini"
                    @expand-change="(row: any, expanded: boolean) => handleSecondExpandChange(row, expanded, scope.row)"
                  >
                    <el-table-column type="expand">
                        <template #default="scope">
                          <div style="padding: 5px 50px 30px 50px!important;;">
                              <el-table
                                  ref="propsfile_all_imgs_listTable"
                                  class="tb-edit"
                                  :data="file_all_imgs_list"
                                  border
                                  :preserve-expanded-content="false"
                                  height="400px"
                                  size="mini"
                                >
                                  <el-table-column prop="" type="index" label="序号"  align="center" width="60" header-align="center" />
                                  <el-table-column prop="file_name" label="文件名" min-width="120" align="center" sortable show-overflow-tooltip />
                                   <el-table-column prop="text_content" label="文字内容" min-width="160" align="center" sortable show-overflow-tooltip />
                                  <el-table-column prop="file_url" label="文件地址" min-width="120" align="center" sortable  show-overflow-tooltip />
                                  <el-table-column prop="mp3_url" label="视频地址" min-width="120" align="center" sortable show-overflow-tooltip />
                                  <el-table-column prop="description" label="描述" min-width="120" align="center" sortable show-overflow-tooltip />
                                  <el-table-column prop="slide_number" label="页码" min-width="60" align="center" sortable show-overflow-tooltip />
                                  <el-table-column prop="width" label="宽" min-width="60" align="center" sortable show-overflow-tooltip />
                                  <el-table-column prop="height" label="高" min-width="60" align="center" sortable show-overflow-tooltip />
                                  <el-table-column prop="size_text" label="文件大小" min-width="100" align="center" sortable show-overflow-tooltip />
                                  <el-table-column prop="created_date" label="创建时间" min-width="120" align="center" sortable show-overflow-tooltip />
                                  <el-table-column prop="created_user" label="创建人" min-width="80" align="center" sortable show-overflow-tooltip />
                                </el-table>
                          </div>
                        </template>
                      </el-table-column>
                    <el-table-column prop="" type="index" label="序号"  align="center" width="60" header-align="center" />
                    <el-table-column prop="file_name" label="文件名" min-width="120" align="center" sortable show-overflow-tooltip />
                    <el-table-column prop="file_url" label="文件地址" min-width="120" align="center" sortable  show-overflow-tooltip />
                    <el-table-column prop="img_count" label="图片数" min-width="120" align="center" sortable />
                    <el-table-column prop="img_count" label="操作" min-width="120" align="center" sortable >
                    <template #default="scope">
                      <!-- <span>提取PTT内容</span> -->
                      <el-button type="primary"  plain size="default" round icon="CirclePlus" @click="ExtractPPTContent(scope.row)">提取PTT内容</el-button>
                    </template>
                    </el-table-column>
                    <el-table-column prop="mp3_url" label="视频地址" min-width="120" align="center" sortable show-overflow-tooltip />
                    <el-table-column prop="description" label="描述" min-width="120" align="center" sortable show-overflow-tooltip />
                    <el-table-column prop="created_date" label="上传时间" min-width="160" align="center" sortable show-overflow-tooltip />
                    <el-table-column prop="created_user" label="操作人" min-width="100" align="center" sortable />
                  </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="" type="index" label="序号"  align="center" width="60" header-align="center" />
        <el-table-column prop="title" label="名称" min-width="120" align="center" sortable />
        <el-table-column prop="ver" label="版本" min-width="120" align="center" sortable />
        <el-table-column prop="kc_bm" label="课程编码" min-width="120" align="center" sortable />
        <el-table-column prop="kc_mc" label="课程名称" min-width="120" align="center" sortable />
        <el-table-column prop="created_date" label="创建时间" min-width="160" align="center" sortable />
        <el-table-column prop="created_user" label="操作人" min-width="100" align="center" sortable />
      </el-table>
      <div style="margin-top: 16px; text-align: right;">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="searchForm.total"
          :page-size="searchForm.pageSize"
          :current-page="searchForm.currentPage"
          :page-sizes="[20, 50, 100, 500, searchForm.total]"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 编辑对话框 -->
    <el-dialog v-model="DialogVisible" ref="kdroomDialog" :title="Dialog_title" draggable width="600px"
      :close-on-press-escape="false" :close-on-click-modal="false">
      <div style="width:100%;text-align:center;">
        <el-form :model="FromModule" :inline="true" :label-width="formLabelWidth">
          <el-form-item label="PPT资源名称" >
            <el-input v-model="FromModule.title" placeholder="PPT资源名称" style="width:400px" />
          </el-form-item>
          <el-form-item label="版本" >
            <el-input v-model="FromModule.ver" placeholder="版本" style="width:400px" />
          </el-form-item>
          <el-form-item label="课程名称" >
            <el-select v-model="FromModule.course_base_id" clearable filterable style="width: 400px;" placeholder="课程编码">
            <el-option v-for="item in course_base_list" :key="item.id" :label="item.kc_mc" :value="item.id" >
                <span style="float: left">{{ item.kc_mc }} <span> {{ '('+item.id+')' }} </span></span>
                <span style="float: right; color: #67C23A; font-size: 13px;">{{ item.kc_bm }}</span>
            </el-option>
          </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="DialogVisible = false" icon="Close">关闭</el-button>
          <el-button type="success" @click="SaveFrom()" icon="CircleCheck"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 上传PPT对话框 -->
    <el-dialog
        v-model="uploadDialog.visible"
        :title="uploadDialog.title"
        width="600px"
        :close-on-click-modal="false"
        :close-on-press-escape="!uploadDialog.uploading">
      <el-form :model="uploadDialog.form" label-width="120px">
        <el-form-item label="PPT资源文件" required>
          <el-upload
              class="upload-demo"
              drag
              ref="upload"
              :show-file-list="false"
              action="#"
              :auto-upload="false"
              :on-change="beforeUpload"
              :limit="1"
              :multiple="false"
              :on-exceed="handleExceed"
              :disabled="uploadDialog.uploading"
              accept=".ppt,.pptx">
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                仅支持 ppt、pptx 格式
              </div>
            </template>
          </el-upload>
          <div class="upload-file-info" v-if="uploadDialog.form.file">
            已选择: {{ uploadDialog.form.file.name }} ({{ (uploadDialog.form.file.size / (1024 * 1024)).toFixed(2) }}MB)
          </div>
        </el-form-item>
        <el-form-item v-if="uploadDialog.uploading">
          <el-progress :percentage="uploadDialog.progress" :format="formatProgress" />
          <div class="upload-status">正在上传中，请勿关闭窗口...</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialog.visible = false" :disabled="uploadDialog.uploading">取消</el-button>
          <el-button type="primary" @click="submitUpload" :loading="uploadDialog.uploading" :disabled="uploadDialog.uploading">{{ uploadDialog.uploading ? '上传中...' : '上传保存' }}</el-button>
        </span>
      </template>
    </el-dialog>


  </div>
</template>

<script lang="ts" setup>
 import { defineComponent, onMounted, reactive, ref, toRefs } from "vue";
 import { GetVideoPPTInfoData,SaveVideoPPTInfoData,uploadVideoPPTFile,SetVideoPPTFileList,Extract_PPT_content,GetVideoPPTFileImgsList } from '@/api/kcmgr'
 import { GetBaseKcData } from '@/api/question'
 import { ElMessage,ElMessageBox } from 'element-plus'
 import { UploadFilled } from '@element-plus/icons-vue'
 import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'

 const searchForm= reactive({
    loading:false,
    tableHeight:window.innerHeight - 200,
    keySearch:'',
    course_base_id:2,
    dataTable:[],
    total: 0,
    pageSize: 30,
    currentPage: 1
  })
const uploadDialog = reactive({
  visible: false,
  title:'',
  progress: 0,
  uploading: false,
  form: {
    file: null as File | null,
    file_title: '' as string,
    ppt_info_id: null as number | null
  }
})


const DialogVisible = ref(false)
const Dialog_title = ref('')
const formLabelWidth = '100px'
const handlecurrentRow = ref([]) as any
const FromModule = ref({}) as any;
const course_base_list = ref([]) as any
const file_all_imgs_list = ref([]) as any

const upload = ref<UploadInstance>()
const uploadRef = ref()

const getKcData = () => {
    GetBaseKcData({}).then((res: any) => {
      if (res.code == 200) {
        course_base_list.value = res.data
      } else {
        ElMessage.error(res.msg)
      }
    })
}

const getData=()=> {
  searchForm.loading = true
    const pars = {
      key: searchForm.keySearch,
      //course_base_id: searchForm.course_base_id === '' ? 0 : searchForm.course_base_id,
      course_base_id: searchForm.course_base_id,
      currentPage: searchForm.currentPage,
      pageSize: searchForm.pageSize,
    }
    GetVideoPPTInfoData(pars).then((msg:any) => {
      searchForm.dataTable = msg.data || []
      searchForm.total = msg.total || searchForm.dataTable.length
      searchForm.loading = false
    }).catch((err:any) => { console.log(err); searchForm.loading = false })
}


const ExtractPPTContent = (row:any) => {

    ElMessageBox.confirm(
      '确认是否提取【' + row.file_name + '】PPT内容并转图片保存, 是否继续?',
      '提示',
      {
        confirmButtonText: '确定提取',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    .then(() => {
      searchForm.loading = true
      const pars = { 
        ppt_url: row.file_url,
        ppt_info_file_id: row.ppt_info_file_id
      }
      Extract_PPT_content(pars).then((msg:any) => { 
        console.log(msg.data)
        if (msg.data.results.length > 0) {
          ElMessage({
            type: 'success',
            message: '成功提取!' + msg.data.total_slides + '条'
          })
          searchForm.loading = false
          getData()
          handleSecondExpandChange(row,true,row)
        } else {
          ElMessage({
            type: 'info',
            message: '提取失败!' + msg.msg
          })
        }
      }).catch((err:any) => {
        ElMessage({
          type: 'info',
          message: '提取失败!' + err
        })
      })



    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消删除!',
      })
    })
}

const AddData = () => {
    FromModule.value={
      id:0,
      ver:'',
      title:'',
      course_base_id:''
    }
    Dialog_title.value = '新增用户类型'
    DialogVisible.value = true
  }

const handleEdit = () => {
  if (handlecurrentRow.value != null && handlecurrentRow.value.length === 1) {
    Dialog_title.value = '编辑【' + handlecurrentRow.value[0].title + '】'
    DialogVisible.value = true
    FromModule.value = handlecurrentRow.value[0]
    } else {
      ElMessage({
          message: '请勾选一条数据进行编辑！',
          type: 'error',
        })
      return false
    }
}
//保存数据
const SaveFrom=() => {
  if(FromModule.value.title==='' || FromModule.value.title===null || FromModule.value.title === undefined){
    ElMessage({
          message: '用户类型名称不能为空',
          type: 'info',
        })
        return
  }
  SaveVideoPPTInfoData(FromModule.value).then((res:any) => {
    if (res.data > 0) {
      DialogVisible.value = false
      ElMessage({
        message: '保存成功！',
        type: 'success',
      })
      getData()
    }else{
      ElMessage({
        message: '保存失败！'+res.msg,
        type: 'error',
      })
    }
  })
}

// 处理上传按钮点击
const handleUpload = async () => {
  if (handlecurrentRow.value != null && handlecurrentRow.value.length === 1) {
      uploadDialog.visible = true
      uploadDialog.title = '上传【' + handlecurrentRow.value[0].title + '】PPT资源'
      uploadDialog.form = {
        file: null,
        file_title: '',
        ppt_info_id: handlecurrentRow.value[0].id,
      }
    } else {
      ElMessage({
          message: '请勾选一条数据进行编辑！',
          type: 'error',
        })
      return false
    }
}


const handleSelectionChange = (rows: any) => {
  handlecurrentRow.value = rows
}
const handleSizeChange = (size: number) => {
  searchForm.pageSize = size
  getData()
}

const handlePageChange = (page: number) => {
  searchForm.currentPage = page
  getData()
}

// 处理文件超出限制
const handleExceed: UploadProps['onExceed'] = (files) => {
  if (upload.value) {
    upload.value.clearFiles()
    const file = files[0] as UploadRawFile
    upload.value.handleStart(file)
  }
}
// 处理文件选择
const beforeUpload = (file: UploadRawFile) => {
  if (upload.value) {
    upload.value.clearFiles()
  }
  const isPPT = file.type === 'application/vnd.ms-powerpoint' || 
                file.type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation' ||
                file.name.toLowerCase().endsWith('.ppt') ||
                file.name.toLowerCase().endsWith('.pptx')
  const isLt20M = file.size / 1024 / 1024 < 20
  if (!isPPT) {
    ElMessage.error('只能上传PPT文件!')
    return false
  }
  if (!isLt20M) {
    ElMessage.error('文件大小不能超过 20MB!')
    return false
  }
  uploadDialog.form.file = file
  uploadDialog.form.file_title = file.name.split('.')[0] 
  return false
}
// 格式化上传进度
const formatProgress = (percentage: number) => {
  return percentage === 100 ? '上传完成' : `${percentage}%`
}

// 提交上传
const submitUpload = async () => {
  if (!uploadDialog.form.file) {
    ElMessage.warning('请先选择要上传的文件')
    return
  }

  try {
    uploadDialog.uploading = true 
    const formData = new FormData()
    formData.append('file', uploadDialog.form.file) 
    const uploadRes = await uploadVideoPPTFile(formData)
    if (uploadRes.success) {
      const pars = {
        ppt_info_id: uploadDialog.form.ppt_info_id,
        file_name: uploadDialog.form.file_title,
        file_url: uploadRes.url,
        mp3_url: '',
        description: ''
      }
      const saveRes = await SetVideoPPTFileList(pars)
      if (saveRes.code === 200) {
        ElMessage.success('上传成功')
        uploadDialog.visible = false
        getData()
      } else {
        ElMessage.error(saveRes.msg || '保存数据失败')
      }

    } else {
      ElMessage.error('文件上传失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '上传失败')
  } finally {
    uploadDialog.uploading = false
    uploadDialog.progress = 0
  }
}

// 处理第二层展开
const handleSecondExpandChange = async (row: any, expanded: boolean, parentRow: any) => {
  if (expanded) {
    try {
      if (!row.file_all_imgs) {
        // row.loading_imgs = true
        const res = await GetVideoPPTFileImgsList({ ppt_info_file_id: row.ppt_info_file_id })
        if (res.code === 200) {
          // row.file_all_imgs = res.data || []
          file_all_imgs_list.value = res.data || []
        } else {
          ElMessage.error(res.msg || '获取数据失败')
        }
      }
    } catch (error: any) {
      ElMessage.error(error.message || '获取数据失败')
    } finally {
      // row.loading_imgs = false
    }
  }
}

 
  onMounted(() => {
    getData();
    getKcData();
  });
</script>

<style lang="scss">
.addRolsDia{
  .el-dialog__body{
    padding: 5px 5px !important;
}
.el-transfer-panel__item.el-checkbox .el-checkbox__label{
  margin-left: 20px !important;
}
.el-transfer-panel__body {
  height:550px
}
.el-transfer-panel__list.is-filterable{
  height: 400px;
}
.el-transfer-panel__item.el-checkbox .el-checkbox__label{
    width: auto!important;
  }
 .el-transfer-panel{
  width: 350px !important;
  text-align: left !important;
}
.el-transfer__buttons {
    padding: 0 20px !important;
}
}


</style>
