<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        stripe
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="config_version" label="版本" width="120" align="center" />
        <el-table-column label="是否执行" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.is_enabled ? 'success' : 'danger'">
              {{ row.is_enabled ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="检查方法" width="150" align="center">
          <template #default="{ row }">
            <el-tag :type="row.check_method === 'popup' ? 'primary' : 'warning'">
              {{ getCheckMethodText(row.check_method) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="检查时间" width="150" align="center">
          <template #default="{ row }">
            <el-input-number
              v-model="row.time"
              :min="1"
              :max="999"
              size="small"
              @change="handleTimeChange(row)"
              style="width: 100px"
            />
            <span style="margin-left: 5px">分钟</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="120" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :loading="row.statusLoading"
              @change="handleStatusChange(row)"
              active-text="启用"
              inactive-text="禁用"
            />
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 添加/编辑对话框 -->
      <el-dialog
        v-model="dialog.visible"
        :title="dialog.title"
        width="500px"
        :close-on-click-modal="false"
        @closed="resetForm"
      >
        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="版本" prop="config_version">
            <el-input v-model="form.config_version" placeholder="请输入配置版本" />
          </el-form-item>
          <el-form-item label="是否执行" prop="is_enabled">
            <el-switch v-model="form.is_enabled" active-text="是" inactive-text="否" />
          </el-form-item>
          <el-form-item label="检查方法" prop="check_method">
            <el-select v-model="form.check_method" placeholder="请选择检查方法" style="width: 100%">
              <el-option label="弹窗验证" value="popup" />
              <el-option label="人脸识别" value="face_recognition" />
            </el-select>
          </el-form-item>
          <el-form-item label="检查时间" prop="time">
            <el-input-number
              v-model="form.time"
              :min="1"
              :max="999"
              placeholder="请输入检查时间间隔"
              style="width: 100%"
            />
            <span style="margin-left: 10px; color: #999;">分钟</span>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-switch v-model="form.status" active-text="启用" inactive-text="禁用" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialog.visible = false">取消</el-button>
            <el-button type="primary" @click="submitForm" :loading="dialog.loading">
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getVerificationConfigAll,
  updateVerificationConfig,
} from '@/api/kcmgr'

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref<any[]>([])

// 对话框
const dialog = reactive({
  visible: false,
  title: '添加规则',
  loading: false
})

// 表单引用
const formRef = ref()

// 表单数据
interface FormData {
  id?: number
  config_version: string
  is_enabled: boolean
  check_method: string
  time: number
  status: boolean
}

const form = reactive<FormData>({
  config_version: '',
  is_enabled: true,
  check_method: 'popup',
  time: 5,
  status: true
})

// 表单验证规则
const rules = {
  config_version: [
    { required: true, message: '请输入配置版本', trigger: 'blur' }
  ],
  check_method: [
    { required: true, message: '请选择检查方法', trigger: 'change' }
  ],
  time: [
    { required: true, message: '请输入检查时间', trigger: 'blur' },
    { type: 'number', min: 1, max: 999, message: '时间必须在1-999分钟之间', trigger: 'blur' }
  ]
}

// 获取检查方法文本
const getCheckMethodText = (method: string) => {
  const methodMap: Record<string, string> = {
    'popup': '弹窗验证',
    'face_recognition': '人脸识别'
  }
  return methodMap[method] || method
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取数据列表
const getList = async () => {
  try {
    loading.value = true
    const res = await getVerificationConfigAll({})
    if (res.code === '200' || parseInt(res.code) === 200) {
      tableData.value = (res.data || []).map((item: any) => ({
        ...item,
        statusLoading: false
      }))
    } else {
      ElMessage.error(res.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 编辑规则
const handleEdit = (row: any) => {
  resetForm()
  Object.assign(form, {
    id: row.id,
    config_version: row.config_version,
    is_enabled: row.is_enabled,
    check_method: row.check_method,
    time: row.time,
    status: row.status
  })
  dialog.title = '编辑规则'
  dialog.visible = true
}

// 复制规则
const handleCopy = (row: any) => {
  resetForm()
  Object.assign(form, {
    config_version: `${row.config_version}_copy`,
    is_enabled: row.is_enabled,
    check_method: row.check_method,
    time: row.time,
    status: false // 复制的规则默认禁用
  })
  dialog.title = '复制规则'
  dialog.visible = true
}

// 处理时间变化
const handleTimeChange = async (row: any) => {
  try {
    const res = await updateVerificationConfig({
      id: row.id,
      time: row.time
    })
    if (res.code === '200' || parseInt(res.code) === 200) {
      ElMessage.success('时间更新成功')
    } else {
      ElMessage.error(res.msg || '时间更新失败')
    }
  } catch (error) {
    console.error('时间更新失败:', error)
    ElMessage.error('时间更新失败')
  }
}

// 处理状态变化
const handleStatusChange = async (row: any) => {
  try {
    row.statusLoading = true
    const res = await updateVerificationConfig({
      id: row.id,
      status: row.status
    })
    if (res.code === '200' || parseInt(res.code) === 200) {
      ElMessage.success(`${row.status ? '启用' : '禁用'}成功`)
    } else {
      // 恢复原状态
      row.status = !row.status
      ElMessage.error(res.msg || '状态更新失败')
    }
  } catch (error) {
    // 恢复原状态
    row.status = !row.status
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  } finally {
    row.statusLoading = false
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    id: undefined,
    config_version: '',
    is_enabled: true,
    check_method: 'popup',
    time: 5,
    status: true
  })
}

// 提交表单
const submitForm = async () => {
  try {
    await formRef.value.validate()

    dialog.loading = true

    const isEdit = !!form.id
    const api = updateVerificationConfig
    const params = isEdit ? form : {
      config_version: form.config_version,
      is_enabled: form.is_enabled,
      check_method: form.check_method,
      time: form.time,
      status: form.status
    }

    const res = await api(params)
    if (res.code === '200' || parseInt(res.code) === 200) {
      ElMessage.success(isEdit ? '更新成功' : '添加成功')
      dialog.visible = false
      getList()
    } else {
      ElMessage.error(res.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    dialog.loading = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.action-container {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>