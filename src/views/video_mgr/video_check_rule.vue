<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        stripe
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="config_version" label="版本" width="120" align="center" />
        <el-table-column label="是否执行" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.is_enabled ? 'success' : 'danger'">
              {{ row.is_enabled ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="检查方法" width="150" align="center">
          <template #default="{ row }">
            <el-tag :type="row.check_method === 'popup' ? 'primary' : 'warning'">
              {{ getCheckMethodText(row.check_method) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="检查时间" width="150" align="center">
          <template #default="{ row }">
            <el-input-number
              v-model="row.time"
              :min="1"
              :max="999"
              size="small"
              @change="handleTimeChange(row)"
              style="width: 100px"
            />
            <span style="margin-left: 5px">分钟</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="200" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :loading="row.statusLoading"
              @change="handleStatusChange(row)"
              active-text="启用"
              inactive-text="禁用"
            />
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
      </el-table>

    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getVerificationConfigAll,
  updateVerificationConfig,
} from '@/api/kcmgr'

// 加载状态
const loading = ref(false)

// 数据加载标志，用于防止数据加载时触发change事件
const isDataLoading = ref(false)

// 表格数据
const tableData = ref<any[]>([])

// 对话框
const dialog = reactive({
  visible: false,
  title: '添加规则',
  loading: false
})

// 表单引用
const formRef = ref()

// 表单数据
interface FormData {
  id?: number
  config_version: string
  is_enabled: boolean
  check_method: string
  time: number
  status: boolean
}

const form = reactive<FormData>({
  config_version: '',
  is_enabled: true,
  check_method: 'popup',
  time: 5,
  status: true
})

// 获取检查方法文本
const getCheckMethodText = (method: string) => {
  const methodMap: Record<string, string> = {
    'popup': '弹窗验证',
    'face_recognition': '人脸识别'
  }
  return methodMap[method] || method
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取数据列表
const getList = async () => {
  try {
    loading.value = true
    isDataLoading.value = true
    const res = await getVerificationConfigAll({})
    if (res.code === '200' || parseInt(res.code) === 200) {
      tableData.value = (res.data || []).map((item: any) => ({
        ...item,
        statusLoading: false
      }))
    } else {
      ElMessage.error(res.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
    // 延迟重置标志，确保数据渲染完成
    setTimeout(() => {
      isDataLoading.value = false
    }, 100)
  }
}

// 处理时间变化
const handleTimeChange = async (row: any) => {
  // 如果正在加载数据，忽略时间变化事件
  if (isDataLoading.value) {
    return
  }

  try {
    const res = await updateVerificationConfig({
      id: row.id,
      time: row.time
    })
    if (res.code === '200' || parseInt(res.code) === 200) {
      ElMessage.success('时间更新成功')
    } else {
      ElMessage.error(res.msg || '时间更新失败')
    }
  } catch (error) {
    console.error('时间更新失败:', error)
    ElMessage.error('时间更新失败')
  }
}

// 处理状态变化
const handleStatusChange = async (row: any) => {
  // 如果正在加载数据，忽略状态变化事件
  if (isDataLoading.value) {
    return
  }

  try {
    row.statusLoading = true
    const res = await updateVerificationConfig({
      id: row.id,
      status: row.status
    })
    if (res.code === '200' || parseInt(res.code) === 200) {
      ElMessage.success(`${row.status ? '启用' : '禁用'}成功`)
    } else {
      // 恢复原状态
      row.status = !row.status
      ElMessage.error(res.msg || '状态更新失败')
    }
  } catch (error) {
    // 恢复原状态
    row.status = !row.status
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  } finally {
    row.statusLoading = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.action-container {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>