<template>
  <div class="page">
    <div class="header">
      <el-button plain
                 type="success"
                 size="small"
                 @click="reloadData"
                 icon="refresh">刷新
      </el-button>
      <el-select v-model="init_data.selected_course_id"
                 placeholder="请选择课程"
                 clearable
                 filterable
                 size="small"
                 style="width: 260px;"
                 @change="handleCourseChange">
        <el-option
            v-for="item in courseList"
            :key="item.id"
            :label="`${item.course_code} ${item.course_name}`"
            :value="item.id">
        </el-option>
      </el-select>
      <el-button type="primary" 
                 size="small" 
                 @click="handleUpload"
                 icon="upload">上传视频
      </el-button>
      <el-button type="warning" 
                 size="small" 
                 @click="handleSyncVideo"
                 icon="refresh">同步视频
      </el-button>
    </div>
    <div class="body">
      <el-table
          :data="video_ls_local"
          width="100%"
          height="calc(100vh - 135px)"
          v-loading="init_data.pageLoading"
          ref="multipleTable"
          highlight-current-row
          size="small"
          border
          :element-loading-text="init_data.pageLoadingText"
          :element-loading-spinner="init_data.pageLoadingSvg"
          element-loading-svg-view-box="-10, -10, 50, 50"
          element-loading-background="rgba(122, 122, 122, 0.8)"
          :row-style="tableRowStyle"
          :cell-style="tableCellStyle"
      >
        <el-table-column prop="" type="index" label="序号"  align="center" width="60" header-align="center" />
        <el-table-column label="课程名称"
                         align="left"
                         min-width="150"
                         header-align="center"
                         show-overflow-tooltip>
          <template #default="scope">
            {{ getCourseName(scope.row.course_jc_id) }}
          </template>
        </el-table-column>
        <el-table-column label="视频标题"
                         align="left"
                         min-width="100"
                         header-align="center"
                         show-overflow-tooltip
                         prop="title">
        </el-table-column>
        <el-table-column label="时长"
                         align="center"
                         width="100"
                         header-align="center"
                         prop="duration_s">
        </el-table-column>
        <el-table-column label="文件大小"
                         align="center"
                         width="100"
                         header-align="center"
                         prop="size_s">
        </el-table-column>
        <el-table-column label="分辨率"
                         align="center"
                         width="120"
                         header-align="center">
          <template #default="scope">
            {{ scope.row.width }}x{{ scope.row.height }}
          </template>
        </el-table-column>
        <el-table-column label="预览图"
                         align="center"
                         width="120"
                         header-align="center">
          <template #default="scope">
            <img :src="`${scope.row.url}${scope.row.image}`" 
                 style="width: 100px; height: 60px; object-fit: cover; cursor: pointer;"
                 @click="handleIamgePreview(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="上传时间"
                         align="center"
                         width="200"
                         header-align="center"
                         prop="date_created">
        </el-table-column>
        <el-table-column label="操作"
                         align="center"
                         width="180"
                         header-align="center">
          <template #default="scope">
            <el-button size="small" 
                       type="primary" 
                       icon="video-play"
                       @click="handlePreview(scope.row)">预览
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 上传视频对话框 -->
    <el-dialog
        v-model="uploadDialog.visible"
        title="上传视频"
        width="500px"
        :close-on-click-modal="false"
        :close-on-press-escape="!uploadDialog.uploading">
      <el-form :model="uploadDialog.form" label-width="100px">
        <el-form-item label="所属课程" required>
          <el-select v-model="uploadDialog.form.course_id" 
                    placeholder="请选择课程" 
                    filterable
                    clearable 
                    style="width: 100%">
            <el-option
                v-for="item in courseList"
                :key="item.id"
                :label="`${item.course_code} ${item.course_name}`"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="视频标题" required>
          <el-input v-model="uploadDialog.form.title" placeholder="请输入视频标题"></el-input>
        </el-form-item>
        <el-form-item label="视频文件" required>
          <el-upload
              class="upload-demo"
              drag
              ref="upload"
              :show-file-list="false"
              action="#"
              :auto-upload="false"
              :on-change="beforeUpload"
              :limit="1"
              :multiple="false"
              :disabled="uploadDialog.uploading"
              :on-exceed="handleExceed"
              accept="video/mp4">
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 mp4 文件
              </div>
            </template>
          </el-upload>
          <div class="upload-file-info" v-if="uploadDialog.form.file">
            已选择: {{ uploadDialog.form.file.name }} ({{ (uploadDialog.form.file.size / (1024 * 1024)).toFixed(2) }}MB)
          </div>
        </el-form-item>
        <el-form-item v-if="uploadDialog.uploading">
          <el-progress :percentage="uploadDialog.progress" :format="p => p + '%'" />
          <div class="upload-status">正在上传中，请勿关闭窗口...</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialog.visible = false" :disabled="uploadDialog.uploading">取消</el-button>
          <el-button type="primary" @click="submitUpload" :loading="uploadDialog.uploading" :disabled="uploadDialog.uploading">{{ uploadDialog.uploading ? '上传中...' : '确定' }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 视频预览对话框 -->
    <el-dialog
        v-model="previewDialog.visible"
        title="视频预览"
        width="800px"
        :destroy-on-close="true"
        :close-on-click-modal="false">
      <video
          v-if="previewDialog.url"
          :src="previewDialog.url"
          controls
          style="width: 100%">
      </video>
    </el-dialog>

    <el-dialog
        v-model="previewImageDialog.visible"
        title="图片预览"
        width="800px"
        :close-on-click-modal="false">
      <img
          :src="previewImageDialog.url"
          style="width: 100%">
      />
    </el-dialog>

    <!-- 同步视频对话框 -->
    <el-dialog
        v-model="syncDialog.visible"
        title="同步视频"
        width="400px"
        :close-on-click-modal="false">
      <el-form label-width="100px">
        <el-form-item label="选择课程" required>
          <el-select 
            v-model="syncDialog.course_id" 
            placeholder="请选择要同步的课程"
            style="width: 100%"
            filterable>
            <el-option
                v-for="item in courseList"
                :key="item.id"
                :label="`${item.course_code} ${item.course_name}`"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="syncDialog.visible = false" :disabled="syncDialog.loading">取消</el-button>
          <el-button 
            type="primary" 
            @click="confirmSyncVideo" 
            :loading="syncDialog.loading"
            :disabled="!syncDialog.course_id">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang='ts'>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElProgress } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { getVideoList, insertVideoList, getCourseJcList, sync_video_course } from '@/api/kcmgr';
import { msgShow } from '@/utils/message';
import axios from 'axios'
import { genFileId } from 'element-plus'
import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'

// 生成GUID函数
const generateGuid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// 获取文件扩展名
const getFileExtension = (filename: string) => {
  return filename.slice((filename.lastIndexOf(".") - 1 >>> 0) + 2);
}

// 获取文件名（不含扩展名）
const getFileNameWithoutExtension = (filename: string) => {
  return filename.slice(0, filename.lastIndexOf("."))
}

interface VideoItem {
  id: number
  no: number
  title: string
  mp4_filename: string
  duration: string
  duration_s: number
  size: number
  size_s: string
  width: number
  height: number
  image: string
  date_created: string
  url: string
  img: string
  course_jc_id: number
}

const video_ls_local = ref<VideoItem[]>([])
const init_data = ref({
  pageLoading: false,
  pageLoadingText: "获取数据中，请稍后...",
  pageLoadingSvg: `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `,
  selected_course_id: null as number | null
})

const uploadDialog = ref({
  visible: false,
  progress: 0,
  uploading: false,
  form: {
    title: '',
    file: null as File | null,
    course_id: null as number | null
  }
})

const previewDialog = ref({
  visible: false,
  url: ''
})

const previewImageDialog = ref({
  visible: false,
  url: ''
})

// 同步视频对话框
const syncDialog = ref({
  visible: false,
  loading: false,
  course_id: null as number | null
})

// 课程列表
const courseList = ref<Array<{id: number, course_code: string, course_name: string}>>([])

// 获取课程列表
const getCourseListData = async () => {
  try {
    const res = await getCourseJcList()
    if (res.code === 200) {
      courseList.value = res.data
    } else {
      msgShow(res.msg || '获取课程列表失败', 'warning')
    }
  } catch (error) {
    msgShow('获取课程列表失败', 'error')
  }
}

// 表格样式
const tableRowStyle = () => {
  return {
    height: '40px'
  }
}

const tableCellStyle = () => {
  return {
    padding: '4px 0'
  }
}

// 获取课程名称
const getCourseName = (courseId: number | null) => {
  if (!courseId) return '-'
  const course = courseList.value.find(c => Number(c.id) === Number(courseId))
  return course?.course_name || '-'
}

// 加载数据
const reloadData = async () => {
  init_data.value.pageLoading = true
  try {
    // 先获取课程列表
    await getCourseListData()
    // 再获取视频列表
    const params = {
      course_id: init_data.value.selected_course_id
    };
    const res = await getVideoList(params)
    if (res.code === 200) {
      video_ls_local.value = res.data
    } else {
      msgShow(res.msg || '获取视频列表失败', 'warning')
    }
  } catch (error) {
    msgShow('获取数据失败', 'error')
  } finally {
    init_data.value.pageLoading = false
  }
}

// 处理课程选择变化
const handleCourseChange = () => {
  reloadData()
}

// 处理上传按钮点击
const handleUpload = async () => {
  await getCourseListData() // 获取课程列表
  uploadDialog.value.visible = true
  uploadDialog.value.form = {
    title: '',
    file: null,
    course_id: null
  }
}

// 处理同步视频按钮点击
const handleSyncVideo = async () => {
  await getCourseListData() // 确保课程列表已加载
  syncDialog.value.visible = true
  syncDialog.value.course_id = null
}

// 确认同步视频
const confirmSyncVideo = async () => {
  if (!syncDialog.value.course_id) {
    msgShow('请选择要同步的课程', 'warning')
    return
  }
  
  try {
    syncDialog.value.loading = true
    const res = await sync_video_course({ course_jc_id: syncDialog.value.course_id })
    if (res.code.toString() === "200") {
      msgShow('同步成功', 'success')
      reloadData() // 刷新视频列表
      syncDialog.value.visible = false
    } else {
      msgShow(res.msg || '同步失败', 'error')
    }
  } catch (error) {
    msgShow('同步过程中发生错误', 'error')
    console.error('同步视频失败:', error)
  } finally {
    syncDialog.value.loading = false
  }
}

// 上传前检查
const beforeUpload = (event: any) => {
  const file = event.raw || event.file
  const isMP4 = file.type === 'video/mp4'
  if (!isMP4) {
    msgShow('只能上传 MP4 格式的视频文件！', 'warning')
    return false
  }
  uploadDialog.value.form.file = file
  // 将文件名（不含扩展名）设置为视频标题
  uploadDialog.value.form.title = getFileNameWithoutExtension(file.name)
  return false // 阻止自动上传
}
const upload = ref<UploadInstance>()

const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}

// 分片上传视频
const uploadByChunks = async (file: File) => {
  try {
    // 基本配置
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1IiwiZXhwIjoyMzQ2NzYwMDY3fQ.APtOpmmdNvGGoBhuTdwFuSijPphU4WJVyt5BoIZF6tA';
    const baseUrl = 'https://m1.swufe-online.com/video';
    const chunkSize = 5 * 1024 * 1024;  // 5MB一片
    const chunks = Math.ceil(file.size / chunkSize);
    const fileId = generateGuid(); // 使用GUID作为文件标识
    const title = uploadDialog.value.form.title;
    const fileExtension = getFileExtension(file.name);
    const newFileName = `${fileId}.${fileExtension}`; // 使用GUID作为新文件名
    
    uploadDialog.value.uploading = true;
    uploadDialog.value.progress = 0;
    
    try {
      // 1. 初始化上传
      const initRes = await axios.post(`${baseUrl}/init?token=${token}`, {
        filename: newFileName,
        total_chunks: chunks,
        file_id: fileId,
        title: title
      });
      
      if (initRes.data.error) {
        throw new Error(initRes.data.error);
      }
      
      // 2. 分片上传
      for (let i = 0; i < chunks; i++) {
        const start = i * chunkSize;
        const end = Math.min(file.size, start + chunkSize);
        const chunk = file.slice(start, end);
        
        const formData = new FormData();
        formData.append('file', chunk);
        formData.append('file_id', fileId);
        formData.append('chunk_index', i.toString());
        formData.append('filename', newFileName);
        
        const chunkRes = await axios.post(`${baseUrl}/chunk?token=${token}`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });
        
        if (chunkRes.data.error) {
          throw new Error(chunkRes.data.error);
        }
        
        // 更新进度
        uploadDialog.value.progress = Math.floor(((i + 1) / chunks) * 100);
      }
      
      // 3. 完成上传
      const completeRes = await axios.post(`${baseUrl}/complete?token=${token}`, {
        file_id: fileId,
        title: title,
        filename: newFileName
      });
      
      if (completeRes.data.error) {
        throw new Error(completeRes.data.error);
      }

      const now = new Date();
      const pad = (n: number, width = 2) => String(n).padStart(width, '0');
      const date = `${now.getFullYear()}-${pad(now.getMonth() + 1)}-${pad(now.getDate())}`;
      const time = `${pad(now.getHours())}:${pad(now.getMinutes())}:${pad(now.getSeconds())}`;
      const milliseconds = pad(now.getMilliseconds(), 3);
      const microseconds = milliseconds + '000';

      insertVideoList({
        "title": title,
        "mp4_filename": completeRes.data.path.replace('http://m1.swufe-online.com/',''),
        "duration": completeRes.data.duration,
        "duration_s": completeRes.data.duration_s,
        "size": completeRes.data.size,
        "size_s": completeRes.data.size_s,
        "width": completeRes.data.width,
        "height": completeRes.data.height,
        "date_created": `${date} ${time}.${microseconds}`,
        "url": 'http://m1.swufe-online.com/',
        "image": completeRes.data.frame_path.replace('http://m1.swufe-online.com/',''),
        "course_jc_id": uploadDialog.value.form.course_id
      })
      
      msgShow('上传成功', 'success');
      uploadDialog.value.visible = false;
      reloadData();
      
    } catch (error: any) {
      console.error('分片上传错误:', error);
      msgShow(error.message || '上传失败', 'error');
    } finally {
      uploadDialog.value.uploading = false;
    }
  } catch (error: any) {
    console.error('分片上传错误:', error);
    msgShow(error.message || '上传失败', 'error');
    uploadDialog.value.uploading = false;
  }
}

// 提交上传
const submitUpload = async () => {
  if (!uploadDialog.value.form.title) {
    msgShow('请输入视频标题', 'warning')
    return
  }
  if (!uploadDialog.value.form.file) {
    msgShow('请选择要上传的视频文件', 'warning')
    return
  }
  if (!uploadDialog.value.form.course_id) {
    msgShow('请选择所属课程', 'warning')
    return
  }
  
  const file = uploadDialog.value.form.file
  return uploadByChunks(file)
}

// 处理预览
const handlePreview = (row: VideoItem) => {
  previewDialog.value.url = `${row.url}/${row.mp4_filename}`
  previewDialog.value.visible = true
}

const handleIamgePreview = (row: VideoItem) => {
  previewImageDialog.value.url = `${row.url}/${row.image}`
  previewImageDialog.value.visible = true
}
     


onMounted(() => {
  reloadData()
})
</script>

<style scoped>
.page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  padding: 10px;
  display: flex;
  gap: 10px;
  align-items: center;
}

.body {
  flex: 1;
  padding: 0 10px 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.upload-file-info {
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
}

.upload-status {
  margin-top: 10px;
  color: #409eff;
  font-size: 14px;
  text-align: center;
  margin-bottom: 12px;
}
</style> 