<template>
  <div style="padding: 20px;background-color: #e8e8e8;">
    <div style="display: flex;gap: 30px;align-items: start;">
      <div style="width: 70%;">
        <h3>统计总览</h3>
        <div class="tjcard">
          <div class="tjbg"></div>
          <div class="tjdataitem">
            <div class="tjdatanumber">51</div>
            <div class="tjdatatips">题库数量</div>
          </div>
          <div class="tjdataitem">
            <div class="tjdatanumber">{{mgrdata.stzs}}</div>
            <div class="tjdatatips">试题总数</div>
          </div>
          <div class="tjdataitem">
            <div class="tjdatanumber">{{mgrdata.kczs}}</div>
            <div class="tjdatatips">课程总数</div>
          </div>
          <div class="tjdataitem">
            <div class="tjdatanumber">{{mgrdata.dzs}}</div>
            <div class="tjdatatips">课件总数</div>
          </div>
          <div class="tjdataitem">
            <div class="tjdatanumber">{{ mgrdata.xss }}</div>
            <div class="tjdatatips">学生总数</div>
          </div>
          <div class="tjdataitem">
            <div class="tjdatanumber">{{ mgrdata.spzsc }}</div>
            <div class="tjdatatips">视频时长</div>
          </div>
        </div>
        <h3 style="margin-top: 15px;">快捷入口</h3>
        <div class="kjrk">
          <div v-if="menulist.length === 0"
            style="color: #909399;width: 100%;height: 100%;display: flex;justify-content: center;align-items: center;">
            暂无快捷入口 ~</div>
          <div v-else style="display: flex;gap: 0px;">
            <div class="menu_item" v-for="(item, index) in menulist" :key="index" @click="toMenu(item)">
              <div class="menu_icon">
                <i class=" el-icon" v-if="item.icon">
                  <component class="icons el-icon" :is="item.icon"/>
                </i>
                 <i class=" el-icon" v-else>
                  <component class="icons el-icon" is="Pointer"/>
                </i>
              </div>
              <div class="menu_text">{{ item.title }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="ggcard" style="width: 27%;">
        <h3>公告</h3>
        <div class="ggcarddata">

          <span style="color: #909399;">暂无公告</span>

        </div>
      </div>
    </div>
    <div style="display: flex;gap: 30px;margin-top: 50px;">
      <el-card shadow="hover" style="flex: 1;">
        <h3>进行中的课程</h3>
        <el-table :data="[]" style="width: 100%;margin-top: 10px;" height="350" stripe size="small">
          <el-table-column prop="date" label="课程名称">
          </el-table-column>
          <el-table-column prop="date" label="学习人数">
          </el-table-column>
          <el-table-column prop="date" label="学完人数">
          </el-table-column>
        </el-table>
      </el-card>
      <el-card shadow="hover" style="flex: 1;" class="ggcard">
        <h3>学习总览</h3>
        <div
          style="height: 350px;font-size: 12px;display: flex;justify-content: center;align-items: center;color: #909399;">
          暂无数据
        </div>
      </el-card>
    </div>
    <div style="display: flex;gap: 30px;margin-top: 20px;">

      <el-card shadow="hover" style="flex: 1;">
        <h3>用户总览</h3>
        <div
          style="height: 350px;font-size: 12px;display: flex;justify-content: center;align-items: center;color: #909399;">
          暂无数据
        </div>
      </el-card>
    </div>
  </div>
</template>
<script lang="ts" setup>

import { onMounted, defineAsyncComponent, ref } from "vue";
import { storeToRefs } from "pinia"
import { useUserStore } from "@/store/modules/user";
import { getIndexData } from '@/api/user'
import router from "@/router";
// 默认加载的组件名
const compName = ref('homeOther')
const useStore = useUserStore()
const { user_type_id } = storeToRefs(useStore)
const menulist:any = ref([])
const mgrdata:any=ref({
  kczs:0,
  stzs:0,
  xss:0,
  dzs:0,
  spzsc:0,

})
onMounted(() => {
  console.log(`output->首页`, '首页')
  GetData()

});
const GetData = () => {
  getIndexData().then((res: any) => {
    console.log('首页数据', res);
    menulist.value = res.data.menulist
    mgrdata.value = res.data.mgrdata[0];
  }).catch((err: any) => {
    console.error('获取首页数据失败', err);
  });
}
const toMenu = (item: any) => {
  if (item.path) {
   router.push({ path: item.path });
  }
}
</script>
<style>
.el-card__body {
  background-image: url('@/assets/dot-grid.png');
}

.el-table {
  background: transparent;
}
</style>
<style lang="scss" scoped>
.tjcard {

  padding: 1rem;
  background-color: #e5e7eb;
  box-shadow:
    -10px -10px 20px white,
    10px 10px 20px rgb(153, 161, 175),
    inset -10px -10px 20px rgb(209, 213, 220);
  border-radius: 20px;
 color: #374151;
  position: relative;
  /*background-color: #0a84ff;*/
  margin-top: 10px;
  height: 110px;
  display: flex;
  justify-content: space-between;
  padding: 0px 30px;
}

.tjbg {
  /*background-image: url('./images/p6.png');*/
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  
}

.tjdataitem {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0px 20px;
  gap: 5px;
}

.tjdatanumber {
  font-size: 30px;
  color: #0a84ff;
  font-weight: bold;
}

.tjdatatips {
  font-size: 14px;
 /* color: #afd5fa;*/
  color: #333;
}

.kjrk {
  background-image: url('@/assets/p6.png');
  width: 100%;
  height: 160px;
  padding: 15px;
  margin-top: 15px;
  display: flex;
  justify-content: start;
  align-items: center;

}
.menu_icon{
  font-size: 30px;
  background-color: #0a84ff;
  width: 70px;
  height: 70px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  border-radius: 5px;
;
}
.menu_item{
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
   padding: 10px;
   border-bottom: 3px solid transparent;
}
.menu_text{
  font-size: 14px;
  color: #333;
  width: 60px;
  text-align: center;
}
.menu_item:hover {
  cursor: pointer;
  color: #0a84ff;
  background-color: #DCDFE6;
  border-bottom: 3px solid #0a84ff;
  padding: 10px;
}
.ggcarddata {
  border-top: 1px solid #DCDFE6;
  border-bottom: 1px solid #DCDFE6;
  /*background-image: url('./images/dot-grid.png');*/
  width: 100%;
  height: 328px;
  border-radius: 5px;
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

</style>