
<template>

      <router-view></router-view>
   
</template>
<script lang="ts">
import { computed, defineComponent,toRaw } from 'vue'
import { usePermissionStore } from '@/store/modules/permission'

//import { store } from '@/store/index'

export default defineComponent({
  components: {
    
  },
 setup(){
   const permissionStore=usePermissionStore()
 const permission_routes =toRaw(permissionStore.accessRoutes)
 console.log(permission_routes)
 return{
  permission_routes
 }
 }
})
 
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
   color: #2c3e50;
   height:100%;
}
body{
 margin: 0px;
 height: 100vh;
}
.nvbbar{
 width: 100%;
 height: 50px;
 box-shadow: 0 1px 4px rgb(0 21 41 / 28%);
}
.mb-2{
 height:50px;
 font-family: "Helvetica";
 line-height:50px;
}
</style>
