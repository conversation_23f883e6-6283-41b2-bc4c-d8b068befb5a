{
  "compilerOptions": {
   "baseUrl": ".",
    "target": "esnext",
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    // "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "lib": ["esnext", "dom"],
    "skipLibCheck": true,
    "paths": {
     "@/*": [ "src/*" ],
    }
    //"types": ["element-plus/global"]
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/icons/index.Ts","./src/*.ts","./*.d.ts",
  "./src/*.vue",
  "./src/*.ext",],
 // "references": [{ "path": "./tsconfig.node.json" }],
 
}
