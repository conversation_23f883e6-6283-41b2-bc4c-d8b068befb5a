/*
 * @Author: GeekQiaQia
 * @Date: 2022-02-18 16:13:43
 * @LastEditTime: 2022-06-05 20:17:00
 * @LastEditors: GeekQiaQia
 * @Description:
 * @FilePath: /vue3.0-template-admin/vite.config.ts
 */
import { resolve } from 'path'
import { ConfigEnv, loadEnv, UserConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { tr } from 'element-plus/es/locale'
import packageJson from "./package.json"
// import vueJsx from '@vitejs/plugin-vue-jsx' 

// const pathSrc = path.resolve(__dirname, 'src')

const CWD = process.cwd()

// https://cn.vitejs.dev/config/
export default ({ mode }: ConfigEnv): UserConfig => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD)

  return {
    base: '/vue/xczxmgr/', // 设开发或生产环境服务的 公共基础路径
    define: {
      // 类型： Record<string, string> 定义全局变量替换方式。每项在开发时会被定义为全局变量，而在构建时则是静态替换。
      'process.platform': null,
      'process.version': null,
      'import.meta.env.PACKAGE_SECRET': JSON.stringify(packageJson.secret),
    },
    resolve: {
      // 类型：Record<string, string> | Array<{ find: string | RegExp, replacement: string }> 将会被传递到 @rollup/plugin-alias 作为它的 entries。
      alias: {
        '~': resolve(__dirname, './'),
        '@': resolve(__dirname, 'src')
      },
      extensions: ['.js', '.ts', '.jsx', '.tsx', '.json', '.vue', '.mjs'] // 类型： string[] 导入时想要省略的扩展名列表。
    },
    css: {
      preprocessorOptions: {
        scss: {
          // api: 'modern-compiler',
          silenceDeprecations: ['legacy-js-api', 'import', 'global-builtin'],
          additionalData: `@use "@/styles/element/index.scss" as *;`
         
        }
      }
    },
    plugins: [
      // PkgConfig(),
      // OptimizationPersist(),
      vue(),
      // vueJsx()

    ],
    server: {
      hmr: { overlay: false }, // 禁用或配置 HMR 连接 设置 server.hmr.overlay 为 false 可以禁用服务器错误遮罩层

      // 服务配置
      port: 3001, // 类型： number 指定服务器端口;
      open: true, // 类型： boolean | string在服务器启动时自动在浏览器中打开应用程序；
      cors: true, // 类型： boolean | CorsOptions 为开发服务器配置 CORS。默认启用并允许任何源
    },
    // 打包配置
    build: {
      target: 'modules',
      outDir: 'P:/webapps/vue/xczxmgr', //指定输出路径
      assetsDir: 'static', // 指定生成静态资源的存放路径
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_debugger: true,
          drop_console: true
        }
      }
    },

    // https://www.vitejs.net/config/#build-commonjsoptions
  }
}
